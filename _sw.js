const APP_NAME = 'BetFugu'
const APP_VER = '107'
const CACHE_NAME = APP_NAME + '-' + APP_VER

const REQUIRED_FILES = []

self.addEventListener('install', function (event) {
    event.waitUntil(
        caches
            .open(CACHE_NAME)
            .then(function (cache) {
                return cache.addAll(REQUIRED_FILES)
            })
            .catch(function (error) {})
            .then(function () {
                return self.skipWaiting()
            })
            .then(function () {})
    )
})

self.addEventListener('fetch', function (event) {
    // 检查是否为开发环境（localhost或127.0.0.1）
    if (
        self.location.hostname === 'localhost' ||
        self.location.hostname === '127.0.0.1' ||
        self.location.hostname === '6005b07b87a4.ngrok-free.app'
    ) {
        // 开发环境下不使用缓存，直接从网络获取资源
        event.respondWith(fetch(event.request))
        return
    }

    event.respondWith(
        caches.match(event.request).then(function (response) {
            //console.log((!!response) + " " + event.request.url);
            if (response) {
                if (response.status >= 400) caches.open(CACHE_NAME).then((cache) => cache.delete(event.request))
                else return response
            }

            return fetch(event.request).then(function (r) {
                if (!r.ok || r.status >= 400) return r
                const req = event.request
                if (req.method.toUpperCase() !== 'GET') return r
                if (req.url.toLowerCase().indexOf('/api/') >= 0) return r
                if (req.url.toLowerCase().indexOf('/drama/') >= 0) return r

                // 排除特定文件不缓存
                const url = req.url.toLowerCase()
                // 移除查询参数后再判断文件类型
                const cleanUrl = url.split('?')[0].split('#')[0]
                if (cleanUrl.endsWith('version.json') || cleanUrl.endsWith('.html') || cleanUrl.endsWith('_manifest.json')) {
                    return r
                }

                const cacheControl = (r.headers.get('Cache-Control') || '').toLowerCase()
                if (cacheControl.indexOf('no-cache') > 0) return r
                const ext = req.url.replaceAll(/\?.*/g, '').replaceAll(/\&.*/g, '').replaceAll(/\#.*/g, '').replaceAll(/.*\//g, '').split('.')[1]
                if (!ext) return r
                caches.open(CACHE_NAME).then(function (cache) {
                    cache.put(req, r)
                })
                return r.clone()
            })
        })
    )
})

self.addEventListener('activate', function (event) {
    event.waitUntil(self.clients.claim())
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames
                    .filter((cacheName) => cacheName.startsWith(APP_NAME + '-'))
                    .filter((cacheName) => cacheName !== CACHE_NAME)
                    .map((cacheName) => caches.delete(cacheName))
            )
        })
    )
})

self.addEventListener('push', function (event) {
    try {
        const data = event.data.json()
        const options = {
            body: data.body,
            icon: data.icon,
            image: data.image,
            badge: data.badge,
            actions: data.actions || [],
            data: data.data || {},
            requireInteraction: true,
            vibrate: [200, 100, 200],
            silent: false,
            renotify: true,
            tag: 'notification-' + Date.now(),
            timestamp: Date.now(),
        }
        if (data.image) event.waitUntil(Promise.all([fetch(data.image), self.registration.showNotification(data.title, options)]))
        else event.waitUntil(self.registration.showNotification(data.title, options))
    } catch (error) {
        console.error('Push event failed:', error)
    }
})

class NotificationHandler {
    constructor(maxRetries = 3) {
        this.maxRetries = maxRetries
    }

    async handleClick(event) {
        let retries = 0
        const tryOpenWindow = async () => {
            try {
                const client = await self.clients.openWindow('/')
                if (client) {
                    await client.focus()
                    return true
                }
            } catch (error) {
                if (retries < this.maxRetries) {
                    retries++
                    await new Promise((resolve) => setTimeout(resolve, 1000 * retries))
                    return tryOpenWindow()
                }
                throw error
            }
            return false
        }

        const promiseChain = (async () => {
            event.notification.close()

            const clients = await self.clients.matchAll({
                type: 'window',
                includeUncontrolled: true,
            })

            for (const client of clients) {
                if (/*client.url === '/' &&*/ 'focus' in client) {
                    await client.focus()
                    return
                }
            }

            if (self.clients.openWindow) {
                await tryOpenWindow()
            }
        })()

        event.waitUntil(promiseChain)
    }
}

const handler = new NotificationHandler()
self.addEventListener('notificationclick', (event) => {
    handler.handleClick(event)
})
