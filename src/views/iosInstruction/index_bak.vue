<template>
    <div>
        <Header />
        <div class="ios-wrap">
            <div class="ios-content" v-for="(item, index) in tipConfig" :key="index">
                <div class="ios-title">
                    <span class="title-index">{{ index + 1 }}</span
                    >{{ $t(item.title) }}
                </div>
                <div class="ios-text">{{ $t(item.text) }}</div>
            </div>
            <div class="ios-video">
                <video
                    ref="videoDom"
                    :autoplay="true"
                    :muted="true"
                    :loop="true"
                    src="https://betfugu.com/static/video/yindao.mp4"
                    x5-video-player-type="h5"
                    :x5-video-player-fullscreen="false"
                    controls360="no"
                    raw-controls
                    :webkit-playsinline="true"
                    :x5-playsinline="true"
                    :playsinline="true"
                    :fullscreen="false"
                ></video>
            </div>
        </div>
    </div>
</template>
<script setup lang="tsx">
import Header from '@/components/Header.vue'
import { onActivated, ref } from 'vue'

const videoDom = ref(null)

const tipConfig = [
    {
        title: 'Open in Safari',
        text: 'If you are in any other browser, open this page with Safari browser.',
    },
    {
        title: 'Click "share"',
        text: 'Tap on the "Share" icon in the menu at the bottom of the screen. A dialog box will open.',
    },
    {
        title: 'Click "Add to Home Screen"',
        text: 'Tap on the item "Add to Home Screen", and click"Ready"',
    },
]
onActivated(() => {
    videoDom.value.currentTime = 0
    videoDom.value?.play()
})
</script>
<style lang="scss" scoped>
.ios-wrap {
    padding: 50px 0;
    height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height)) !important;
    overflow-y: auto;
}
.ios-content {
    padding: 0 30px 0 16px;
    font-family: MicrosoftYaHei;
    font-size: 26px;
    line-height: 32px;
    color: #b8caed;

    .ios-title {
        font-size: 28px;
        font-weight: bold;
        color: #fff;

        .title-index {
            display: inline-block;
            margin-right: 24px;
            width: 44px;
            height: 44px;
            line-height: 44px;
            text-align: center;
            border-radius: 50%;
            background-image: linear-gradient(30deg, #0286f1 0%, #0761cf 100%);
        }
    }
    .ios-text {
        margin: 28px 0 45px 0;
        padding-left: 63px;
        line-height: 40px;
    }
    .ios-video {
        width: 100%;
        height: 617px;

        .video {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
