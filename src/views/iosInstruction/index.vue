<template>
    <van-popup class="down-pop" v-model:show="show" position="bottom" close-on-popstate round :z-index="3001" @closed="handleClose" destroy-on-close>
        <div class="wrap">
            <div class="header-title">
                {{ $t('ios_add_to_desktop') }}
                <div class="close" @click="handleClose"></div>
            </div>
            <div class="ios-wrap">
                <div class="ios-content" v-for="(item, index) in tipConfig" :key="index">
                    <div class="ios-title">
                        <span class="title-index">{{ index + 1 }}</span
                        >{{ $t(item.title) }}
                    </div>
                    <div class="ios-text">{{ $t(item.text) }}</div>
                </div>
                <div class="ios-video">
                    <video
                        ref="videoDom"
                        :autoplay="true"
                        :muted="true"
                        :loop="true"
                        src="https://betfugu.com/static/video/yindao_smaller.mp4"
                        x5-video-player-type="h5"
                        :x5-video-player-fullscreen="false"
                        controls360="no"
                        raw-controls
                        :webkit-playsinline="true"
                        :x5-playsinline="true"
                        :playsinline="true"
                        :fullscreen="false"
                    ></video>
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { onActivated, ref } from 'vue'

const show = defineModel()
const videoDom = ref(null)

const tipConfig = [
    {
        title: 'Open_in_Safari',
        text: 'open_page_safari',
    },
    {
        title: 'Click_share',
        text: 'Tap_on_the_Share_i',
    },
    {
        title: 'Click_Add_to_Home_S',
        text: 'Tap_on_the_item_Add',
    },
]
const handleClose = () => {
    show.value = false
}
onActivated(() => {
    if (videoDom.value) {
        videoDom.value.currentTime = 0
        videoDom.value?.play()
    }
})
</script>
<style lang="scss" scoped>
.down-pop {
    margin-left: 20px;
    width: 710px;
    height: 70%;

    .wrap {
        height: 100%;
        // background: var(--main-bg);
        background-color: #2a2d3d;
    }
    .header-title {
        position: relative;
        height: 100px;
        line-height: 100px;
        text-align: center;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;

        .close {
            position: absolute;
            right: 10px;
            top: 50%;
            width: 90px;
            height: 90px;

            background: url('@/assets/img/new-home/close.png') no-repeat center center;
            background-size: 30px 30px;

            transform: translateY(-50%);
        }

        // &::after {
        //     position: absolute;
        //     left: 0;
        //     bottom: 2px;
        //     content: '';
        //     width: 100%;
        //     height: 1px;
        //     background: #fff;
        // }
    }

    .ios-wrap {
        overflow-y: auto;
        height: calc(100% - 100px);
    }
    .ios-content {
        padding: 0 30px 0 16px;
        font-family: MicrosoftYaHei;
        font-size: 26px;
        line-height: 32px;
        color: #b8caed;

        .ios-title {
            font-size: 28px;
            font-weight: bold;
            color: #fff;

            .title-index {
                display: inline-block;
                margin-right: 24px;
                width: 44px;
                height: 44px;
                line-height: 44px;
                text-align: center;
                border-radius: 50%;
                background-image: linear-gradient(30deg, #0286f1 0%, #0761cf 100%);
            }
        }
        .ios-text {
            margin: 20px 0 25px 0;
            padding-left: 63px;
            line-height: 40px;
        }
        .ios-video {
            width: 100%;
            height: 617px;

            .video {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
