<template>
    <div class="report">
        <Header></Header>
        <div class="report-content">
            <div class="reason">
                <p class="reason-title">{{ $t('Reason') }}</p>
                <div class="reason-check">
                    <van-checkbox-group v-model="checked" shape="square">
                        <div class="check-item" v-for="item in reportOpt" :key="item.type">
                            <p>{{ $t(item.text) }}</p>
                            <van-checkbox :name="item.type" shape="square">
                                <template #icon="props">
                                    <div :class="['check-box', { checked: props.checked }]">
                                        <img v-if="props.checked" src="@/assets/img/report/checked.png" />
                                    </div>
                                </template>
                            </van-checkbox>
                        </div>
                    </van-checkbox-group>
                </div>
            </div>
            <div class="description">
                <p class="reason-title">
                    {{ $t('Describtion') }}<span class="text-[#7e776e]">({{ $t('Optional') }})</span>
                </p>
                <div class="description-content">
                    <van-field
                        class="description-input"
                        v-model="message"
                        rows="1"
                        autosize
                        type="textarea"
                        :placeholder="$t('Provide_more_detail')"
                    />
                </div>
                <div class="submit-btn">
                    <Button type="sign" :disabled="!checked.length" @confirm="handleSubmit">{{ $t('Submit') }}</Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="report">
import Header from '@/components/Header.vue'
import Button from '@/components/Button.vue'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const i18n = useI18n()
const checked = ref([])
const message = ref('')

const reportOpt = ref([
    {
        type: 1,
        text: 'Politics_Related',
        checked: false,
    },
    {
        type: 2,
        text: 'Sexually_Explicit',
        checked: false,
    },
    {
        type: 3,
        text: 'Scam',
        checked: false,
    },
    {
        type: 4,
        text: 'Insult',
        checked: false,
    },
    {
        type: 5,
        text: 'Other',
        checked: false,
    },
])
const handleSubmit = () => {
    setTimeout(() => {
        message.value = ''
        checked.value = []
        showToast(i18n.t('Feedback_successful'))
        router.back()
    }, 1000)
}
</script>

<style scoped lang="scss">
.report {
    .report-content {
        padding: 16px 25px 0;
        height: calc(var(--vh, 1vh) * 100 - var(--header-height)) !important;
        overflow: auto;

        .reason-title {
            line-height: 67px;
            font-size: 36px;
            color: #fff;
        }
        .reason-check {
            background-color: #26262b;
            border-radius: 20px;
            overflow: hidden;
        }
        .check-item {
            @apply flex justify-between items-center;
            padding: 0 34px;
            height: 100px;
            background: #26262b;
            font-size: 30px;

            &:nth-child(even) {
                background: #383638;
            }

            .check-box {
                width: 24px;
                height: 24px;
                border: solid 2px #7e776e;
                border-radius: 50%;

                &.checked {
                    border-color: #fff;
                }
            }
        }
        .description {
            margin-top: 69px;

            .description-content {
                height: 420px;
                padding: 20px;
                background-color: #3a3533;
                border-radius: 20px;
            }
            .description-input {
                padding: 30px;
                height: 260px;
                background-color: #0f1421;
                border-radius: 10px;

                :deep(.van-field__control) {
                    font-family: MyriadPro-Regular;
                    font-size: 28px;
                    color: #7e776e;
                    &::placeholder {
                        font-family: MyriadPro-Regular;
                        color: #7e776e;
                    }
                }
            }
        }
        .submit-btn {
            margin-top: 34px;
            text-align: center;

            button {
                width: 352px !important;
                height: 72px !important;
            }
        }
    }
}
</style>
