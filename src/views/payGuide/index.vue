<template>
    <div class="pay-guide">
        <Header></Header>
        <div class="pay-content">
            <img class="pay-title" src="@/assets/img/pay-guide/biaoti.png" />
            <p class="title-txt"><PERSON>port<PERSON> ang G<PERSON>ash, Maya, <PERSON><PERSON>, <PERSON><PERSON> at iba pang paraan ng pagbabayad.</p>
            <p class="title-txt">
                Mangyaring magbayad ng <span>{{ route.query.coin }} PHP</span> ayon sa halagang ipinapakita sa pahina, kung hindi hindi ito maitatala.
            </p>
            <p class="title-txt">Mangyaring siguraduhing magbayad sa loob ng 5 minuto.</p>
            <div class="contenx-text">
                <p class="text-lefts">1.</p>
                <p class="text-rights">I-save ang QR code na ito para sa pag-recharge</p>
            </div>
            <div class="qr-area">
                <div ref="qrcodeDom" class="qr-code">
                    <div class="qr-info">
                        <div class="coin-num">{{ route.query.coin }}PHP</div>
                        <div class="coin-time">{{ route.query.time }}</div>
                    </div>
                    <VueQrcode class="code" :value="route.query.u" type="image/png" @change="onDataUrlChange" />
                </div>
                <div class="qr-download" @click="downloadQr">save</div>
            </div>
            <div class="contenx-text">
                <p class="text-lefts">2.</p>
                <p class="text-rights">Buksan ang GCash app, pindutin ang QR button, at i-upload ang na-save na QR code para sa pag-recharge</p>
            </div>
            <div class="flex justify-end">
                <img class="content-pic2" src="@/assets/img/pay-guide/p2.png" />
            </div>

            <div class="contenx-text">
                <p class="text-lefts">3.</p>
                <p class="text-rights">
                    Ipasok ang tamang halaga ng recharge, pindutin ang payment button, at tapusin ang pagbabayad. Ang QR code ay na-save na.
                </p>
            </div>
            <img class="content-pic3" src="@/assets/img/pay-guide/p3.png" />
        </div>
    </div>
</template>
<script setup lang="ts">
import VueQrcode from 'vue-qrcode'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import html2canvas from 'html2canvas'

const route = useRoute()

const qrCodeUrl = ref('')
const qrcodeDom = ref(null)

const onDataUrlChange = (dataUrl) => {
    qrCodeUrl.value = dataUrl
}
const downloadQr = () => {
    html2canvas(qrcodeDom.value).then(function (canvas) {
        const picUrl = canvas.toDataURL('image/png')
        // 将 base64 字符串转换为 Blob 对象
        var arr = picUrl.split(',')
        var mime = arr[0].match(/:(.*?);/)[1]
        var bstr = atob(arr[1])
        var n = bstr.length
        var u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        var blob = new Blob([u8arr], { type: mime })

        // 创建一个 URL 对象
        var url = URL.createObjectURL(blob)
        // 创建一个 a 元素
        const link = document.createElement('a')

        // 设置文件链接地址
        link.href = url
        // 设置下载的文件名
        link.download = 'qrcode.png'

        // 模拟点击事件来触发下载
        document.body.appendChild(link)
        link.click()
        window.URL.revokeObjectURL(url)

        // 然后移除这个元素
        document.body.removeChild(link)
    })
}
</script>
<style lang="scss" scoped>
.pay-guide {
    height: calc(var(--vh, 1vh) * 100 - var(--safe-height)) !important;
    background: #272f61;

    .pay-content {
        height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height)) !important;
        overflow-y: scroll;
    }
    .pay-title {
        margin: 30px auto;
        width: 418px;
        height: 100px;
    }
    .title-txt {
        padding: 0 71px 0 42px;
        font-family: MicrosoftYaHei;
        font-size: 24px;
        line-height: 37px;
        color: #fefeff;

        span {
            color: #ffff00;
        }
    }
    .contenx-text {
        margin: 20px 0;
        padding: 0 71px 0 42px;
        display: flex;
        font-family: MicrosoftYaHei;
        font-size: 28px;
        font-weight: bold;
        line-height: 37px;
        color: #fefeff;
        .text-lefts {
            width: 30px;
        }
        .text-rights {
            flex: 1;
        }
    }

    .content-pic3 {
        margin: 0 auto;
    }
    .content-pic2 {
        margin-bottom: 20px;
        width: 608px;
    }
    .content-pic3 {
        width: 476px;
    }
    .qr-area {
        margin-bottom: 50px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .qr-code {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        margin: 15px 0 40px;
        width: 248px;
        height: 278px;
        background: #fff;

        .qr-info {
            margin-bottom: 5px;
            width: 100%;
            text-align: center;
            font-size: 13px;
            background: #fff;

            .coin-num {
                font-size: 22px;
                font-weight: bold;
            }
        }
        .code {
            width: 200px;
            height: 210px;
        }
    }
    .qr-download {
        width: 150px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 30px;
        font-family: MicrosoftYaHei;
        font-size: 28px;
        font-weight: bold;
        color: #fefeff;
        background: #d06342;
    }
}
</style>
