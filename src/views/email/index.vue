<template>
    <div class="email">
        <Header />
        <div class="email-content">
            <div class="email-title">绑定邮箱账号</div>
            <div class="wrap">
                <van-field v-model="bindParams.mail" placeholder="请输入邮箱账号" />
            </div>
            <div class="wrap">
                <van-field v-model="bindParams.code" placeholder="请输入邮箱验证码" :maxlength="6" />
            </div>
            <div class="email-btn">
                <div v-if="!codeOpt.isSend" :class="['btn-item', { 'opacity-40': !bindParams.mail }]">
                    <Button type="sign" @click="sendEmailCode">发送</Button>
                </div>
                <div v-else class="btn-item">
                    <Button type="sign">Resend({{ codeOpt.seconds }})</Button>
                </div>
                <div class="btn-item" @click="handleBind">
                    <Button type="sign">绑定</Button>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import Header from '@/components/Header.vue'
import Button from '@/components/Button.vue'
import { useCutdown } from '@/hooks/useCutdown'
import * as service from '@/api'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'

const router = useRouter()
const store = useBaseStore()

const { codeOpt, doCutDown } = useCutdown()
const bindParams = reactive({
    mail: '',
    code: '',
})

const sendEmailCode = async () => {
    if (codeOpt.isSend) {
        return
    }
    if (!/^([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})$/.test(bindParams.mail)) {
        return showToast('Please enter a valid e-mail address format')
    }
    try {
        const res = await service.sendEmailCode(bindParams.mail)
        if (res.code === 200) {
            codeOpt.isSend = true
            doCutDown()
            showToast('Verification_code_ha')
        }
    } catch (e) {
        console.log(e)
    }
}
const handleBind = async () => {
    if (!/^([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})$/.test(bindParams.mail)) {
        return showToast('Please enter a valid e-mail address format')
    }

    if (!/\d{4}/.test(bindParams.code)) {
        return showToast('Incorrect_verification')
    }
    try {
        const res = await service.bindEmail({
            ...bindParams,
        })
        if (res.code === 200) {
            // res.data
            store.$patch((state) => {
                state.userInfo = { ...state.userInfo, linkmail: res.data }
            })
            showToast('Binding successful')
            router.back()
        }
    } catch (e) {
        console.log(e)
    }
}
</script>
<style lang="scss" scoped>
.email {
    .email-content {
        padding: 50px 30px 0;
        height: calc(var(--vh, 1vh) * 100 - var(--header-height)) !important;
        overflow: auto;

        .email-title {
            margin-bottom: 120px;
            color: #fff;
            font-size: 36px;
        }
        .wrap {
            padding: 0 30px;
        }
        :deep(.van-cell) {
            padding-top: 0;
            padding-bottom: 0;

            background: var(--main-bg);
            .van-field__control {
                color: #fff;
                height: 120px;
                line-height: 120px;
            }
            .van-cell__value {
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            }
        }
        .email-btn {
            margin-top: 50px;
            .btn-item {
                margin: 30px 0;
                text-align: center;
            }
        }
    }
}
</style>
