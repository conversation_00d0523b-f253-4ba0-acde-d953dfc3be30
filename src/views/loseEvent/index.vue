<template>
    <div class="recharge-rebate">
        <div class="tip" v-html="$t('Loss_wallet_compensa_1')"></div>
        <div v-if="info.status == 1" class="recharge-cutdown">
            <van-count-down :time="time">
                <template #default="timeData">
                    <div class="clock">
                        <span class="block">{{ timeData.days }}</span>
                        <span class="colon">day :</span>
                        <span class="block">{{ timeData.hours }}</span>
                        <span class="colon">h :</span>
                        <span class="block">{{ timeData.minutes }}</span>
                        <span class="colon">m</span>
                    </div>
                </template>
            </van-count-down>
        </div>
        <div v-if="tableData.length" class="recharge-table">
            <div class="recharge-tr">
                <div class="recharge-td">{{ $t('Loss_wallet_compensa_3') }}</div>
                <div class="recharge-td">{{ $t('Loss_wallet_compensa_4') }}</div>
            </div>
            <template v-for="(item, index) in tableData" :key="index">
                <div class="recharge-tr" v-if="item.show">
                    <div class="recharge-td">{{ item.clientconditon }}</div>
                    <div class="recharge-td">
                        {{ item.clientdetail }}
                    </div>
                </div>
            </template>
        </div>
        <div class="tip2" v-html="$t('Loss_wallet_compensa_2')"></div>
        <div class="recharge-btn" @click="router.push('/wallets')">{{ $t('Recharge') }}</div>
    </div>
</template>
<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useBaseStore } from '@/stores'
import dayjs from 'dayjs'
import { Acticity } from '@/stores/types'

const router = useRouter()
const route = useRoute()
const store = useBaseStore()

const time = ref(0)

const info = ref<Partial<Acticity>>({})

const tableData = ref([])

const getInfo = async () => {
    try {
        const res = await store.getSignEvent({
            id: route.query.id as string,
        })
        if (res.code === 200) {
            info.value = res.acticity
            const { lostmoneyrebateConfg, user } = res.acticity
            const { endtime = 0 } = user || {}
            tableData.value = lostmoneyrebateConfg.context
            // @ts-ignore
            time.value = dayjs(endtime) - dayjs()
        }
    } catch (e) {
        console.log(e)
    }
}

onBeforeMount(() => {
    getInfo()
})
</script>
<style lang="scss" scoped>
.recharge-rebate {
    min-height: 100%;
    padding: 29px 15px 0;
    color: #7e776e;
    font-size: 24px;
    background: url('@/assets/img/earn-cash/gold_bg.png') no-repeat left bottom;
    background-size: 100% 168px;

    .tip {
        color: #fff;
        :deep(span) {
            color: #ff4455;
            font-size: 30px;
        }
    }
    .tip2 {
        margin-top: 30px;
        color: #ffa72a;
    }

    .recharge-cutdown {
        margin: 54px 0 34px;

        :deep(.van-count-down) {
            color: #ffc11a;
            font-size: 60px;
            font-weight: bold;
        }

        .clock {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;

            &::before {
                content: '';
                width: 68px;
                height: 68px;
                background: url('@/assets/img/events/clock.png') no-repeat;
                background-size: 100% 100%;
            }
            .block {
                margin-left: 13px;
            }
        }
    }
    .recharge-table {
        padding: 6px 20px;
        // height: 400px;
        background-image: linear-gradient(120deg, #b41ce0 0%, #8454f0 80%, #538bff 100%), linear-gradient(#3a3533, #3a3533);
        border-radius: 30px;

        .recharge-tr {
            display: flex;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);

            &:first-child {
                .recharge-td {
                    color: #acf0ff;
                }
            }
            &:last-child {
                border-bottom: 0;
            }

            .recharge-td {
                line-height: 84px;
                text-align: center;
                font-size: 30px;
                color: #ffffff;

                &:first-child {
                    width: 230px;
                }
                &:last-child {
                    flex: 1;
                }
            }
        }
    }
    .recharge-btn {
        margin: 74px auto 0;
        width: 480px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
        border-radius: 44px;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
}
</style>
