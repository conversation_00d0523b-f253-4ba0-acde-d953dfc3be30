<template>
    <div class="events">
        <Header :show-back="false" :show-right="false"> </Header>

        <div class="events-wrap">
            <template v-if="filterEvents.length">
                <div class="event-item" v-for="(item, index) in filterEvents" :key="index" @click="handleAction(item)">
                    <div class="img-wrap">
                        <img v-lazy="item.picture" />
                        <div class="img-des" v-html="$t(item?.name || '')"></div>
                        <!-- <span :class="['unred', { read: item.read }]"></span> -->
                    </div>
                    <div class="tip">
                        <div class="tip-left" v-html="$t(item?.title || '')"></div>
                        <div v-if="item.end" class="tip-time">
                            {{ $t('event_end_time', { last_time: dayjs(item.end).format('YYYY-MM-DD') }) }}
                        </div>
                    </div>
                </div>
            </template>
            <van-empty class="tip" v-else>{{ $t('No_more_content') }}</van-empty>
        </div>
        <BaseFooter :initTab="4" />
        <LuckySpin v-model="showLuckySpin" />
    </div>
</template>

<script setup lang="ts" name="events">
import { defineAsyncComponent, hydrateOnIdle } from 'vue'
import Header from '@/components/Header.vue'
import BaseFooter from '@/components/BaseFooter.vue'
import { getEvents } from '@/api/home'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import dayjs from 'dayjs'
import { checkLogin } from '@/utils'
import { useGame } from '@/hooks/useGame'
import EventBus from '@/utils/bus'

const router = useRouter()
const store = useBaseStore()
const { goGamePage } = useGame()

const showLuckySpin = ref(false)
const LuckySpin = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/views/luckyspin/index.vue'),
    delay: 2000,
})

const events = ref([])

const filterEvents = computed(() => {
    const now = dayjs()
    return events.value.filter((item) => {
        if (store.userInfo?.telephone && item.link === '/bindPhone') {
            return false
        }
        if (store.userInfo?.shareid && item.link === '/binInviter') return false
        return !item.end || now.isBefore(dayjs(item.end))
    })
})

const getList = async () => {
    getEvents().then((res) => {
        if (res.code === 200) {
            events.value = res.data
        }
    })
}
watch(
    () => store.wallet?.currency,
    (val, oldVal) => {
        if (val !== oldVal) {
            getList()
        }
    }
)
const handleAction = async (item) => {
    if (item.jump && item.jump !== 'url') {
        try {
            const logined = await checkLogin()
            if (!logined) {
                return
            }
        } catch (e) {
            return
        }
    }
    switch (item.jump) {
        case 'page':
            router.push({
                path: item.param,
            })
            break
        case 'activity':
            if (item.param === 'totalrechargetable') {
                showLuckySpin.value = true
            } else {
                EventBus.emit('activity', item)
            }
            break
        case 'game':
            goGamePage({ gametag: item.param })
            break
        case 'url':
            window.open(item.param)
            break
        default:
            break
    }
}

onActivated(() => {
    getList()
})
</script>

<style scoped lang="scss">
.events {
    :deep(.header) {
        height: 156px;
        padding-top: 56px;
    }
    .events-wrap {
        padding: 20px 15px;
        height: calc(var(--vh, 1vh) * 100 - var(--footer-height) - 156px - var(--safe-height));
        overflow-y: scroll;

        .event-item {
            @apply flex flex-col;
            margin-bottom: 20px;
            width: 720px;
            height: 288px;
            border-radius: 30px;
            overflow: hidden;
            font-family: MicrosoftYaHei;
            color: #fff;

            .img-wrap {
                position: relative;
                flex: 1;
                // height: 240px;
                img {
                    width: 100%;
                    min-height: 240px;
                    // height: 100%;
                    // object-fit: cover;
                }
                .img-des {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 55%;
                    height: 100%;
                    padding: 25px 30px;
                    font-size: 30px;
                    font-weight: bold;
                }
                .unred {
                    position: absolute;
                    top: 10px;
                    right: 20px;
                    width: 15px;
                    height: 15px;
                    border-radius: 50%;
                    background: #ee0a24;

                    &.read {
                        display: none;
                    }
                }
            }
            .tip {
                display: flex;
                align-items: center;
                padding: 0 20px;
                height: 48px;
                line-height: 48px;
                font-size: 22px;
                background-color: #2a2d3d;

                .tip-left {
                    flex: 1;
                }
                .tip-time {
                    color: #668292;
                }
            }
        }
    }
    .tip {
        font-size: 20px;
    }
}
</style>
