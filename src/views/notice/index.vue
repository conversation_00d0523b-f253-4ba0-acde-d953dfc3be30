<template>
    <div class="notice">
        <Header />
        <van-tabs class="notice-tab" v-model:active="active" lazy-render>
            <van-tab v-for="(item, index) in tabNav" :key="item.key">
                <template #title
                    ><van-badge :content="getCount(item.key)" :offset="offset" :show-zero="false">{{ $t(item.name) }}</van-badge></template
                >
                <div class="scroll-wrap">
                    <component :is="item.component" :active="active === index"></component>
                </div>
            </van-tab>
        </van-tabs>
    </div>
</template>
<script setup lang="ts">
import Header from '@/components/Header.vue'
import Events from '@/components/notice/events/index.vue'
import System from '@/components/notice/system/index.vue'
import Transaction from '@/components/notice/transaction/index.vue'
import { useBaseStore } from '@/stores'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { getChannel } from '@/utils'

const store = useBaseStore()
const route = useRoute()
const i18n = useI18n()

const active = ref(Number(route.query.tab))
const offset = [-20, 10]

const tabNav = [
    {
        key: 'activeNum',
        name: 'Home_events',
        component: Events,
    },
    {
        key: '10000',
        name: 'System_message',
        component: System,
    },
    {
        key: '20000',
        name: 'Transaction_message',
        component: Transaction,
    },
]
const getCount = (key) => {
    return key === 'activeNum' ? store.activeNum : store.unReadCount[getChannel(key, store.userInfo?.uid || 0)]
}
onMounted(() => {
    store.getMsgUnread()
})
</script>
<style lang="scss" scoped>
$height: 116px;
.notice {
    .notice-tab {
        padding: 0 30px;
        :deep(.van-tab__text) {
            display: inline-block;
            width: 100%;
            height: 100%;
            text-align: center;
            line-height: 80px;
            .van-badge__wrapper {
                width: 100%;
            }
        }
        :deep(.van-tabs__wrap) {
            padding: 18px 0;
            height: $height;
            .van-tabs__nav {
                height: 80px;
                background-image: linear-gradient(#4b4644, #4b4644), linear-gradient(#20222c, #20222c);
                border-radius: 40px;
                span {
                    color: #fff;
                    font-size: 24px;
                    font-weight: bold;
                }
                &.van-tabs__nav--line {
                    padding-bottom: 0;
                }

                .van-tab--active {
                    background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#168a75, #168a75);
                    border-radius: 34px;
                }
                .van-tabs__line {
                    display: none;
                }
            }
        }
        // :deep(.van-tab__panel) {
        //     height: calc(var(--vh, 1vh) * 100 - var(--header-height) - $height) !important;
        //     overflow: auto;
        //     touch-action: pan-y;
        // }
        .scroll-wrap {
            height: calc(var(--vh, 1vh) * 100 - var(--header-height) - $height - 30px - var(--safe-height)) !important;
            overflow: auto;
        }
    }
}
</style>
