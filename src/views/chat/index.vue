<template>
    <div class="chat">
        <Header :title="nickname">
            <template #right><div class="chat-expand" @click="handleExpand"></div></template>
        </Header>
        <div class="chat-wrap">
            <Chat v-model="store.unReadCount[getChannel(userId, store.userInfo.uid)]" ref="chat" :userId="userId" @limit="getLimit" />
        </div>
        <div class="chat-footer">
            <div class="chat-internate">
                <div v-if="isReseller && !['10000', '20000'].includes(userId as string)" class="transfer-btn" @click="transferShow = true"></div>
                <div class="chat-input-wrap">
                    <van-field v-model="message" center :placeholder="$t('Write_someting')">
                        <!-- <template #button>
                            <div class="input-btn">
                                <div class="input-pic"></div>
                                <div class="input-emoji"></div>
                            </div>
                        </template> -->
                    </van-field>
                </div>
                <div class="chat-btn" @click="sendText">{{ $t('send') }}</div>
                <div class="chat-new" v-if="store.unReadCount[getChannel(userId, store.userInfo?.uid)]" @click="reachBottom">{{ $t('New') }}</div>
            </div>
        </div>
        <Transfer v-model="transferShow" :min="min" />
    </div>
</template>

<script setup lang="ts" name="chat">
import Header from '@/components/Header.vue'
import Chat from '@/components/chat/chat.vue'
import { useBaseStore } from '@/stores'
import { useRoute, useRouter } from 'vue-router'
import { getChannel } from '@/utils'
import Transfer from '@/components/chat/tranfer.vue'

const message = ref('')

const route = useRoute()
const router = useRouter()
const store = useBaseStore()
const chat = ref(null)
let sending = ref(false)
const transferShow = ref(false)
const channel = ref(getChannel(route.query.userID, store.userInfo?.uid))
const min = ref(0)

const isReseller = computed(() => {
    const isReseller = store.userInfo?.isReseller
    const anoterIs = route.query.isRs === '1'
    return (isReseller || anoterIs) && !(isReseller && anoterIs)
})

const userId = ref(route.query.userID)
const nickname = computed(() => route.query.nickname as string)

const handleExpand = () => {
    router.push('/report')
}
const sendText = async () => {
    if (!message.value || sending.value) {
        return
    }
    sending.value = true
    const res = await store.sendText({
        uid: +userId.value,
        envent: 'text',
        text: message.value,
    })
    if (res.code === 200) {
        message.value = ''
    }
    sending.value = false
}
const reachBottom = () => {
    chat.value.onScrollBottom()
}
const getLimit = (limit) => {
    min.value = limit
}
onBeforeUnmount(() => {
    store.deSubscribe(channel.value)
    store.unReadCount[channel.value] = 0
})
</script>

<style scoped lang="scss">
$chatFooterHeight: calc(100px + var(--safe-height));
.chat-expand {
    width: 40px;
    height: 40px;
    background: url('@/assets/img/chat/expand.png') no-repeat center center;
    background-size: 10px 100%;
}
.chat-wrap {
    height: calc(var(--vh, 1vh) * 100 - var(--header-height) - $chatFooterHeight) !important;
}
.chat-footer {
    position: fixed;
    // bottom: 0;
    top: calc(var(--vh, 1vh) * 100 - $chatFooterHeight);
    left: 0;
    width: 100%;
    min-height: $chatFooterHeight;
    padding: 0 20px 20px 18px;

    .chat-internate {
        @apply flex items-center;

        .transfer-btn {
            @apply flex justify-center items-center;
            margin-right: 12px;
            width: 72px;
            height: 72px;
            border-radius: 50%;
            background: linear-gradient(150deg, #20ba96 0%, #20b794 50%, #1fb491 100%), linear-gradient(#10c580, #10c580);

            &::before {
                display: inline-block;
                content: '';
                width: 50px;
                height: 50px;
                background: url('@/assets/img/chat/transfer.png') no-repeat;
                background-size: 100% 100%;
            }
        }

        .chat-btn {
            width: 150px;
            height: 72px;
            line-height: 72px;
            background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#21c69e, #21c69e);
            background-blend-mode: normal, normal;
            border-radius: 22px;
            text-align: center;
            font-size: 26px;
            font-weight: bold;
            color: #fff;
        }
        .chat-new {
            @apply flex items-center gap-[10px];
            position: absolute;
            top: -60px;
            right: 22px;
            box-sizing: border-box;
            min-width: 104px;
            height: 40px;
            padding: 0 10px;
            line-height: 40px;
            background: #1fb491;
            border: 1px solid #21c69e;
            border-radius: 30px;
            font-size: 26px;
            font-weight: bold;
            color: #fff;

            &::before,
            &::after {
                display: inline-block;
                content: '';
            }
            &::before {
                width: 30px;
                height: 30px;
                background: url('@/assets/img/chat/msg_icon.png') no-repeat;
                background-size: 100% 100%;
            }
            &::after {
                width: 18px;
                height: 11px;
                background: url('@/assets/img/chat/msg_down.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        .chat-input-wrap {
            margin-right: 12px;
            flex: 1;
            background-color: #24272d;
            border-radius: 36px;
            border: 2px solid rgba(101, 96, 94, 0.9);
            overflow: hidden;

            .van-field {
                padding: 6px 12px 6px 33px;
            }
        }

        .input-btn {
            display: flex;

            .input-pic,
            .input-emoji {
                margin-right: 12px;
                width: 58px;
                height: 58px;
                background: url('@/assets/img/chat/image.png') no-repeat;
                background-size: 100% 100%;
            }
            .input-emoji {
                background-image: url('@/assets/img/chat/emoji.png');
            }
        }
        :deep(.van-field) {
            background: #25272d;
            .van-field__control,
            .van-field__control::placeholder {
                font-family: San-Francisco-Text-Medium;
                font-size: 32px;
                font-weight: normal;
                color: #565866;
            }
        }
    }
}
</style>
