<template>
    <div class="iframe">
        <template v-if="loading">
            <div class="loading loader"></div>
            <div class="loading-tip">Connecting...</div>
        </template>
        <div class="iframe-header">
            <div class="header-left">
                <div class="iframe-back" @click="closeFrame">
                    <svg xmlns="http://www.w3.org/2000/svg" width="57" height="57" viewBox="0 0 57 57" fill="none">
                        <rect width="57" height="57" rx="15" fill="#2A2D3D" />
                        <path
                            d="M31.0209 40.0418L19.0001 28.021L31.0209 16.0002"
                            stroke="#668292"
                            stroke-width="3"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                </div>
                <div class="iframe-refresh" @click="refresh">
                    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="31" viewBox="0 0 31 31" fill="none">
                        <path d="M27.125 5.1665V15.4998" stroke="#668292" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M3.875 15.5V25.8333" stroke="#668292" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M27.125 15.5C27.125 9.07971 21.9203 3.875 15.5 3.875C12.2156 3.875 9.24937 5.23704 7.13523 7.42708M3.875 15.5C3.875 21.9203 9.07971 27.125 15.5 27.125C18.6359 27.125 21.4818 25.8833 23.5729 23.8648"
                            stroke="#668292"
                            stroke-width="3"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                </div>
            </div>
            <div v-if="!free" class="iframe-deposit" @click="goRecharge">{{ $t('Deposit') }}</div>
        </div>
        <iframe ref="iframeDom" class="frame-wrap" width="100%" :src="url"></iframe>
        <!-- <van-floating-bubble class="iframe-close" v-model:offset="offset" @click="closeFrame" axis="xy">
            <div class="close"></div>
        </van-floating-bubble> -->
    </div>
</template>
<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const router = useRouter()
const i18n = useI18n()
const urlRefresh = ref(`${Date.now()}`)

const url = computed(() => {
    const urlobj = new URL(decodeURIComponent(route.query.u as string))
    urlobj.searchParams.append('temp_t', urlRefresh.value + '')

    return urlobj.toString()
})
const iframeDom = ref(null)

const loading = ref(false)
const free = ref(false)

const closeFrame = async () => {
    try {
        // const res = await store.gameUnSubscribe()
        // if (res.code === 200) {
        if (route.query.from === 'video') {
            router.replace('/video')
        } else {
            router.replace('/')
        }
        // } else {
        //     showdialog()
        // }
    } catch (e) {
        showdialog()
    }
}
const goRecharge = async () => {
    router.push({
        path: '/wallets',
        query: {
            back: 'true',
            from: 'game',
        },
    })
}

const showdialog = () => {
    showDialog({
        message: i18n.t('The data is expected to berefreshed. lf you cannot clickthe confirmation button below,please wait for 3-5 seconds'),
        className: 'common-dialog',
        showCancelButton: false,
        confirmButtonText: i18n.t('Confirm'),
    })
}

const refresh = () => {
    urlRefresh.value = `${Date.now()}`
}

onMounted(() => {
    loading.value = true
    iframeDom.value.addEventListener('load', function () {
        loading.value = false
    })
})
onActivated(() => {
    free.value = !!route.query.isFree
})
</script>
<style lang="scss" scoped>
.iframe {
    position: relative;
    width: 100%;
    height: 100%;

    .frame-wrap {
        position: relative;
    }
    .recharge {
        min-width: 120px;
        height: 40px;
        padding: 0 20px;
        line-height: 40px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
        color: #ffffff;
        border-radius: 8px;
        font-size: 20px;
        text-align: center;
    }
}
</style>
<style lang="scss">
.iframe {
    @apply flex flex-col;
}
.iframe-header {
    @apply flex items-center justify-between;
    padding: 0 16px 0 11px;
    height: 67px;
    .header-left {
        @apply flex-1 flex items-center;
    }

    .iframe-back {
        margin-right: 13px;
        width: 57px;
        height: 57px;

        svg {
            width: 100%;
            height: 100%;
        }
    }
    .iframe-refresh {
        @apply flex justify-center items-center;
        width: 57px;
        height: 57px;
        border-radius: 15px;
        background: #2a2d3d;

        svg {
            width: 31px;
            height: 31px;
        }
    }
    .iframe-deposit {
        @apply flex justify-center items-center;
        width: 163px;
        height: 57px;
        border-radius: 100px;
        background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%);
        color: #000;

        text-align: center;
        font-family: 'Microsoft YaHei';
        font-size: 30px;
        font-weight: 400;
    }
}
iframe {
    flex: 1;
}
.iframe-close {
    &.van-floating-bubble {
        top: 0;
        right: 0;
        width: 60px;
        height: 60px;
        background: rgba(0, 0, 0, 0.4) !important;
        .close {
            @apply flex justify-center items-center;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);

            &::before {
                display: inline-block;
                content: '';
                width: 32px;
                height: 32px;
                background: url('@/assets/img/home-notice/close.png') no-repeat;
                background-size: 100%;
                opacity: 0.6;
            }
        }
    }
}
/* 横屏样式 */
@media (orientation: landscape) {
    // .iframe-close {
    //     &.van-floating-bubble {
    //         width: 30px;
    //         height: 30px;
    //         top: 10px;
    //         right: 10px;
    //         transform-origin: top right;
    //         // transform: scale(0.5);

    //         .close {
    //             &::before {
    //                 width: 16px;
    //                 height: 16px;
    //             }
    //         }
    //     }
    // }
    .iframe {
        @apply flex-row;

        .iframe-header {
            @apply flex-col flex-col-reverse;
            width: 40px;
            height: 100%;
            padding: 11px 0 15px;
            .header-left {
                @apply flex-col flex-col-reverse flex-1;

                .iframe-back {
                    width: 30px;
                    height: 30px;
                    margin-right: 0;
                    margin-top: 15px;
                }
                .iframe-refresh {
                    width: 30px;
                    height: 30px;
                    border-radius: 5px;

                    svg {
                        width: 20px;
                        height: 20px;
                    }
                }
            }
            .iframe-deposit {
                @apply flex justify-center items-center;
                height: 100px;
                width: 30px;
                padding: 0 5px;
                font-size: 20px;

                writing-mode: vertical-rl;
                transform: rotateZ(180deg);
            }
        }
    }
}
/* 竖屏样式 */
@media (orientation: portrait) {
    .iframe {
        .iframe-header {
            padding-top: calc(var(--safe-height) + constant(safe-area-inset-top));
            padding-top: calc(var(--safe-height) + env(safe-area-inset-top));
            height: calc(var(--safe-height) + constant(safe-area-inset-top) + 67px);
            height: calc(var(--safe-height) + env(safe-area-inset-top) + 67px);
        }
    }

    // .iframe-close {
    //     &.van-floating-bubble {
    //         top: calc(5px + constant(safe-area-inset-top));
    //         top: calc(5px + env(safe-area-inset-top));
    //         right: 10px;
    //     }
    // }
}

.loader {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    margin-left: -50px;
    margin-top: -50px;
    border-radius: 50%;
    background: radial-gradient(farthest-side, #10c580 94%, #0000) top/16px 16px no-repeat, conic-gradient(#0000 30%, #10c580);
    -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 16px), #000 0);
    animation: l13 1s infinite linear;
}
.loading-tip {
    position: absolute;
    top: 55%;
    left: 0;
    width: 100%;
    color: #668292;
    text-align: center;
    font-family: 'Microsoft YaHei';
    font-size: 28px;
    font-weight: 400;
}
@keyframes l13 {
    100% {
        transform: rotate(1turn);
    }
}
</style>
