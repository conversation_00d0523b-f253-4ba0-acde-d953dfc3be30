<template>
    <div class="install">
        <div class="tip">点击下面的按钮安装并下载应用</div>
        <button v-if="pwaIconShow" class="install-btn" @click="addPwaBtn">安装</button>
        <div class="bg-[blue]">1</div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

const pwaIconShow = ref(false)
let deferredPrompt: any = null

onMounted(() => {
    // 监听 PWA 注册事件
    window.addEventListener('beforeinstallprompt', (e) => {
        pwaIconShow.value = true
        e.preventDefault()
        deferredPrompt = e
        console.log('支持安装pwa')
    })
})

const addPwaBtn = () => {
    if (deferredPrompt) {
        deferredPrompt.prompt()
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('用户已添加到主屏幕')
                pwaIconShow.value = false
            } else {
                console.log('用户拒绝添加到主屏幕')
            }
            deferredPrompt = null
        })
    }
}
</script>
<style lang="scss" scoped>
.install {
    font-family: MicrosoftYaHeiSemibold;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    text-align: center;

    .tip {
        padding: 20px 0;
    }
    .install-btn {
        width: 300px;
        height: 80px;
        background-image: linear-gradient(30deg, #0286f1 0%, #0761cf 100%);
        border-radius: 30px;
    }
}
</style>
