<template>
    <div>
        <video ref="videodom" muted autoplay src="https://betfugu.com/video/58ddb55c-c575-4749-bbba-f0fa452dc123.mp4"></video>
    </div>
</template>

<script setup lang="ts" name="setting">
const router = useRouter()
const videodom = ref(null)

onMounted(() => {
    document.addEventListener('click', () => {
        videodom.value.muted = false
    })
})
</script>

<style scoped lang="scss"></style>
