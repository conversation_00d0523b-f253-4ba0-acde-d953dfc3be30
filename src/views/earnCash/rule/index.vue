<template>
    <div class="earn-rule">
        <Header></Header>
        <div class="rule-wrap">
            <div class="ruler-content">
                <p>{{ $t('earnMoney_rule_page1') }}</p>
                <img class="invite-pic" src="@/assets/img/earn-cash/invite.png" />
                <div class="line"></div>
                <p v-html="$t('earnMoney_rule_page2')"></p>
                <p
                    v-html="
                        $t('earnMoney_rule_page3', {
                            // num: route.query.sharerate,
                            // num1: route.query.sharerate2,
                            num: '40%',
                            num1: '10%',
                        })
                    "
                ></p>
                <p v-html="$t('earnMoney_rule_page4')"></p>
                <!-- <div class="rule-btn" @click="goPage(1)">{{ $t('Join the group') }}</div> -->
                <div class="rate-title"><span>Elite Program</span></div>
                <img class="rate-text" src="@/assets/img/earn-cash/rate-text.png" />
                <p class="text" v-html="$t('earnMoney_rule_page5')"></p>
                <!-- <div v-if="rate >= 3" class="rule-btn" @click="goPage(2)">{{ $t('Alamin ang Elite Club') }}</div> -->
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import Header from '@/components/Header.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const rate = computed(() => +route.query.rate)
const goPage = (type) => {
    window.open(type === 1 ? 'https://www.facebook.com/share/AFQETWstn9pSFAQg/?mibextid=K35XfP' : 'https://t.me/astronautime')
}
</script>
<style scoped lang="scss">
.earn-rule {
    background: url('@/assets/img/earn-cash/purple_bg.png') no-repeat left top;
    background-size: 100% 866px;
    .rule-wrap {
        height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height)) !important;
        overflow: auto;

        .ruler-content {
            min-height: 100%;
            padding: 10px 15px 168px;
            background: url('@/assets/img/earn-cash/gold_bg.png') no-repeat left bottom;
            background-size: 100% 168px;
            font-size: 26px;
            color: #ffffff;
            p {
                margin-bottom: 40px;
            }

            .invite-pic {
                width: 507px;
                height: 513px;
                margin: 0 auto;
            }
            .line {
                width: 636px;
                height: 22px;
                background: url('@/assets/img/earn-cash/line.png');
                background-size: 100% 100%;
                margin: 20px auto;
            }
            :deep(.d1) {
                color: #4cff5d;
            }
            :deep(.d2) {
                color: #5bceff;
            }
            .rate-title {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 47px;
                gap: 10px;

                span {
                    line-height: 47px;
                    font-size: 40px;
                    font-weight: bold;
                    color: #ffffff;
                    text-shadow: 0px 1px 0px 2px rgba(189, 68, 241, 0.8);
                }
                &::after,
                &::before {
                    content: '';
                    width: 165px;
                    height: 18px;
                    background: url('@/assets/img/earn-cash/title_line.png') no-repeat;
                    background-size: 100% 100%;
                }
                &::after {
                    transform: rotateZ(180deg);
                }
            }
            .rate-text {
                margin: 30px 0;
                height: 320px;
            }
            .text {
                font-size: 24px;
                color: #87a1c0;
            }
            .rule-btn {
                width: 400px;
                height: 80px;
                margin: 60px auto;
                line-height: 80px;
                background-image: linear-gradient(100deg, #1acf9a 0%, #13b187 65%, #0c9274 100%), linear-gradient(#ffffff, #ffffff);
                background-blend-mode: normal, normal;
                border-radius: 40px;
                font-size: 30px;
                color: #fff;
                text-align: center;
            }
        }
    }
}
</style>
