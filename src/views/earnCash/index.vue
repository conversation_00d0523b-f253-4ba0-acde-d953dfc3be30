<template>
    <div class="earn">
        <Back v-if="route.query.back" />

        <div class="earn-cash" @scroll="handleScroll">
            <div v-if="isAgent" class="nav">
                <div v-for="item in navTab" :class="['nav-item', { active: navIndex === item.key }]" :key="item.key" @click="changeTab(item)">
                    {{ $t(item.name) }}
                </div>
            </div>
            <div v-if="navIndex === 0" class="earn-content">
                <div class="banner">
                    <div class="banner-btn" @click="goRule"><img src="@/assets/img/earn-cash/details.png" /></div>
                </div>
                <div class="top-rank" v-if="topRank.length">
                    <div class="rank-title">{{ $t('Promotion_ranking') }}</div>
                    <div class="rank-list">
                        <div :class="['rank', `rank${index}`]" v-for="(item, index) in topRank" :key="index">
                            <div class="top-avatar">
                                <div class="avatar">
                                    <img :src="item.avatar" />
                                </div>
                            </div>
                            <van-text-ellipsis class="rank-name" :content="encryptUsername(decodeName(item.nickname))" />
                            <div class="rank-gold">{{ store.cureencySymbol() }} {{ formatNumber(item.score) }}</div>
                        </div>
                    </div>
                    <div class="rank-more" @click="dialogVisible.rank = true">{{ $t('See_more') }} ></div>
                </div>
                <div v-if="exchangeList.length" class="roll-notice">
                    <van-swipe class="roll-swipe" :autoplay="3000" :show-indicators="false" :vertical="true" :touchable="false">
                        <van-swipe-item v-for="item in exchangeList" :key="item.uid">
                            <div class="roll-item">
                                <van-text-ellipsis
                                    :content="
                                        $t('earnMoney_carousel_info', { nickname: encryptUsername(decodeName(item.nickname)), num: item.count })
                                    "
                                />
                            </div>
                        </van-swipe-item>
                    </van-swipe>
                </div>
                <div class="eran-person">
                    <!-- <div class="person-top">
                        <div class="person-avatar"><img :src="userInfo.avatar" /></div>
                        <div class="person-center">
                            <div class="name">{{ decodeName(userInfo.nickname) }}</div>
                            <van-rate class="person-rate" v-model="rate" disabled :icon="Rate" :void-icon="Brate" />
                        </div>
                        <div class="person-btn" @click="showShare = true">{{ $t('Invite_to_earn') }}</div>
                    </div> -->
                    <PersonInfo v-model="rate" @invite="showShare = true" />
                    <div class="person-balance">
                        <div class="balance-tip">
                            Balance： <span class="balance-num">{{ store.cureencySymbol() }}{{ formatNumber(baseInfo.current || 0) }}</span>
                        </div>
                        <div v-if="baseInfo.current > 0" class="balance-btn decoration" @click="handleWithDraw">
                            {{ $t('Withdrawal') }}
                        </div>
                    </div>
                    <div class="relative">
                        <div class="relative-item">
                            <div
                                class="relative-tip decoration"
                                @click="
                                    () => {
                                        checkLogin().then(() => {
                                            dialogVisible.invite = true
                                        })
                                    }
                                "
                            >
                                {{ $t('My_Invites') }}
                            </div>
                            <div class="relative-num">{{ baseInfo.invitetotal || 0 }}</div>
                        </div>
                        <div class="relative-item relative-line">
                            <div
                                class="relative-tip decoration"
                                @click="
                                    () => {
                                        checkLogin().then(() => {
                                            dialogVisible.earn = true
                                        })
                                    }
                                "
                            >
                                {{ $t('Total_Earned') }}
                            </div>
                            <div class="relative-num">{{ store.cureencySymbol() }}{{ formatNumber(baseInfo.total || 0) }}</div>
                        </div>
                        <div class="relative-item">
                            <div class="relative-tip">{{ $t('Month_Earned') }}</div>
                            <div class="relative-num">{{ store.cureencySymbol() }}{{ formatNumber(baseInfo.day30 || 0) }}</div>
                        </div>
                    </div>
                </div>
                <div class="rule">
                    <div class="rule-top">
                        <p>{{ $t('earnMoney_rule_title') }}</p>
                        <div v-if="store.wallet?.currency === 'PHP'" class="rule-btn decoration" @click="goRule">{{ $t('Learn_more') }}</div>
                    </div>
                    <!-- v-html="$t('earnMoney_rule_page', { num: getPercent(baseInfo.sharerate), num1: getPercent(baseInfo.sharerate2) })" -->
                    <p class="rule-content" v-html="$t('earnMoney_rule_page', { num: '40%', num1: '10%' })"></p>
                </div>
                <!--div class="jion-btn">
                    <div class="btn" @click="concat">{{ $t('earncash_join_group') }}</div>
                </div> -->
            </div>
            <div class="earn-master" v-else>
                <div class="master-info">
                    <PersonInfo v-model="rate" @invite="showShare = true" />
                    <div class="master-balance">
                        <div class="master-balance-num">
                            <div>
                                {{ $t('Balance') }}: <span>{{ store.cureencySymbol() }}{{ formatNumber(baseInfo.current || 0) }}</span>
                            </div>

                            <div class="master-btn" @click="handleWithDraw">{{ $t('Withdrawal') }}</div>
                        </div>
                        <div class="balance-btn decoration" @click="dialogVisible.record = true">{{ $t('Record') }}</div>
                    </div>
                    <div class="master-total bottom-line">
                        <p class="master-title">{{ $t('Total') }}:</p>
                        <div class="master-total-item">
                            <div class="total-item right-line">
                                <p class="total-item-top">{{ $t('Cumulative_users') }}</p>
                                <p class="total-item-bottom">
                                    {{ agentData.invitetotal }} <span class="income">{{ agentData.invitetoday }}</span>
                                </p>
                            </div>
                            <div class="total-item">
                                <p class="total-item-top">{{ $t('Cumulative_income') }}</p>
                                <p
                                    :class="[
                                        'total-item-bottom',
                                        { scale: (formatNumber(agentData.total || 0) + formatNumber(agentData.today || 0)).length > 10 },
                                    ]"
                                >
                                    {{ cureencySymbol() }}{{ formatNumber(agentData.total || 0) }}
                                    <span class="income">{{ cureencySymbol() }}{{ formatNumber(agentData.today || 0) }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="master-other bottom-line">
                        <p class="master-title">{{ $t('Today') }}:</p>
                        <div class="other-con">
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Increase') }}</p>
                                <p class="other-con-bottm">{{ agentData.invitetoday }}</p>
                            </div>
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Active') }}</p>
                                <p class="other-con-bottm">{{ agentData.activetoday }}</p>
                            </div>
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Income') }}</p>
                                <p class="other-con-bottm">{{ cureencySymbol() }}{{ formatNumber(agentData.today || 0) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="master-other">
                        <p class="master-title">{{ $t('Season') }}:</p>
                        <div class="other-con">
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Increase') }}</p>
                                <p class="other-con-bottm">{{ agentData.inviteweek }}</p>
                            </div>
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Active') }}</p>
                                <p class="other-con-bottm">{{ agentData.activeweek }}</p>
                            </div>
                            <div class="other-con-item">
                                <p class="other-con-top">{{ $t('Income') }}</p>
                                <p class="other-con-bottm">{{ cureencySymbol() }}{{ formatNumber(agentData.incomeweek || 0) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ShareDialog v-model="showShare" />
        <BaseFooter :initTab="2" />
        <earnClub v-model="dialogVisible.club" />
        <NormalDialog class="redeem-dialog" v-model="dialogVisible.reddem" :title="$t('Enter_redeem_amount')" :closeOnClickOverlay="false">
            <div class="redeem-content">
                <div class="input-wrap"><van-field v-model="redeemCount" :placeholder="$t('please_input_integer')" type="digit" /></div>
                <div class="oprate">
                    <div class="reddem-btn" @click="beanExchange">{{ $t('Submit') }}</div>
                    <div class="reddem-record" @click="dialogVisible.record = true"></div>
                </div>
            </div>
        </NormalDialog>
        <Rank v-if="dialogVisible.rank" @onClose="dialogVisible.rank = false" />
        <Invite v-if="dialogVisible.invite" @onClose="dialogVisible.invite = false" />
        <TotalEarn v-if="dialogVisible.earn" @onClose="dialogVisible.earn = false" />
        <ReedemRecoder v-if="dialogVisible.record" @onClose="dialogVisible.record = false" />
        <Server />
    </div>
</template>

<script setup lang="ts" name="earnCash">
import { defineAsyncComponent, hydrateOnIdle } from 'vue'
import BaseFooter from '@/components/BaseFooter.vue'
import EarnClub from '@/components/dialog/earnClub.vue'
import ReedemRecoder from '@/components/earnMoney/reedem-recoder.vue'
import ShareDialog from '@/components/dialog/shareDialog.vue'
import { formatNumber } from '@/utils/toolsValidate'
// import Brate from '@/assets/img/earn-cash/brate.png'
// import Rate from '@/assets/img/earn-cash/rate.png'
import { useRouter, useRoute } from 'vue-router'
// import useClipboard from 'vue-clipboard3'
import NormalDialog from '@/components/dialog/NormalDialog.vue'
import Rank from '@/components/earnMoney/rank.vue'
import Invite from '@/components/earnMoney/t1Player.vue'
import TotalEarn from '@/components/earnMoney/totalEarn.vue'
import { getEarnInfo, getEarnBroadcast, getRankTop, getAgentStats, getNotLoginEarnInfo, getNotLoginRankTop } from '@/api/earnCash'
import { AgentStatus } from '@/api/earnCash/types'
import { EarnInfo } from '@/api/earnCash/types'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import Back from '@/components/backBtn.vue'
import { decodeName, encryptUsername, checkLogin } from '@/utils'
import { Log } from '@/api/log'
import PersonInfo from '@/components/earnMoney/personInfo.vue'
import { useScroll } from '@/hooks/useScroll'
// import Server from '@/components/server.vue'
const Server = defineAsyncComponent({
    loader: () => import('@/components/server.vue'),
    hydrate: hydrateOnIdle(/* 传递可选的最大超时 */),
})

const router = useRouter()
const route = useRoute()
const store = useBaseStore()
const { cureencySymbol } = storeToRefs(store)
// const { toClipboard } = useClipboard()
const i18n = useI18n()
const { handleScroll } = useScroll()

const isAgent = computed(() => store.isAgent)

const showShare = ref(false)
const agentData = ref<AgentStatus>()

const navTab = ref([
    {
        name: 'Agent',
        key: 0,
    },
    {
        name: 'Master',
        key: 1,
    },
])
const navIndex = ref(0)
const rate = ref(0)
const redeemCount = ref('')
const dialogVisible = reactive({
    rank: false,
    reddem: false,
    club: false,
    invite: false,
    earn: false,
    record: false,
})
watch(
    () => dialogVisible.reddem,
    (val) => {
        if (!val) {
            redeemCount.value = ''
        }
    }
)
const baseInfo = ref<Partial<EarnInfo>>({})
const exchangeList = ref([])
const topRank = ref([])
const groupMap = {
    PHP: 'https://www.facebook.com/groups/3834346583512517',
    USD: 'https://www.facebook.com/share/g/1AJiintjvs/',
    IDR: '',
    INR: '',
    BRL: 'https://www.facebook.com/share/g/1Bn4ta98AK/',
    NGN: 'https://www.facebook.com/share/g/1B69uTGj6Q/',
    TRY: 'https://www.facebook.com/share/g/18Yt1ay2qW/',
}
const changeTab = (item) => {
    navIndex.value = item.key
    if (item.key === 1) {
        getAgent()
    }
}

const beanExchange = () => {
    if (!redeemCount.value) {
        return showToast({
            message: i18n.t('Incorrect_amount_enter_right'),
            zIndex: 30002,
        })
    }
    if (baseInfo.value.invitetotal < 5) {
        return showToast({
            message: i18n.t('cash_out_invite_5people'),
            zIndex: 30002,
        })
    }

    store.Beanexchange(redeemCount.value).then((res) => {
        if (res.code === 200) {
            dialogVisible.reddem = false
            baseInfo.value.current = res.latest
            redeemCount.value = ''
            showToast(i18n.t('Redemption_successful'))
        }
    })
}
const handleWithDraw = () => {
    if (!store.token) {
        return router.push({ path: '/login' })
    }
    dialogVisible.reddem = true
    // router.push({
    //     path: '/wallets',
    //     query: {
    //         tab: 1,
    //     },
    // })
}
const goRule = () => {
    router.push({
        path: '/earn-rule',
        query: {
            sharerate: getPercent(baseInfo.value.sharerate),
            sharerate2: getPercent(baseInfo.value.sharerate2),
            rate: rate.value,
        },
    })
}
const getPercent = (num = 0) => {
    return num * 100 + '%'
}
const getAgent = () => {
    getAgentStats().then((res) => {
        if (res.code === 200) {
            agentData.value = res.data
        }
    })
}
const init = () => {
    if (store.token) {
        getEarnInfo().then((res) => {
            if (res.code === 200) {
                baseInfo.value = res.data
                rate.value = res.data.starinfo?.shareStar
            }
        })
        getRankTop({
            start: 0,
            stop: 2,
        }).then((res) => {
            if (res.code === 200) {
                topRank.value = res.data.populars.members.map((item) => {
                    return { ...item, score: res.data.populars.scores[item.uid] }
                })
            }
        })
        getEarnBroadcast().then((res) => {
            if (res.code === 200) {
                exchangeList.value = res.data
            }
        })
    } else {
        getNotLoginEarnInfo().then((res) => {
            if (res.code === 200) {
                baseInfo.value = res.data
            }
        })
        getNotLoginRankTop({
            start: 0,
            stop: 2,
        }).then((res) => {
            if (res.code === 200) {
                topRank.value = res.data.populars.members.map((item) => {
                    return { ...item, score: res.data.populars.scores[item.uid] }
                })
            }
        })
    }

    if (isAgent.value) {
        getAgent()
    }
}
const concat = () => {
    window.open(groupMap[store.wallet?.currency] || groupMap.PHP)
}
onMounted(() => {
    init()
})
onActivated(() => {
    Log({
        event: 'earn_visit',
    })
    init()
})
</script>

<style scoped lang="scss">
$navT: 60px;
$navHeight: 64px;
.earn {
    :deep(.back) {
        top: calc(30px + var(--safe-height) + constant(safe-area-inset-top));
        top: calc(30px + var(--safe-height) + env(safe-area-inset-top));
    }
    .decoration {
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
        padding-bottom: 2px;
        &::after {
            display: inline-block;
            content: '';
            width: 26px;
            height: 26px;
            background: url('@/assets/img/earn-cash/arrow.png') no-repeat;
            background-size: 100% 100%;
        }
        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: #fff;
            border-radius: 4px;
        }
    }
    .earn-cash {
        height: calc(var(--vh, 1vh) * 100 - var(--footer-height) - var(--safe-height)) !important;
        overflow: auto;

        .earn-content,
        .earn-master {
            padding: 0 15px;
            background: url('@/assets/img/earn-cash/gold_bg.png') no-repeat left bottom;
            background-size: 100% 372px;

            .master-balance {
                // margin: 40px 0 30px;
                // height: 180px;
                // padding: 0 30px 0 40px;
                // background: url('@/assets/img/earn-cash/master_bg.png') no-repeat;
                // background-size: 100% 100%;
                border: 4px solid rgba(255, 255, 255, 0.2);
                border-left: 0;
                border-right: 0;
                padding-top: 30px;
                padding-bottom: 10px;
                color: #fff;

                .master-balance-num {
                    display: flex;
                    align-items: center;
                    font-size: 48px;

                    div:nth-child(1) {
                        flex: 1;
                    }

                    span {
                        color: #ffcc30;
                    }
                }
                .master-btn {
                    width: 220px;
                    height: 68px;
                    line-height: 68px;
                    border-radius: 34px;
                    border: solid 3px #ffffff;
                    text-align: center;
                    font-size: 24px;
                    font-weight: bold;
                    color: #ffffff;
                }
                .balance-btn {
                    display: inline;
                    font-size: 24px;
                    &::after {
                        width: 20px;
                        height: 20px;
                        margin-left: 5px;
                    }
                }
            }
            .master-info {
                padding: 22px 38px;
                // height: 940px;
                background: url('@/assets/img/earn-cash/master_info.png') no-repeat;
                background-size: 100% 100%;
                .master-title {
                    font-size: 30px;
                    font-weight: bold;
                    line-height: 62px;
                    color: #ffffff;
                }
                .master-total {
                    .master-total-item {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        .total-item {
                            position: relative;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            flex: 1;
                            // height: 120px;
                            font-size: 40px;
                            line-height: 36px;
                            color: #fff;

                            &.right-line::after {
                                position: absolute;
                                right: 0;
                                top: 0;
                                content: '';
                                display: inline-block;
                                width: 4px;
                                height: 100%;
                                background: #46806c;
                                border-radius: 2px;
                            }
                        }
                        .total-item-top {
                            font-size: 24px;
                        }
                        .total-item-bottom {
                            margin-top: 29px;
                            color: #ffcc30;
                            height: 32px;
                            line-height: 32px;
                            &.scale {
                                font-size: 35px;
                            }
                            .income {
                                color: #fff;

                                &::before {
                                    display: inline-block;
                                    content: '';
                                    margin-right: 10px;
                                    width: 17px;
                                    height: 100%;
                                    background: url('@/assets/img/earn-cash/income_bg.png') no-repeat center center;
                                    background-size: 100% 35px;
                                }
                            }
                        }
                    }
                }
                .bottom-line {
                    padding-bottom: 41px;
                    border-bottom: 4px solid rgba(255, 255, 255, 0.2);
                }
                .master-other {
                    padding-bottom: 41px;
                    .other-con {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        .other-con-item {
                            flex: 1;
                            line-height: 36px;
                            text-align: center;

                            .other-con-top {
                                font-size: 24px;
                                color: #fff;
                            }
                            .other-con-bottm {
                                margin-top: 30px;
                                font-size: 40px;
                                color: #ffcc30;
                            }
                        }
                    }
                }
            }
        }
        .nav {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: $navHeight + $navT;
            padding: $navT 40px 0;

            .nav-item {
                width: 330px;
                height: 56px;
                line-height: 56px;
                background-color: #4b4644;
                border-radius: 22px;
                text-align: center;
                font-size: 24px;
                font-weight: bold;
                color: #ffffff;

                &.active {
                    height: 64px;
                    line-height: 64px;
                    font-size: 30px;
                    background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#ed501d, #ed501d);
                }
            }
        }
        .earn-master {
            padding-top: 40px;
            height: calc(100% - $navHeight - $navT);
        }

        .banner {
            position: relative;
            height: 370px;
            background: url('@/assets/img/earn-cash/banner.png') no-repeat;
            background-size: 100% 100%;

            .banner-btn {
                position: absolute;
                top: 195px;
                left: 315px;
                width: 128px;
                height: 32px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .top-rank {
            box-sizing: border-box;
            height: 380px;
            background: url('@/assets/img/earn-cash/rank.png') no-repeat;
            background-size: 100% 100%;
            padding: 20px;

            .rank-title {
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-bottom: 58px;
                font-size: 44px;
                line-height: 41px;
                color: #fff;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 40px;
                    height: 41px;
                    background: url('@/assets/img/earn-cash/wards.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            .rank-list {
                @apply grid grid-cols-3 gap-[10px];

                .rank {
                    position: relative;
                    width: 220px;
                    height: 200px;
                    padding: 115px 10px 0;
                    border-radius: 20px;
                    background-image: linear-gradient(150deg, #ffcc44 0%, rgba(255, 204, 68, 0.7) 40%, rgba(255, 204, 68, 0.4) 100%);

                    .top-avatar {
                        position: absolute;
                        top: -42px;
                        left: 50%;
                        width: 120px;
                        height: 140px;

                        transform: translateX(-50%);
                        &::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: url('@/assets/img/earn-cash/first.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .avatar {
                            width: 120px;
                            height: 120px;
                            border-radius: 50%;
                            overflow: hidden;
                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                                object-fit: cover;
                            }
                        }
                    }
                    .rank-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #ffffff;
                        text-align: center;
                    }
                    .rank-gold {
                        height: 40px;
                        background-color: rgba(0, 0, 0, 0.2);
                        border-radius: 20px;
                        font-size: 26px;
                        font-weight: bold;
                        color: #fff;
                        text-align: center;
                    }
                }
                .rank1 {
                    background-image: linear-gradient(150deg, #98e0ff 0%, rgba(152, 224, 255, 0.63) 40%, rgba(152, 224, 255, 0.25) 100%),
                        linear-gradient(#000000, #000000);
                    .top-avatar {
                        &::after {
                            background-image: url('@/assets/img/earn-cash/second.png');
                        }
                    }
                }
                .rank2 {
                    background-image: linear-gradient(150deg, #c56f5b 0%, rgba(197, 111, 91, 0.7) 40%, rgba(197, 111, 91, 0.4) 100%),
                        linear-gradient(#c56446, #c56446);
                    .top-avatar {
                        &::after {
                            background-image: url('@/assets/img/earn-cash/third.png');
                        }
                    }
                }
            }
            .rank-more {
                margin-top: 10px;
                font-size: 24px;
                font-weight: bold;
                line-height: 40px;
                color: #fffc30;
                text-decoration: underline;
                text-align: right;
            }
        }
        .roll-notice {
            margin: 32px 0;
            height: 72px;
            padding: 0 31px;
            background: url('@/assets/img/earn-cash/roll.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;

            .roll-swipe {
                height: 100%;

                .roll-item {
                    padding-left: 36px;
                    line-height: 72px;
                    background: url('@/assets/img/earn-cash/fire.png') no-repeat left center;
                    background-size: 25px 32px;
                    font-size: 24px;
                    font-weight: bold;
                    color: #ffffff;
                    :deep(.van-text-ellipsis) {
                        line-height: 72px;
                    }
                }
            }
        }
        .eran-person {
            margin-top: 32px;
            border-radius: 40px;
            width: 720px;
            padding: 20px 40px;
            background: url('@/assets/img/earn-cash/person.png') no-repeat;
            background-size: 100% 100%;

            // .person-top {
            //     display: flex;
            //     align-items: center;
            //     gap: 15px;
            //     padding-bottom: 30px;

            //     .person-avatar {
            //         position: relative;
            //         width: 118px;
            //         height: 118px;

            //         img {
            //             width: 100%;
            //             height: 100%;
            //             border-radius: 50%;
            //             object-fit: cover;
            //         }

            //         &::after {
            //             content: '';
            //             position: absolute;
            //             top: -2px;
            //             left: -2px;
            //             width: 120px;
            //             height: 120px;
            //             background: url('@/assets/img/earn-cash/avatar.png') no-repeat;
            //             background-size: 100% 100%;
            //         }
            //     }
            //     .person-center {
            //         display: flex;
            //         flex-direction: column;
            //         justify-content: center;
            //         flex: 1;
            //         // padding-top: 20px;
            //         .name {
            //             margin-bottom: 6px;
            //             font-size: 36px;
            //             font-weight: bold;
            //             line-height: 36px;
            //             color: #ffffff;
            //         }
            //         .person-rate {
            //             height: 40px;
            //         }
            //     }
            //     .person-btn {
            //         justify-self: flex-end;
            //         align-self: center;
            //         width: 220px;
            //         height: 68px;
            //         line-height: 68px;
            //         // background-color: #3ccaff;
            //         border-radius: 34px;
            //         border: solid 3px #ffffff;
            //         font-size: 24px;
            //         font-weight: bold;
            //         letter-spacing: -1px;
            //         color: #ffffff;
            //         text-align: center;
            //     }
            // }

            .person-balance {
                display: flex;
                align-items: center;
                height: 154px;
                font-size: 48px;
                color: #fff;
                $border: 4px solid #4f7595;
                border-bottom: $border;
                border-top: $border;

                .balance-tip {
                    flex: 1;
                }
                .balance-num {
                    color: #ffcc30;
                }
                .balance-btn {
                    font-size: 30px;
                }
            }
            .relative {
                display: flex;
                padding: 20px 0 30px;

                .relative-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    flex: 1;
                    height: 120px;
                    text-align: center;
                    &.relative-line {
                        position: relative;
                        &::before,
                        &::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            width: 4px;
                            height: 100%;
                            background: #455066;
                            border-radius: 2px;
                        }
                        &::before {
                            left: 0;
                        }
                        &::after {
                            right: 0;
                        }
                    }
                }
                .relative-tip {
                    display: inline-block;
                    font-size: 24px;
                    color: #fff;

                    &::after {
                        position: relative;
                        top: 4px;
                        margin-left: 8px;
                    }
                }
                .relative-num {
                    color: #ffcc30;
                    font-size: 40px;
                }
            }
        }
        .rule {
            margin-top: 58px;
            padding-bottom: 60px;
            font-size: 24px;
            font-weight: bold;
            color: #fff;

            .rule-top {
                display: flex;

                align-items: center;

                margin-bottom: 30px;

                p {
                    flex: 1;
                    font-size: 40px;
                    color: #fff;
                }
            }
            .rule-btn {
                color: #ffcc30;

                &::after {
                    width: 12px;
                    height: 18px;
                    background-image: url('@/assets/img/earn-cash/y_row.png');
                }
                &::before {
                    background: #ffcc30;
                }
            }
            .rule-content {
                span {
                    color: #4cff5d;
                }
            }
        }
        :deep(.d1) {
            color: #4cff5d;
        }
        :deep(.d2) {
            color: #5bceff;
        }
    }
    .redeem-content {
        padding: 43px 50px 20px;

        .input-wrap {
            // border: 1px solid #303a47;
            border-radius: 30px;
            overflow: hidden;
            width: 540px;
            height: 108px;

            :deep(.van-field) {
                padding: 0 30px;
                height: 100%;
                line-height: 108px;
                background-color: #444b5e;

                .van-field__control {
                    color: #fff;
                    &::placeholder {
                        color: #747a8d;
                    }
                }
            }
        }
        .oprate {
            position: relative;
        }
        .reddem-btn {
            width: 408px;
            height: 80px;
            margin: 50px auto 0;
            line-height: 80px;
            border-radius: 50px;
            color: #fff;
            background: #1c946c;
            text-align: center;
            font-size: 30px;
        }
        .reddem-record {
            position: absolute;
            right: -20px;
            top: 20px;
            width: 40px;
            height: 44px;
            background: url('@/assets/img/earn-cash/record.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .redeem-dialog {
        :deep(.dialog) {
            width: 684px;
            height: 450px;
            background-image: linear-gradient(#2a2d3d, #2a2d3d), linear-gradient(#2a2d3b, #2a2d3b);
            background-blend-mode: normal, normal;
            border-radius: 30px;
            border: none;

            .close-icon {
                background-image: url('@/assets/img/new-home/close.png');
            }
        }
    }
    // .jion-btn {
    //    padding-bottom: 50px;
    //    .btn {
    //        margin: 0 auto;
    //        width: 440px;
    //        height: 88px;
    //        line-height: 88px;
    //        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
    //        background-blend-mode: normal, normal;
    //        border-radius: 44px;
    //        font-size: 36px;
    //        color: #ffffff;
    //        text-align: center;
    //    }
    // }
}
</style>
