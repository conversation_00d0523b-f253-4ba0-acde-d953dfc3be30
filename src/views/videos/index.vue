<template>
    <div class="videos">
        <Loading v-if="(state.loading && slides.length === 0) || state.refresh" />
        <div class="muted-icon" @click="handleMutedClick">
            <img v-if="baseStore.isMuted" src="@/assets/img/video/sound_off.png" />
            <img v-else src="@/assets/img/video/sound_on.png" />
        </div>
        <!-- v-love="state.uniqueId" -->
        <div class="video-wrap" :id="state.uniqueId" @click="click(state.uniqueId)">
            <Swiper
                :modules="modules"
                :space-between="50"
                direction="vertical"
                :virtual="(VirtualOptions as undefined)"
                @slideChange="onSlideChange"
                @touchEnd="onTouchEnd"
                @slider-move="onTouchMove"
            >
                <swiper-slide v-for="(item, index) in slides" :key="item.id + index" :virtualIndex="index" v-slot="{ isActive }">
                    <Item
                        :item="item"
                        :videoConfig="videoConfig"
                        :autoplay="index === 0"
                        :isActive="isActive"
                        :position="{ uniqueId: state.uniqueId, index, activeIndex: state.activeIndex }"
                        :paging="paging"
                        @seriesChange="seriesChange"
                    />
                </swiper-slide>
            </Swiper>
        </div>
        <BaseFooter :initTab="1" />
        <van-overlay :show="baseStore.showVideoDrive" z-index="100" @click="baseStore.showVideoDrive = false">
            <div class="driver">
                <img class="driver-phone" src="@/assets/img/video/phone.png" />
                <img ref="pointerDom" class="driver-point" src="@/assets/img/video/pointer.png" />
                <p class="driver-tip">{{ $t('Slide_it') }}</p>
            </div>
        </van-overlay>
    </div>
</template>
<script setup lang="tsx" name="videos">
import BaseFooter from '@/components/BaseFooter.vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Virtual } from 'swiper/modules'
import Item from '@/components/video/videoItem.vue'
import 'swiper/scss'
import { useBaseStore } from '@/stores'
import bus, { EVENT_KEY } from '@/utils/bus'
import * as service from '@/api/video'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'

const baseStore = useBaseStore()
const i18n = useI18n()
const route = useRoute()
const router = useRouter()
const refreshKey = ref(1)

const modules = [Virtual]
const pointerDom = ref(null)
const VirtualOptions = {
    enabled: true,
    addSlidesBefore: 2,
    addSlidesAfter: 5,
    renderExternalUpdate: false,
    cache: false,
}
const slides = ref([])
const videoConfig = ref({})
const state = reactive({
    activeIndex: 0,
    loading: true,
    uniqueId: 'video',
    finish: false,
    refresh: false,
    total: 0,
})

const paging = reactive({
    page: -1,
    size: 5,
    id: '',
})
let neddRefresh = false
// 定位
const session = sessionStorage.getItem('videoHis')
if (session) {
    const page = JSON.parse(session)
    paging.id = page.id
    sessionStorage.removeItem('videoHis')
}

watch(
    () => baseStore.wallet?.currency,
    () => {
        neddRefresh = true
    }
)
watch(
    () => baseStore.userInfo?.uid,
    (val, newVal) => {
        if (val !== newVal) {
            neddRefresh = true
        }
    }
)

const onSlideChange = (swiper) => {
    state.activeIndex = swiper.activeIndex
    if (state.loading) {
        return
    }
    if (slides.value.length - swiper.activeIndex <= 4) {
        if (paging.page + 1 >= state.total) {
            paging.page = 0
        } else {
            paging.page = paging.page + paging.size
        }
        getVideoList()
    }
}
const onTouchMove = () => {
    if (!baseStore.token) {
        router.push('/login')
    }
}
const refresh = async () => {
    state.refresh = true
    state.loading = true
    paging.page = -1
    refreshKey.value = refreshKey.value + 1
    neddRefresh = false
    await getVideoList()
}
const onTouchEnd = async (swiper) => {
    if (swiper.isBeginning && swiper.translate > 100) {
        await refresh()
        showToast(i18n.t('Refreshed'))
    }
}
const handleMutedClick = () => {
    baseStore.isMuted = !baseStore.isMuted
}

const getVideoList = async () => {
    state.loading = true
    try {
        const res = await service.getVideoList({
            ...paging,
            withDrama: true,
        })
        if (res.code === 200) {
            let items = res.data.videos || []
            videoConfig.value = Object.assign({}, videoConfig.value, res.data.selfVideo)

            if (paging.id) {
                const idx = items.findIndex((item) => item.id === paging.id)
                paging.id = ''
                if (~idx) {
                    items = items.slice(idx)
                }
            }
            paging.page = res.data.from
            slides.value = state.refresh ? [...items] : [...slides.value, ...items]
            state.total = res.data.total
        }
        state.loading = false
        state.refresh && (state.refresh = false)
    } catch (e) {
        console.log(e)
    }
}
const seriesChange = (configs, idx) => {
    const config = slides.value[idx]
    slides.value[idx] = Object.assign(config, configs.video)
    videoConfig.value = Object.assign({}, videoConfig.value, configs.self)
}
function click(uniqueId) {
    if (uniqueId !== state.uniqueId) return
    bus.emit(EVENT_KEY.SINGLE_CLICK_BROADCAST, {
        uniqueId,
        index: state.activeIndex,
        type: EVENT_KEY.ITEM_TOGGLE,
    })
}
// function dbclick(uniqueId) {
//     if (uniqueId !== state.uniqueId) return
//     bus.emit(EVENT_KEY.SINGLE_CLICK_BROADCAST, {
//         uniqueId,
//         index: state.activeIndex,
//         type: EVENT_KEY.ITEM_LIKE,
//     })
// }

function togglePlay() {
    bus.emit(EVENT_KEY.SINGLE_CLICK_BROADCAST, {
        uniqueId: state.uniqueId,
        index: state.activeIndex,
        type: EVENT_KEY.ITEM_TOGGLE,
    })
}
function toolCallback({ id, key, flag, index, type, dramaid }) {
    if (!videoConfig.value[id]) {
        videoConfig.value[id] = {}
    }

    const item = slides.value[index]
    if (type === 2) {
        if (!videoConfig.value[dramaid]) {
            const config = videoConfig.value[id]
            videoConfig.value[dramaid] = { like: config.like, favorite: config.favorite }
        }
        videoConfig.value[dramaid][key] = flag
        const dramItem = baseStore.dramaStatus[dramaid]
        dramItem[key] = dramItem[key] + (flag ? +1 : -1)
    }
    videoConfig.value[id][key] = flag
    slides.value[index] = { ...item, [key]: item[key] + (flag ? +1 : -1) }
}
const getVideoDetail = async () => {
    const { dramaid, index } = baseStore.buyVideoParam
    try {
        const res = await service.getVideoDramaIndex({
            dramaid: dramaid,
            index: index === 0 ? 1 : index,
        })
        if (res.code === 200) {
            slides.value = [res.video]
            videoConfig.value[res.video.id] = res.self
        }
        baseStore.buyVideoParam = null
    } catch (e) {
        console.log(e)
        baseStore.buyVideoParam = null
    }
}

onBeforeMount(async () => {
    baseStore.dramaStatus = {}
    if (baseStore.buyVideoParam) {
        await getVideoDetail()
    }

    getVideoList()
})

onMounted(() => {
    bus.on(EVENT_KEY.SINGLE_CLICK, click)
    // bus.on(EVENT_KEY.DOUBLE_CLICK, dbclick)
    bus.on(EVENT_KEY.TOGGLE_CURRENT_VIDEO, togglePlay)
    bus.on(EVENT_KEY.TOOL_CLICK_BROADCAST, toolCallback)
    if (baseStore.showVideoDrive) {
        pointerDom.value.addEventListener('animationend', () => {
            baseStore.showVideoDrive = false
        })
    }
})
onUnmounted(() => {
    bus.off(EVENT_KEY.SINGLE_CLICK, click)
    bus.off(EVENT_KEY.TOGGLE_CURRENT_VIDEO, togglePlay)
    bus.off(EVENT_KEY.TOOL_CLICK_BROADCAST, toolCallback)
    // bus.off(EVENT_KEY.DOUBLE_CLICK, dbclick)
})
onDeactivated(() => {
    bus.emit(EVENT_KEY.TOGGLE_CURRENT_VIDEO)
})
onActivated(() => {
    if (route.query.back || neddRefresh) {
        refresh()
    }
    bus.emit(EVENT_KEY.TOGGLE_CURRENT_VIDEO)
})
</script>
<style lang="scss" scoped>
.videos {
    height: 100%;

    .muted-icon {
        position: absolute;
        top: 50px;
        left: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 68px;
        height: 67px;
        border-radius: 50%;
        z-index: 40;

        img {
            width: 40px;
            height: 40px;
        }
    }

    .foru-icon {
        display: inline-block;
        width: 49px;
        height: 35px;
        margin-right: 10px;
        background: url('@/assets/img/foru/jl.png') no-repeat;
        background-size: 100% 100%;

        transform: translateY(4px);
    }
    .foru-title {
        font-size: 36px;
        font-weight: bold;
        line-height: 30px;
        color: #fff;
    }

    .video-wrap {
        height: calc(var(--vh, 1vh) * 100 - var(--footer-height) - var(--safe-height)) !important;
        overflow: hidden;
        .swiper,
        .swiper-wrapper,
        .swiper-slide {
            width: 100%;
            height: 100%;
        }
    }
}
.unSuport {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.driver {
    position: absolute;
    width: 194px;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);

    .driver-tip {
        margin-top: 20px;
        font-size: 36px;
        line-height: 40px;
        color: #ffffff;
        text-align: center;
    }
    .driver-point {
        position: absolute;
        left: 90px;
        top: 183px;
        width: 194px;
        height: 177px;
        animation: slide 2s ease 3;
    }
}
@keyframes slide {
    0% {
        transform: translate3d(0, 0, 0);
    }
    100% {
        transform: translate3d(0, -60px, 0);
    }
}
</style>
