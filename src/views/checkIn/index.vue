<template>
    <div class="check">
        <div class="check-top">
            <div class="banner">
                <p>{{ $t('Sign_in_banner_copy') }}</p>
                <div class="back" @click="router.back()"></div>
            </div>
            <div class="tip">
                {{ $t('There will be a certain time delay in recording game bets,which will not exceed 5 minutes.') }}
            </div>
        </div>
        <div class="check-list-wrap">
            <div class="check-list">
                <div class="list-header grid grid-cols-4">
                    <div class="table-td" v-for="item in listTitle" :key="item">{{ $t(item) }}</div>
                </div>
                <div ref="listDom" class="list-content">
                    <div
                        :class="['grid grid-cols-4 table-tr', `list${index}`, { active: dayjs().isSame(dayjs(item.date), 'day') }]"
                        v-for="(item, index) in list"
                        :key="index"
                    >
                        <div class="table-td">{{ item.date }}</div>
                        <div class="table-td">
                            {{ getTimeStatus(item) === 3 ? '? ?' : item.rewardnum }}
                            {{ getTimeStatus(item) === 3 ? '' : item.rewardtype === 'gold' ? store.cureencySymbol(currency) : 'bonus' }}
                        </div>
                        <div class="table-td">{{ getRequired(item) }}</div>
                        <div class="table-td">
                            <div v-if="getTimeStatus(item) !== 3 && item.pick === 1" class="claim claimed" @click="receiveReward(item, index)">
                                {{ $t('Claim') }}
                            </div>
                            <div v-else-if="getTimeStatus(item) !== 3 && item.pick != 1" class="claim">
                                {{ item.pick == 0 || !item.pick ? $t('Claim') : $t('Claimed') }}
                            </div>
                            <div v-else class="claim-tip">{{ $t('Stay tuned') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { useRouter, useRoute } from 'vue-router'
import { useBaseStore } from '@/stores'
import { Activity } from '@/stores/types'
import { useI18n } from 'vue-i18n'

const listTitle = ['Day', 'Rewards', 'Require', 'Status']

const listDom = ref(null)
const active = ref(0)
const router = useRouter()
const route = useRoute()
const store = useBaseStore()
const i18n = useI18n()
const currency = ref('')
const list = ref([])
const baseInfo = ref<Activity>({})

const reqireOption = {
    bet: 'bet',
    pay: 'Deposit',
    login: 'log in',
}
const getRequired = (item) => {
    if (getTimeStatus(item) === 3) {
        return 'Stay tuned'
    }
    if (item.conditiontype === 'login') {
        return 'login'
    }
    return `${reqireOption[item.conditiontype]} ${item.condition} ${item.conditionvalue}`
}
const getTimeStatus = (item) => {
    const today = dayjs()
    return today.isAfter(item.date, 'day') ? 1 : today.isBefore(item.date, 'day') ? 3 : 2
}

const showToay = () => {
    const today = dayjs()
    const fToday = today.format('YYYY-MM-DD')
    const idx = list.value.findIndex((item) => item.day === fToday)
    active.value = idx

    nextTick(() => {
        const el = document.querySelector(`.list${idx}`)
        if (el) {
            el.scrollIntoView({ behavior: 'smooth' })
        }
    })
}
const getData = async () => {
    try {
        const res = await store.getSignEvent({ id: 'signActivitiy', version: route.query.id as string })
        if (res.code === 200) {
            // @ts-ignore
            list.value = res.acticity.activity?.context?.sort((a, b) => a.index - b.index) || []
            currency.value = res.acticity.activity.currency
            baseInfo.value = res.acticity.activity
            showToay()
        }
    } catch (e) {
        console.log(e)
    }
}
const receiveReward = async (item, index) => {
    if (item.pick === 2) {
        return
    }
    try {
        const res = await store.ReceiveRewards({
            id: 'signActivitiy',
            content: {
                actid: baseInfo.value._id,
                cid: item._id,
            },
        })
        if (res.code === 200) {
            const item = list.value[index]
            list.value[index] = { ...item, pick: 2 }
            showToast(i18n.t('Successful_collection'))
        }
    } catch (e) {
        console.log(e)
    }
}
onBeforeMount(() => {
    getData()
})
onActivated(() => {
    getData()
})
</script>
<style lang="scss" scoped>
$topH: 468px;

.check {
    .banner {
        position: relative;
        height: 340px;
        background: url('@/assets/img/events/check_bg.jpg') no-repeat;
        background-size: 100% 100%;

        p {
            width: 489px;
            padding-top: 180px;
            padding-left: 80px;
            font-size: 36px;
            font-weight: bold;
            font-style: italic;
            font-stretch: normal;
            line-height: 52px;
            letter-spacing: -1px;
            text-align: left;
            color: #ffffff;
        }
        .back {
            position: absolute;
            top: 88px;
            left: 30px;
            width: 60px;
            height: 60px;
            background: url('@/assets/img/events/back.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .check-top {
        height: $topH;

        .tip {
            margin-top: 30px;
            padding: 0 30px;
            font-size: 24px;
            color: #40cf54;
        }
    }
    .check-list-wrap {
        padding: 0 15px;
    }
    .check-list {
        padding: 6px 6px 0;
        font-size: 24px;
        background: #fff;
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
        background: #2a2b31;
        overflow: hidden;

        .list-header {
            border-top-left-radius: 30px;
            border-top-right-radius: 30px;
            background-color: #454548;
            font-size: 28px;
            color: #ffd145;
            text-align: center;
            .table-td {
                height: 88px;
                line-height: 88px;
            }
        }
        .list-content {
            padding: 0 4px;
            height: calc(var(--vh, 1vh) * 100 - $topH - 88px - var(--safe-height)) !important;
            overflow: auto;
        }
        .table-tr {
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            &.active {
                background-color: #80aee9;
            }
        }
        .table-td {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 79px;
            line-height: 79px;
            text-align: center;
            color: #fff;

            .claim {
                width: 148px;
                height: 48px;
                line-height: 48px;
                background-image: linear-gradient(135deg, #6c6c6c 0%, #5b5a5e 100%), linear-gradient(#ffa72a, #ffa72a);
                border-radius: 24px;
                text-align: center;
                font-size: 24px;
                color: #fff;
            }
            .claimed {
                background-image: linear-gradient(135deg, #40cf54 0%, #0d9041 100%), linear-gradient(#ffa72a, #ffa72a);
            }
        }
    }
}
</style>
