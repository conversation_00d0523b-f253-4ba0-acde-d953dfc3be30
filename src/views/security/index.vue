<template>
    <div class="security">
        <Header />
        <div class="security-content">
            <!-- <div class="base-line">
                <SysItem :showIcon="false" v-for="item in baseConfig" :key="item.name" :item="item" @click="handleCLick(item)">
                    <template #right>
                        <div class="tip opacity-40" v-if="!userInfo[item.key]">{{ $t('Not_Set') }}</div>
                        <div class="tip" v-else>{{ secutiteName(userInfo[item.key], item.type) }}</div>
                    </template>
                </SysItem>
            </div> -->
            <div class="base-line">
                <SysItem path="/login" v-for="item in thirdLink" :key="item.name" :item="item" @click="handleCLick(item)">
                    <template #right>
                        <div class="tip link" v-if="!userInfo[item.key]">{{ $t('link') }}</div>
                        <div class="tip" v-else>{{ secutiteName(userInfo[item.key]?.nickname, item.type) }}</div>
                    </template>
                </SysItem>
            </div>
            <div class="base-line">
                <SysItem :showIcon="false" v-for="item in otherConfig" :key="item.name" :item="item" @click="handleCLick(item)" />
            </div>
        </div>
        <!-- <BindDialog v-model="showBind" /> -->
    </div>
</template>
<script lang="ts" setup>
import Header from '@/components/Header.vue'
import SysItem from '@/components/me/list.vue'
// import BindDialog from '@/components/dialog/BindDialog.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { H5Login } from '@/utils/H5Login'
import eventbus from '@/utils/bus'
import * as service from '@/api'
import { encode } from 'js-base64'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { decodeName } from '@/utils'

const path = '../../assets/img/security/'
const file = import.meta.glob('../../assets/img/security/*', { eager: true })

const getImg = (name) => {
    return file[path + name].default
}
const store = useBaseStore()
const i18n = useI18n()
const router = useRouter()
const route = useRoute()
const { userInfo } = storeToRefs(store)
const showBind = ref(false)
// const baseConfig = [
//     // {
//     //     name: 'KYC Setting',
//     //     type: 1,
//     // },
//     {
//         name: 'Reg_Mob_phone_No',
//         type: 2,
//         key: 'telephone',
//     },
//     // {
//     //     name: 'email',
//     //     type: 3,
//     //     key: 'linkmail',
//     // },
// ]
const thirdLink = [
    {
        name: 'Link_with_Facebook',
        type: 4,
        icon: getImg('facebook.png'),
        key: 'linkfacebook',
    },
    {
        name: 'Link_with_Google',
        type: 5,
        icon: getImg('google.png'),
        key: 'linkgoogle',
    },
]
const otherConfig = [
    {
        name: 'Terms_and_Service',
        type: 6,
        link: 'https://betfugu.com/active/ToS.html',
    },
    {
        name: 'Privacy_Policy',
        type: 7,
        link: 'https://betfugu.com/active/privacy.html',
    },
]

// 绑定手机
const bindPhone = () => {
    showBind.value = true
}

// 绑定邮箱
const bindEmail = () => {}
// 绑定Facebook
const bindFacebook = () => {
    H5Login.loginWithFacebook()
}
// 绑定谷歌
const bindGooGle = () => {
    H5Login.enableGoogleSignInPrompt()
}

const handleConfig = {
    2: bindPhone,
    3: bindEmail,
    4: bindFacebook,
    5: bindGooGle,
}

const handleCLick = (item) => {
    if (item.key && ![6, 7].includes(item.type) && userInfo.value[item.key]) {
        return
    }
    switch (item.type) {
        case 3:
            router.push('/email')
            break
        case 6:
        case 7:
            window.open(item.link)
            break
        default:
            handleConfig[item.type]()
            break
    }
}
const handleBind = (data, key) => {
    store.$patch((state) => {
        state.userInfo = { ...userInfo.value, [key]: data }
    })
}
const loginCallback = (data: LoginData) => {
    if (route.path !== '/security') {
        return
    }
    if (data.from == 'facebook') {
        C2S_FacebookBind(data)
    } else if (data.from == 'google') {
        C2S_GoogleBind(data)
    }
}
// facebook 登录
const C2S_FacebookBind = async (fbinfo) => {
    if (fbinfo.status == 'failed') {
        return console.log('facebook 绑定失败')
    }
    const param = {
        unionid: fbinfo.id,
        nickname: encode(fbinfo.name),
        accesstoken: fbinfo.accesstoken,
    }
    try {
        const fbResp = await service.facebookBind(param)
        if (fbResp.code === 200) {
            handleBind(fbResp.data, 'linkfacebook')
        } else {
            showToast(i18n.t('Account_binding_fail'))
        }
    } catch (e) {
        showToast(i18n.t('Account_binding_fail'))
    }
}
// goole 登录
const C2S_GoogleBind = async (googleInfo) => {
    if (googleInfo == 'failed') {
        return
    }
    const param = {
        unionid: googleInfo.id,
        nickname: encode(googleInfo.name),
        accesstoken: googleInfo.accesstoken,
        clientid: googleInfo.client_id,
    }
    try {
        const ggResp = await service.googleBind(param)
        if (ggResp.code === 200) {
            handleBind(ggResp.data, 'linkgoogle')
        } else {
            showToast(i18n.t('Account_binding_fail'))
        }
    } catch (e) {
        showToast(i18n.t('Account_binding_fail'))
    }
}
const secutiteName = (nickname, type) => {
    switch (type) {
        case 2:
            const half = Math.floor(nickname.length / 2)
            return nickname.slice(0, half) + '****' + nickname.slice(half + 4)
        case 3:
            const name = nickname.nickname
            const idx = name.indexOf('@')
            return name.slice(0, 1) + '*'.repeat(idx - 1) + name.slice(idx)
        case 4:
        case 5:
            const tname = decodeName(nickname)
            const hf = Math.floor(tname.length / 2)
            return tname.slice(0, hf) + '*'.repeat(3) + tname.slice(hf + 3)
        default:
            break
    }
}
onMounted(() => {
    H5Login.init()
    eventbus.on(H5Login.H5_Login_Back, loginCallback)
})
onBeforeUnmount(() => {
    eventbus.off(H5Login.H5_Login_Back, loginCallback)
})
</script>
<style lang="scss" scoped>
.security {
    .security-content {
        padding: 20px 20px 0;
        height: calc(var(--vh, 1vh) * 100 - var(--header-height)) !important;
        overflow: auto;
    }
    .base-line {
        margin-bottom: 20px;
        padding: 9px 35px;
        background-color: rgba(58, 53, 51, 0.6);
        border-radius: 20px;

        :deep(.sys-item) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.6);

            &:last-child {
                border-bottom: none;
            }
        }
    }
    .tip {
        font-size: 26px;
        color: #fff;

        &.link {
            color: #527dd0;
        }
    }
}
</style>
