<template>
    <div class="messgae">
        <Header> </Header>
        <div class="message-wrap">
            <div class="inner-wrap">
                <van-pull-refresh
                    class="fresh-wrap"
                    v-model="listParams.refresh"
                    :pulling-text="$t('Pull_down_to_refresh')"
                    :loosing-text="$t('Pull_down_to_refresh')"
                    :loading-text="$t('Loading_please_wait')"
                    @refresh="onRefresh"
                >
                    <van-list
                        v-model:loading="listParams.loading"
                        :finished="listParams.finished"
                        :finished-text="$t('No_more')"
                        :loading-text="$t('Loading_please_wait')"
                        @load="onLoad"
                        :immediate-check="false"
                    >
                        <div class="unread" v-for="(item, index) in listParams.data" :key="index" @click="goChat(item)">
                            <div class="unread-avatar"><img :src="item.avatar" /></div>
                            <div class="unread-content">
                                <div class="unread-name">{{ decodeName(item.nickname) }}</div>
                                <div class="unread-message">
                                    <div class="elipsis" v-html="$t(item.content.text, translateTtoText(item))"></div>
                                </div>
                            </div>
                            <div class="time">{{ dayjs(item.time).format('HH:mm A') }}</div>
                            <div v-if="getCount(item.uid)" :class="['unread-count', `unread-count${getClsCount(item.uid)}`]">
                                {{ getCount(item.uid) > 99 ? '99+' : getCount(item.uid) }}
                            </div>
                        </div>
                        <van-empty class="empty-tip" v-if="listParams.finished && !listParams.data.length">{{ $t('No_more_content') }}</van-empty>
                    </van-list>
                </van-pull-refresh>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import Header from '@/components/Header.vue'
import { useBaseStore } from '@/stores'
import { useRouter } from 'vue-router'
import { Chat } from '@/stores/types'
import { decodeName, getChannel } from '@/utils'
import dayjs from 'dayjs'
import bus, { EVENT_KEY } from '@/utils/bus'
import { encode } from 'js-base64'
import { useI18n } from 'vue-i18n'
import { useSubscript } from '@/components/notice/hook/useSubscribe'

const store = useBaseStore()
const router = useRouter()
const i18n = useI18n()
const { translateTtoText } = useSubscript('')

const uids = ref([])
const channels = ref([])
const listParams = reactive({
    loading: false,
    disabled: false,
    finished: false,
    refresh: false,
    data: [],
    pageNum: 1,
    pageSize: 10,
})
const startPos = computed(() => (listParams.pageNum - 1) * 10)
const endPos = computed(() => (listParams.pageNum - 1) * 10 + listParams.pageSize)

watch(store.chatList, () => {}, {
    deep: true,
})

const getUnreadList = async () => {
    const res = await store.getMsgUnread()
    if (res.code === 200) {
        const unreadChannel = Object.keys(res.unreadmessages || {})
        const readedChannel = res.channels.filter((channel) => !unreadChannel.includes(channel))
        const listChannel = (channels.value = [...unreadChannel, ...readedChannel])
        uids.value = listChannel.map((channel) => {
            const [, sender, reciever] = channel.match(/^(\d+):(\d+)$/)
            return store.userInfo.uid == sender ? reciever : sender
        })
    }
}
const getCount = (uid) => {
    return store.unReadCount[getChannel(uid, store.userInfo.uid)] || 0
}
const getClsCount = (uid) => {
    const count = getCount(uid)
    return count > 99 ? 3 : count < 10 ? 1 : 2
}
const goChat = (item) => {
    store.$patch((state) => {
        state.unReadCount[item.channel] = 0
    })
    router.push({
        path: '/chat',
        query: {
            nickname: decodeName(item.nickname),
            userID: item.uid,
            isRs: Number(item.reseller),
        },
    })
}
const onLoad = async () => {
    listParams.loading = true
    try {
        const res = await store.getHeadermessage({
            uids: uids.value.slice(startPos.value, endPos.value),
            channels: channels.value.slice(startPos.value, endPos.value),
        })
        if (res.code === 200) {
            if (res.users[10000]) {
                res.users[10000] = {
                    avatar: 'https://betfugu.com/static/system.png',
                    nickname: encode(i18n.t('System_message')),
                    uid: 10000,
                }
            }
            if (res.users[20000]) {
                res.users[20000] = {
                    avatar: 'https://betfugu.com/static/transition.png',
                    nickname: encode(i18n.t('Transaction_message')),
                    uid: 20000,
                }
            }
            const list = Object.values(res.users)
                .filter((item) => item.uid && item.uid !== 10001)
                .map((item) => {
                    const uid = [item.uid, store.userInfo.uid].sort().join(':')
                    const hotdata = res.hotdatas[uid]
                    const chat = hotdata ? hotdata[0] : ''
                    const chatMsg = chat ? (JSON.parse(chat) as Chat) : {}
                    return { ...item, ...chatMsg }
                })
            listParams.data = listParams.pageNum === 1 ? [...list] : [...listParams.data, ...list]
            if (list.length < listParams.pageSize - 1) {
                listParams.finished = true
            }
            listParams.pageNum = listParams.pageNum + 1
        }
    } finally {
        listParams.loading = false
    }
}
const onRefresh = async () => {
    listParams.pageNum = 1
    await getUnreadList()
    await onLoad()
    listParams.refresh = false
    listParams.loading = false
    listParams.finished = false
}
const updateMsg = async (msg) => {
    const channel = msg.channel

    const index = listParams.data.findIndex((item) => {
        const { receiver, sender } = item.content
        return getChannel(receiver, sender) === channel
    })
    if (index === -1) {
        const sender = msg.content.sender == store.userInfo.uid ? msg.content.receiver : msg.content.sender
        const res = await store.getHeadermessage({
            uids: [sender],
            channels: [msg.channel],
        })
        let newMsg = {}
        if (res.code === 200) {
            newMsg = { ...res.users[sender], ...msg }
        }
        listParams.data.push(newMsg)
    } else {
        const oldMsg = listParams.data[index]
        listParams.data[index] = { ...oldMsg, ...msg }
    }
}
const handleLogout = () => {
    listParams.pageNum = 1
    listParams.data = []
}
onBeforeMount(async () => {
    bus.on(EVENT_KEY.MESSAGE_UPDATE, updateMsg)
    bus.on(EVENT_KEY.LOGOUT, handleLogout)
})
onBeforeUnmount(() => {
    bus.off(EVENT_KEY.MESSAGE_UPDATE, updateMsg)
    bus.off(EVENT_KEY.LOGOUT, handleLogout)
})

onActivated(async () => {
    await getUnreadList()
    await onLoad()
})
</script>
<style lang="scss" scoped>
.messgae {
    .message-wrap {
        padding: 30px;
        height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height)) !important;

        .inner-wrap {
            height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height) - 40px) !important;
            overflow: auto;
        }
        .social {
            display: flex;
            gap: 20px;
            padding: 0 25px 0 18px;
            height: 86px;
            background: url('@/assets/img/chat/social_bg.png') no-repeat;
            background-size: 100% 100%;

            .tip {
                flex: 1;
                line-height: 86px;
                font-size: 26px;
                font-weight: bold;
                letter-spacing: 1px;
                color: #fff;
            }
            .social-btn {
                display: flex;
                width: 204px;
                height: 100%;

                .btn {
                    flex: 1;
                }
            }
        }
        .unread {
            position: relative;
            display: flex;
            // gap: 21px;
            padding: 16px 10px;
            height: 160px;
            margin: 20px 0;
            background-color: #3d3a38;
            border-radius: 30px;
            .unread-avatar {
                flex-shrink: 0;
                margin-right: 21px;
                width: 132px;
                height: 132px;
                border-radius: 50%;
                overflow: hidden;
                border: 8px solid #7b746b;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            .unread-content {
                flex: 1;
                max-width: 80%;
                padding-right: 76px;
            }
            .unread-name {
                font-size: 30px;
                font-weight: bold;
                color: #fff;
            }

            .unread-message {
                font-size: 24px;
                font-weight: bold;
                color: #7e776e;
                div {
                    max-width: 100%;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;

                    :deep(.text-yellow) {
                        color: #e5ff06;
                    }
                }
            }
        }
        .time {
            position: absolute;
            right: 20px;
            bottom: 15px;
            font-size: 20px;
            font-weight: Bold;
            color: #7e776e;
        }
        .unread-count {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 36px;
            height: 36px;
            background: url('@/assets/img/chat/m_bg1.png') no-repeat;
            background-size: 100% 100%;
            font-size: 22px;
            font-weight: bold;
            color: #fff;
            text-align: center;
        }
        .unread-count2 {
            width: 46px;
            background-image: url('@/assets/img/chat/m_bg2.png');
        }
        .unread-count3 {
            width: 56px;
            background-image: url('@/assets/img/chat/m_bg3.png');
        }
    }
    // .fresh-wrap {
    //     height: 100%;
    // }
}
// :deep(.van-pull-refresh) {
//     overflow: inherit;
// }
</style>
