<template>
    <div class="chrismas">
        <div class="back-btn" @click="router.back()"></div>
        <div class="title-wrap">
            <div class="title">
                {{ $t('Cashback_Magic') }}
                <div class="title-right">
                    <p class="right-title">{{ $t('Daily') }}</p>
                    <p class="right-num">{{ percent }}</p>
                    <div class="title-rule" @click="goDetail"></div>
                </div>
            </div>
        </div>
        <div class="returned-wrap">
            <div class="returned-time">{{ $t('Event_Time') }}: {{ getEnMd(startTime) }}-{{ getEnMd(endTime) }}</div>
            <div class="returned-title">{{ isStartDasy ? $t('Total_amount_expecte_return') : $t('Total_amount_returne') }}</div>
            <div class="returned-amount">
                <NumberAnimation
                    v-if="totalNumber"
                    class="animate-num transition"
                    ref="numberCom"
                    :format="theFormat"
                    :to="numberTo"
                    :from="numberFrom"
                    animationPaused
                    :duration="duration"
                    @complete="completed"
                >
                </NumberAnimation>
            </div>
            <div class="returned-tip">{{ $t('Cashback_reward_info') }}</div>
        </div>
        <div class="chrimas-pic">
            <img src="@/assets/img/cash_back/bg.png" />
        </div>
        <div class="info-area">
            <div class="info-title">
                <div class="info-title-left">{{ $t('My_Rewards') }}</div>
                <div class="info-title-right">{{ $t('Daily_Cashback') }}</div>
            </div>
            <div class="info-detail">
                <div class="detail-amount">
                    {{ store.cureencySymbol() }}<span>{{ isStartDasy ? '??.??' : callBackCount }}</span>
                </div>
                <div class="detail-right">
                    <div v-if="btnStatus === 1" class="btn opacity-50">{{ $t('Claim') }}</div>
                    <div v-else-if="btnStatus === 2" class="btn" @click="handleClaim">{{ $t('Claim') }}</div>
                    <div v-else class="btn clamed opacity-50">{{ $t('Received') }}</div>
                    <div class="time">{{ $t('Claimable_time') }}: <van-count-down :time="cutdownTime" /></div>
                </div>
            </div>
            <div class="info-bot">
                <div class="bot-left">{{ $t('Total_Cashback') }}</div>
                <div class="bot-center">{{ store.cureencySymbol() }}{{ callBackTotal }}</div>
                <div class="bot-right" @click="popVisible = true">{{ $t('Details') }}</div>
            </div>
        </div>
        <van-popup class="confirm-pop" v-model:show="popVisible" position="bottom">
            <div class="confirm-title">
                {{ $t('Cashback_Details') }}
                <div class="close" @click="popVisible = false"></div>
            </div>
            <div class="pop-content-title">
                <div v-for="item in tab" :key="item">{{ $t(item) }}</div>
            </div>
            <div class="pop-content">
                <ScrollList ref="scroll" :api="getList" :disableRefresh="false" @onChange="getRecordList">
                    <template #default>
                        <template v-if="!isEmpty">
                            <div class="record-item" v-for="item in recordList" :key="item">
                                <p>
                                    {{ dayjs(`${item._source.day}`).format('DD/MM/YYYY') }}
                                </p>
                                <p>{{ item._source.addgold }}</p>
                            </div>
                        </template>
                        <van-empty class="empty-tip" v-else :description="$t('Tip_text_receipt_record_info')"></van-empty>
                    </template>
                </ScrollList>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="ts" name="christmasEvent">
import ScrollList from '@/components/ScrollList.vue'
import NumberAnimation from 'vue-number-animation'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import { getRebateLogs } from '@/api/index'
import { getAccountInfo } from '@/api/wallet'
import { useI18n } from 'vue-i18n'

dayjs.extend(advancedFormat)
const store = useBaseStore()
const i18n = useI18n()

const router = useRouter()
const popVisible = ref(false)

const duration = ref(1)
const numberFrom = ref(0)
const numberTo = ref(0)
let totalNumber = ref(0)
let perNum = 0
const numberCom = ref(null)
const isEmpty = ref(false)
const activity = ref({})
const cutdownTime = ref(0)

const tab = ['Date', 'Cashback']
const recordList = ref([])

const percent = computed(() => {
    return `${activity.value?.rebateConfg?.rebatepercentage * 100 || 0}%`
})
const startTime = computed(() => activity.value?.rebateConfg?.begintime)
const endTime = computed(() => activity.value?.rebateConfg?.endtime)
const callBackCount = computed(() => activity.value?.user?.addgold.toFixed(2))
const callBackTotal = computed(() => activity.value?.user?.totalrebate.toFixed(2))
const btnStatus = computed(() => {
    if (activity.value?.user?.picked === 1) {
        return 3
    }
    if (activity.value?.user?.addgold) {
        return 2
    }
    return 1
})
const isStartDasy = computed(() => {
    return dayjs().isSame(dayjs(startTime.value), 'day')
})
const getRecordList = (list, state) => {
    recordList.value = state.reset ? [...list] : [...recordList.value, ...list]
}
const theFormat = (num) => {
    return num.toFixed(2)
}
const completed = () => {
    if (numberTo.value >= totalNumber.value || isStartDasy.value) {
        return
    }
    setTimeout(() => {
        numberTo.value = numberTo.value + getDisNum()
        if (numberTo.value > totalNumber.value) {
            numberTo.value = totalNumber.value
        }
    }, 4000)
}
const goDetail = () => {
    router.push('/christmas-rule')
}
const getEnMd = (time) => {
    const date = dayjs(time)
    return `${date.format('MMMM Do')}`
}
const initNumber = () => {
    // 获取当前时间
    const now = dayjs()
    // 获取当天零点的时间
    const startOfDay = dayjs().startOf('day')
    // 计算从零点到现在过去了多少秒
    if (isStartDasy.value) {
        numberFrom.value = totalNumber.value
        numberTo.value = totalNumber.value
    } else {
        const secondsPassed = now.diff(startOfDay, 'second')
        numberFrom.value = secondsPassed * perNum
        numberTo.value = numberFrom.value + getDisNum()
    }
    nextTick(() => {
        numberCom.value.play()
    })
}
const getDisNum = () => {
    const dis = perNum * 5
    const min = dis - 20
    const max = dis + 20
    return Math.random() * (max - min + 1).toFixed(2) + min
}
const getList = async () => {
    // const { page, pageSize } = params
    try {
        const res = await getRebateLogs({}, false)
        if (res.code === 200) {
            isEmpty.value = res.hits.length === 0
            return res.hits
        } else {
            return []
        }
    } catch (e) {
        return []
    }
}
const getChrismasEvent = async () => {
    const res = await store.getSignEvent(
        {
            id: 'rebate',
        },
        false,
        false
    )
    if (res.code === 200) {
        activity.value = res.acticity
        const { dailytotalreward, endtime } = res.acticity.rebateConfg
        totalNumber.value = dailytotalreward * 100
        perNum = parseInt(totalNumber.value / 24 / 60 / 60)
        cutdownTime.value = dayjs(endtime).valueOf() - dayjs().valueOf()
        initNumber()
    }
}
const handleClaim = async () => {
    const acountRes = await getAccountInfo(false)
    if (acountRes.code === 200 && !!acountRes.data.content) {
        const res = await store.ReceiveRewards({
            id: 'rebate',
        })
        if (res.code === 200) {
            showToast(i18n.t('Successful_collection'))
            // activity.value.user.addgold = 0
            activity.value.user.picked = 1
        }
    } else {
        showDialog({
            message: i18n.t('Tips_for_binding_gcash'),
            className: 'common-dialog',
            showCancelButton: false,
            confirmButtonText: i18n.t('Confirm'),
            allowHtml: true,
            messageAlign: 'left',
        })
    }
}
onBeforeMount(async () => {
    getChrismasEvent()
    const acountRes = await getAccountInfo(false)
    console.log(acountRes, 'acountRes')
})
</script>
<style lang="scss" scoped>
.chrismas {
    height: calc(var(--vh, 1vh) * 100) !important;
    overflow: auto;
    background: linear-gradient(#0f101f, #e53a3c);
    font-family: MicrosoftYaHei;
    .back-btn {
        min-width: 60px;
        position: absolute;
        top: calc(75px + var(--safe-height));
        left: 30px;
        width: 40px;
        height: 40px;
        background: url('@/assets/img/header/back.png') no-repeat center center;
        background-size: 22px 40px;
    }
    .title-wrap {
        padding-top: calc(50px + var(--safe-height));
        text-align: center;
        font-family: MicrosoftYaHeiHeavy;
        font-size: 55px;
        color: #ffffff;
        .title {
            position: relative;
            display: inline-block;
            font-weight: 800;

            .title-right {
                position: absolute;
                top: 25%;
                right: 0;
                width: 104px;
                height: 84px;
                background: url('@/assets/img/cash_back/float_bg.png') no-repeat;
                background-size: 100% 100%;

                transform: translate3d(100%, 0, 0);

                .right-title {
                    font-weight: bold;
                    font-size: 20px;
                }
                .right-num {
                    position: relative;
                    top: -4px;
                    font-size: 32px;
                    font-weight: 800;
                    letter-spacing: -2px;
                    color: #ffa72a;
                }
                .title-rule {
                    position: absolute;
                    top: 0;
                    right: -40px;
                    width: 40px;
                    height: 40px;
                    background: url('@/assets/img/cash_back/question.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
    .returned-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;

        .returned-time {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
        }
        .returned-title {
            font-size: 24px;
            font-weight: bold;
            color: #ffa72a;
        }
        .returned-amount {
            position: relative;
            width: 640px;
            height: 100px;
            background: url('@/assets/img/cash_back/kuang.png') no-repeat;
            background-size: 100% 100%;
            border-radius: 20px;
            text-align: center;
            font-size: 0px;
            .animate-num {
                font-size: 72px;
                letter-spacing: 7px;
                color: #fffd65;
                font-family: 'Avenir', Helvetica, Arial, sans-serif;
                -webkit-font-smoothing: antialiased;
                font-weight: 600;
            }
        }
        .returned-tip {
            margin: 14px 0;
            font-size: 20px;
            font-weight: bold;
            color: #ce5e66;
        }
    }
    .chrimas-pic {
        height: 770px;
    }
    .info-area {
        position: relative;
        top: -25px;
        margin: 0 auto;
        width: 708px;
        height: 390px;
        background: url('@/assets/img/cash_back/list_bg.png') no-repeat;
        background-size: 100% 100%;
    }
    .info-area {
        .info-title {
            display: flex;
            height: 100px;

            .info-title-left,
            .info-title-right {
                flex: 1;
            }

            .info-title-left {
                padding-left: 60px;
                font-size: 30px;
                color: #ffffff;
                display: flex;
                align-items: center;

                &::before {
                    display: inline-block;
                    content: '';
                    margin-right: 10px;
                    width: 48px;
                    height: 48px;
                    background: url('@/assets/img/cash_back/gift.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            .info-title-right {
                padding-top: 50px;
                font-size: 36px;
                font-weight: bold;
                color: #6d6d6d;
            }
        }
        .info-detail {
            position: relative;
            display: flex;
            margin: 0 auto;
            width: 620px;
            height: 180px;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background-color: #c6c6c6;
                border-radius: 2px;
            }
            .detail-amount {
                flex: 1;
                line-height: 180px;
                font-size: 72px;

                color: #333333;

                span {
                    font-weight: bold;
                }
            }
            .detail-right {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 20px;

                .btn {
                    width: 240px;
                    height: 72px;
                    line-height: 72px;
                    background: linear-gradient(100deg, #1acf9a 0%, #13b187 45%, #0c9274 100%), linear-gradient(#6d6d6d, #6d6d6d);
                    border-radius: 36px;
                    text-align: center;
                    font-family: MicrosoftYaHei;
                    font-size: 30px;
                    color: #ffffff;
                    &.clamed {
                        background: #6d6d6d;
                    }
                }
                .time {
                    margin-top: 10px;
                    line-height: 20px;
                    display: flex;
                    align-items: center;
                    font-size: 20px;
                    color: #6d6d6d;
                    :deep(.van-count-down) {
                        font-family: 'Avenir', Helvetica, Arial, sans-serif;
                        -webkit-font-smoothing: antialiased;
                        font-weight: 500;
                        font-size: 20px;
                        color: #6d6d6d;
                    }

                    &::before {
                        position: relative;
                        top: -2px;
                        content: '';
                        display: inline-block;
                        margin-right: 3px;
                        width: 22px;
                        height: 22px;
                        background: url('@/assets/img/cash_back/clock.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }
            }
        }
        .info-bot {
            display: flex;
            align-items: center;
            width: 620px;
            height: 100px;
            margin: 0 auto;

            .bot-left {
                font-family: MicrosoftYaHei;
                font-size: 30px;
                font-weight: normal;
                letter-spacing: 0px;
                color: #6d6d6d;
            }
            .bot-center {
                flex: 1;
                margin: 0 10px;
                font-size: 30px;
                color: #ffa72a;
                text-align: center;
            }
            .bot-right {
                position: relative;
                font-size: 30px;
                color: #0d9777;

                &::before,
                &::after {
                    content: '';
                    display: inline-block;
                }
                &::after {
                    margin-left: 5px;
                    width: 26px;
                    height: 26px;
                    background: url('@/assets/img/cash_back/entry_tip.png') no-repeat;
                    background-size: 100% 100%;
                }
                &::before {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 124px;
                    height: 3px;
                    background-color: #0d9777;
                    border-radius: 2px;
                }
            }
        }
    }
}
.transition {
    transition: all 0.3s ease-in;
}
.confirm-pop {
    width: 684px;
    height: 812px;
    left: 50%;
    margin-left: -342px;
    background-color: #ffffff;
    border-radius: 30px 30px 0px 0px;
}

.confirm-title {
    position: relative;
    height: 100px;
    line-height: 100px;
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    color: #6d6d6d;

    .close {
        position: absolute;
        top: 30px;
        right: 53px;
        width: 33px;
        height: 34px;
        background: url('@/assets/img/wallets/close.png') no-repeat;
        background-size: 100% 100%;
    }
}
.pop-content {
    height: 652px;
    overflow: auto;
}
.pop-content-title,
.record-item {
    @apply grid grid-cols-2;
}
.pop-content-title {
    height: 60px;
    line-height: 60px;
    background-image: linear-gradient(rgba(180, 38, 255, 0.4), rgba(180, 38, 255, 0.4)),
        linear-gradient(150deg, #c240a4 0%, #b22fb9 40%, #a11dce 100%);
    font-size: 26px;
    color: #ffffff;
    text-align: center;
}
.record-item {
    p {
        height: 90px;
        line-height: 90px;
        text-align: center;
        font-size: 30px;
        letter-spacing: -2px;
        color: #6d6d6d;
        border-bottom: 2px solid #c6c6c6;

        &:first-child {
            border-right: 2px solid #c6c6c6;
        }
    }
    &:first-child {
        p {
            border-top: 2px solid #c6c6c6;
        }
    }
}
</style>
