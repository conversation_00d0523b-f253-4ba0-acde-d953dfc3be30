<template>
    <div class="menu-title">
        <button class="menu-close" @click="goback">
            <img src="@/assets/img/new-home/newsetting/back.svg" alt="x" />
        </button>
        <div class="nav-title">VIP Exclusive Gifts</div>
    </div>
    <div class="vip-page">
        <div class="vip-content">
            <div>
                <img class="vip-bg" src="@/assets/img/vip/vip_bg_1.png" />
            </div>
            <!-- <div class="grade-card">
                <div v-if="vipInfo.curLevel != 0" :class="['grade-icon', `vip-${vipInfo.curLevel}`]"></div>
                <VipCard />
                <div v-if="showMatian" class="line card-line"></div>
                <VipCard v-if="showMatian" :type="1" />
            </div>
            <div class="line vip-line"></div> -->
            <div class="rule">
                <div class="rule-title">{{ $t('vip_rule_title') }}</div>
                <div class="rule-content" v-html="$t('vip_rule_details')"></div>
            </div>
            <RuleTable />
            <!-- <div class="line table-line"></div> -->
            <!-- <RuleTable :type="1" /> -->
            <!-- <div class="line rule-line"></div> -->
        </div>
        <!-- <div v-if="vipInfo.serviceUrl" class="vip-cs" @click="goCs"></div> -->
        <!-- <VipDialog /> -->
    </div>
</template>
<script lang="ts" setup>
import VipCard from '@/components/vip/card.vue'
import RuleTable from '@/components/vip/ruleTable1.vue'
import VipDialog from '@/components/vip/vipDialog.vue'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'

const router = useRouter()
const store = useBaseStore()

const { vipInfo } = storeToRefs(store)

const goback = () => {
    router.back()
}
const showMatian = computed(() => {
    return false
    return vipInfo.value.curLevel > 1 && vipInfo.value.keepMax
})
const getVipInfo = async (loading = true) => {
    try {
        const info = await store.getVipInfo(loading)
        if (info.code === 200) {
            store.setvipInfo(info)
        }
    } catch (e) {
        console.log(e)
    }
}

onActivated(() => {
    getVipInfo()
})
</script>

<style lang="scss" scoped>
.menu-title {
    @apply flex justify-center absolute;
    width: 100%;
    height: var(--menu-header);
    z-index: 1000;
    background-color: rgba(50, 55, 56, 1);
    color: #fff;
    display: flex;
    text-align: center;
    align-items: flex-end;
    padding-bottom: 30px;

    .menu-close {
        position: absolute;
        left: 25px;
        width: 55px;
        height: 55px;
        border-radius: 10px;
        background-color: #464f50;
        color: #fff;
        background-size: 100% 100%;
        justify-content: center;
        align-items: center;
        display: flex;
        :deep(img) {
            width: 100%;
            height: 60%;
        }
    }

    .nav-title {
        font-size: 30px;
        font-weight: 450;
        margin-top: 10px;
    }
}
.vip-page {
    // position: relative;
    padding: 0 26px;
    height: calc(var(--vh, 1vh) * 100);
    overflow-y: auto;
    background-color: rgb(41 45 46);

    .vip-content {
        padding-top: calc(var(--menu-header) + 30px);
        //居中
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .vip-cs {
        position: absolute;
        top: 50%;
        right: 0;
        width: 111px;
        height: 115px;
        background: url('@/assets/img/vip/cs.png') no-repeat;
        background-size: 100% 100%;
        transform: translateY(-50%);
    }

    .grade-card {
        position: relative;
        width: 720px;
        padding: 42px 20px 21px;
        background-image: linear-gradient(150deg, #b243d7 0%, #7545da 35%, #3847dc 100%), linear-gradient(#3a3f51, #3a3f51);
        background-blend-mode: normal, normal;
        border-radius: 30px;

        .grade-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 175px;
            height: 154px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            transform: scale(1) translate3d(0, -35%, 0);

            @for $i from 0 through 12 {
                &.vip-#{$i} {
                    background-image: url('@/assets/img/vip/badge/#{$i}.png');
                }
            }
        }
        .card-line {
            margin-top: 23px;
            margin-bottom: 19px;
        }
    }
    .line {
        margin: 0 auto;
        width: 675px;
        height: 18px;
        background: url('@/assets/img/vip/line.png') no-repeat;
        background-size: 100% 100%;
    }
    .vip-line {
        position: relative;
        margin-top: 41px;
        margin-bottom: 45px;
    }
    .table-line {
        margin-top: 45px;
        margin-bottom: 60px;
    }
    .rule-line {
        margin-top: 52px;
        margin-bottom: 41px;
    }
    .rule {
        padding-top: 30px;
        padding-bottom: 20px;
        .rule-title {
            font-size: 28px;
            font-weight: bold;
            line-height: 44px;
            color: #fff;
            font-family: MicrosoftYaHei;
            padding-bottom: 20px;
        }
        .rule-content {
            font-size: 22px;
            line-height: 33px;
            letter-spacing: 0px;
            color: #888c92;

            ::v-deep .text-white {
                color: rgba($color: #fff, $alpha: 0.8);
                display: inline-flex;
                font-weight: bold;
                height: 50px;
                align-items: flex-end;
            }

            ::v-deep .text-green {
                color: #4caf50;
                font-weight: bold;
            }
        }
    }
}
</style>
