<template>
    <div class="recharge">
        <Header />
        <div class="recharge-rebate">
            <div>{{ $t('recharge_rebate_info') }}</div>
            <div class="recharge-cutdown" v-if="info.status === 1">
                <van-count-down :time="time">
                    <template #default="timeData">
                        <div class="clock">
                            <span class="block">{{ timeData.days }}</span>
                            <span class="colon">day :</span>
                            <span class="block">{{ timeData.hours }}</span>
                            <span class="colon">h :</span>
                            <span class="block">{{ timeData.minutes }}</span>
                            <span class="colon">m</span>
                        </div>
                    </template>
                </van-count-down>
            </div>
            <div v-if="tableData.gold.length" class="recharge-table">
                <div class="recharge-tr">
                    <div class="recharge-td" v-for="(item, index) in tableData.payamount" :key="index">{{ item }}</div>
                </div>
                <div class="progress">
                    <div class="progress-bar" v-for="idx in 3" :key="idx"><img class="bar-check" src="@/assets/img/events/check.png" /></div>
                    <div class="progress-done">
                        <div class="bar-inner" v-for="n in 3" :key="n">
                            <div :class="[`inner`, getCurPos() >= n ? `inner${n}` : '']" :style="{ width: getPercentStyle(n) }">
                                <div v-if="active > n - 1" class="progress-bar">
                                    <img class="bar-check" src="@/assets/img/events/checked.png" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="recharge-tr recharge-font">
                    <div class="recharge-td" v-for="(item, index) in tableData.gold" :key="index">{{ item }}</div>
                </div>
                <!-- <div class="recharge-tr recharge-font">
                    <div class="recharge-td" v-for="(item, index) in tableData.bonus" :key="index">{{ item }}</div>
                </div> -->
                <div class="recharge-tr recharge-font">
                    <div class="recharge-td" v-for="(item, index) in tableData.percentage" :key="index">{{ item }}</div>
                </div>
            </div>
            <div class="recharge-btn" @click="router.push('/wallets')">{{ $t('Recharge') }}</div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useBaseStore } from '@/stores'
import dayjs from 'dayjs'
import { Acticity } from '@/stores/types'

const router = useRouter()
const route = useRoute()
const store = useBaseStore()

const time = ref(0)

const active = ref(0)
const info = ref<Partial<Acticity>>({})

const tableData = reactive({
    gold: [],
    bonus: [],
    payamount: [],
    percentage: [],
})
const percentGold = ref([])
const getCurPos = () => {
    const amount = info.value.user?.amount || 0
    let pos = [...percentGold.value].reverse().findIndex((mount) => amount >= mount)
    if (pos === -1) {
        pos = 0
    }
    return pos ? percentGold.value.length - 1 - pos + 2 : pos + 1
}

const getPercentStyle = (item) => {
    if (!info.value.user) {
        return '0%'
    }
    const amount = info.value.user.amount
    const cur = getCurPos()
    if (cur !== 1 && cur != item) {
        return cur > item ? '100%' : '0%'
    }

    if (cur === 1 && item !== 1) {
        return '0%'
    }
    const total = percentGold.value[cur === 0 ? 0 : cur - 1]
    const prevTotal = cur - 2 >= 0 ? percentGold.value[cur - 2] : 0
    const percent = ((amount - prevTotal) / (total - prevTotal)) * 100 + '%'
    return percent
}
const getInfo = async () => {
    try {
        const res = await store.getSignEvent({
            id: route.query.id as string,
        })
        if (res.code === 200) {
            info.value = res.acticity
            const { clientcontext, context } = res.acticity.paymentrebateConfg
            const { amount = 0, endtime } = res.acticity.user || {}
            const list = [...clientcontext, ...context]
            list.forEach((item, index) => {
                Object.keys(item).forEach((key) => {
                    if (key === 'bonus') {
                        return
                    }
                    if (key === 'gold' && index !== 0) {
                        percentGold.value.push(item.payamount)
                    }
                    //
                    const data =
                        (key === 'gold' && index !== 0 ? Number(item.gold) + Number(item.bonus) : item[key]) +
                        (key === 'percentage' || index === 0 ? '' : ` ${store.cureencySymbol()}`)
                    tableData[key].push(data)
                })
            })
            let index = [...percentGold.value].reverse().findIndex((item) => amount >= item)
            if (index !== -1) {
                index = percentGold.value.length - 1 - index
            }
            active.value = index === -1 ? 0 : index + 1
            // @ts-ignore
            time.value = dayjs(endtime) - dayjs()
        }
    } catch (e) {
        console.log(e)
    }
}

onBeforeMount(() => {
    getInfo()
})
</script>
<style lang="scss" scoped>
.recharge {
    height: calc(var(--vh, 1vh) * 100);
    background: url('@/assets/img/recharge-rebate/bg.jpg') repeat-x left top;
    background-size: 1px 100%;
}
.recharge-rebate {
    min-height: 100%;
    padding: 29px 15px 0;
    color: #7e776e;
    font-size: 24px;
    background: url('@/assets/img/recharge-rebate/gold.png') no-repeat left bottom;
    background-size: 100% 859px;

    .recharge-cutdown {
        margin: 54px 0 34px;

        :deep(.van-count-down) {
            color: #ffc11a;
            font-size: 60px;
            font-weight: bold;
        }

        .clock {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;

            &::before {
                content: '';
                width: 68px;
                height: 68px;
                background: url('@/assets/img/events/clock.png') no-repeat;
                background-size: 100% 100%;
            }
            .block {
                margin-left: 13px;
            }
        }
    }
    .recharge-table {
        padding: 16px 0;
        margin-top: 34px;
        // height: 400px;
        background-image: linear-gradient(120deg, #00d2cf 0%, #189aff 40%, #0040aa 100%), linear-gradient(#3a3533, #3a3533);
        background-blend-mode: normal, normal;
        border-radius: 30px;

        .recharge-tr {
            display: flex;
            align-items: center;

            .recharge-td {
                flex: 1;
                line-height: 74px;
                text-align: center;
                font-size: 25px;
                color: #ffffff;
            }
        }
        .recharge-font {
            font-weight: bold;

            .recharge-td {
                font-size: 35px;
            }

            &:nth-child(odd) {
                .recharge-td {
                    color: #fff9c3;
                    background-image: linear-gradient(180deg, #f5f6f1, #ffed58);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent; /* 文本颜色设置为透明，以显示背景 */
                }
            }
            &:nth-child(even) {
                .recharge-td {
                    // color: #fca2ff;
                    background-image: linear-gradient(180deg, #fff6ff, #fa70ff);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent; /* 文本颜色设置为透明，以显示背景 */
                }
            }
        }
        .progress {
            position: relative;
            display: flex;
            margin: 0 auto;
            width: 608px;
            height: 48px;
            padding: 4px;
            background-color: rgba(0, 0, 0, 0.4);
            border-radius: 24px;
            .progress-bar {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                width: 200px;
                background: url('@/assets/img/events/arrow.png') no-repeat center center;
                background-size: 55px 32px;
                opacity: 0.4;

                .bar-check {
                    width: 40px;
                    height: 40px;
                }
            }
            .progress-done {
                position: absolute;
                top: 4px;
                left: 4px;
                display: flex;
                width: 600px;
                height: 40px;

                .progress-bar {
                    flex-shrink: 0;
                    opacity: 1;
                }
                .bar-inner {
                    width: 200px;
                    height: 100%;
                }
                .inner {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    position: relative;
                    width: 200px;
                    height: 100%;
                    background-size: 55px 32px;
                    background-image: linear-gradient(0deg, #ffa72a 0%, #ffc44f 100%);
                    box-shadow: inset 0px 12px 0px 0px rgba(255, 255, 255, 0.3);
                    border-radius: 24px;
                    overflow: hidden;

                    &.inner1 {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                    &.inner2 {
                        border-radius: 0;
                    }
                    &.inner3 {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }
                }
            }
        }
    }
    .recharge-btn {
        margin: 100px auto 0;
        width: 480px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
        border-radius: 44px;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
}
</style>
