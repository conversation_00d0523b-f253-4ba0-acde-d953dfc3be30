<template>
    <div class="languge">
        <Header></Header>
        <div class="languge-wap">
            <LangChoose @on-select="changeLanguage" />
            <div class="btn-wrap">
                <Button type="submit" @confirm="handleConfirm">{{ $t('Confirm') }}</Button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="language">
import Header from '@/components/Header.vue'
import LangChoose from '@/components/language/langChoose.vue'
import Button from '@/components/Button.vue'
import { useBaseStore } from '@/stores'
import { useRouter } from 'vue-router'
import { setlanguage } from '@/api/language'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { setLocaleLang } from '@/language'

const router = useRouter()
const i18n = useI18n()

const language = ref(i18n.locale.value)

const store = useBaseStore()
const changeLanguage = (lang) => {
    language.value = lang
}

const handleConfirm = () => {
    setlanguage(language.value).then((res) => {
        if (res.code === 200) {
            store.lang = language.value
            setLocaleLang(store.lang)
            showToast(i18n.t('Set_language_success'))
            router.back()
            setTimeout(() => {
                location.reload()
            }, 20)
        } else {
            showToast(i18n.t('Set_language_fail'))
        }
    })
}
</script>

<style scoped lang="scss">
.languge {
    background: #fff;
}
.languge-wap {
    position: relative;
    height: calc(var(--vh, 1vh) * 100 - var(--header-height) - var(--safe-height)) !important;
    overflow: auto;
    background: #fff;
    .btn-wrap {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
    }
}
:deep(.header) {
    background: #fff;
    color: #6d6d6a;
    border-bottom: none;
    .header-title {
        color: #6d6d6a;
    }
}
</style>
