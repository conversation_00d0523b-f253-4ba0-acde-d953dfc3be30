<template>
    <div class="profile">
        <Header></Header>
        <div class="user-info">
            <van-uploader accept="image/*" :max-count="1" :preview-image="false" :after-read="afterRead">
                <div class="avatar">
                    <div class="camara"></div>
                    <img :src="userInfo.avatar" />
                </div>
            </van-uploader>
            <div class="user-id">ID: {{ userInfo.uid }}</div>
        </div>
        <div class="sys-set">
            <SysItem
                v-for="item in settingConfig"
                :key="item.name"
                :path="item.type === 4 ? '/me' : '/profile'"
                :item="item"
                @click="handleClick(item)"
                :showArrow="[1, 4].includes(item.type)"
            >
                <template v-if="item.type !== 4" #right>
                    <div class="user-name" v-if="item.type === 1">
                        <van-field class="edit-input" v-model="userName" @blur="onBlur" />
                    </div>
                    <div v-else>
                        <div v-if="item.type === 2 && !userInfo?.telephone" class="bind-btn">{{ $t('Bind') }}</div>
                        <div v-if="item.type === 3 && !userInfo?.shareid && !over24" class="bind-btn">{{ $t('Bind') }}</div>
                        <div class="bind-btn" v-else-if="item.type === 2 && userInfo.telephone && !userInfo.bindtelephonepick" @click="reward">
                            {{ $t('Waiting_to_receive') }}
                        </div>
                        <div class="tip" v-else-if="userInfo.bindtelephonepick">{{ $t('Received') }}</div>
                    </div>
                </template>
            </SysItem>
        </div>
        <!-- <BindDialog v-model="showBind" />
        <BindInviter v-model="showInivte" /> -->
        <van-overlay :show="showCropper" @click="showCropper = false" :lock-scroll="false">
            <div class="cut"><vueCropper ref="cropper" v-bind="{ ...option }"></vueCropper></div>
            <div class="cut-btn" @click="getCutPic">{{ $t('Confirm') }}</div>
        </van-overlay>
    </div>
</template>
<script setup lang="ts" name="profile">
import Header from '@/components/Header.vue'
import SysItem from '@/components/me/list.vue'
// import BindDialog from '@/components/dialog/BindDialog.vue'
// import BindInviter from '@/components/dialog/BindInviter.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { showDialog } from 'vant'
import { decode, encode } from 'js-base64'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import router from '@/router'
import * as service from '@/api'
import dayjs from 'dayjs'

import { useI18n } from 'vue-i18n'

const i18n = useI18n()
const store = useBaseStore()
const { userInfo } = storeToRefs(store)
const showCropper = ref(false)
const cropper = ref(null)
const option = reactive({
    img: '',
    // outputType: 'png',
    outputSize: 0.7,
    // // maxImgSize: 2000,
    // enlarge: 1,
    // autoCrop: true,
    // // 只有自动截图开启 宽度高度才生效
    // autoCropWidth: 200,
    // autoCropHeight: 200,
    // canMoveBox: true,
    // original: true,
    // infoTrue: true,
    size: 1,
    full: false,
    outputType: 'png',
    canMove: true,
    fixedBox: false,
    original: false,
    canMoveBox: true,
    autoCrop: true,
    // 只有自动截图开启 宽度高度才生效
    autoCropWidth: 200,
    autoCropHeight: 200,
    centerBox: false,
    high: false,
    cropData: {},
    enlarge: 1,
    mode: 'contain',
    maxImgSize: 3000,
    limitMinSize: [50, 50],
    fixed: false,
    fixedNumber: [2, 1],
    fillCover: '',
})
const showBind = ref(false)
const showInivte = ref(false)
const userName = ref(userInfo.value.nickname && decode(userInfo.value.nickname))
const over24 = computed(() => {
    const createT = dayjs(userInfo.value.createat)
    const nowT = dayjs()
    const diffHours = nowT.diff(createT, 'hour')
    return diffHours > 24
})
const path = '../../assets/img/me/'
const file = import.meta.glob('../../assets/img/me/*', { eager: true })

const getImg = (name) => {
    return file[path + name].default
}

const settingConfig = computed(() => {
    const config = [
        {
            name: 'Nickname',
            icon: getImg('inviter.png'),
            type: 1,
        },
        // {
        //     name: 'Bind_mobile_phone',
        //     icon: getImg('phone.png'),
        //     type: 2,
        // },
        // {
        //     name: 'Bind_inviter',
        //     icon: getImg('user.png'),
        //     type: 3,
        // },
        // {
        //     name: 'Logout',
        //     icon: getImg('logout.png'),
        //     type: 4,
        // },
    ]
    if (userInfo.value.shareid) {
        config.splice(2, 1)
    }
    return config
})
const handleClick = (item) => {
    if (item.type === 2) {
        if (userInfo.value.telephone) {
            return
        }
        showBind.value = true
    } else if (item.type === 3) {
        if (userInfo.value?.shareid || over24.value) {
            return
        }
        showInivte.value = true
    } else if (item.type === 4) {
        showDialog({
            message: i18n.t('Are_you_sure_exit'),
            className: 'common-dialog',
            showCancelButton: true,
            confirmButtonText: i18n.t('Confirm'),
            cancelButtonText: i18n.t('Cancel'),
        })
            .then(() => {
                store.logOut()
                router.push('/')
            })
            .catch((e) => {
                console.log(e, 'Cancel')
            })
    }
}
const afterRead = (val) => {
    option.img = val.content
    showCropper.value = true
}
const onBlur = () => {
    const nickname = userInfo.value.nickname && decode(userInfo.value.nickname)
    if (nickname === userName.value) {
        return
    }
    showDialog({
        message: i18n.t('Are_you_sure_modify_nickname'),
        className: 'common-dialog',
        showCancelButton: true,
        confirmButtonText: i18n.t('Confirm'),
        cancelButtonText: i18n.t('Cancel'),
    })
        .then(async () => {
            const res = await store.changeUserInfo({
                nickname: encode(userName.value),
            })
            if (res.code !== 200) {
                userName.value = nickname
                showToast(i18n.t('Modification_failed'))
            }
        })
        .catch(() => {
            userName.value = userInfo.value.nickname && decode(userInfo.value.nickname)
        })
}
const getCutPic = () => {
    cropper.value.startCrop()
    cropper.value.getCropBlob((blob) => {
        service
            .uploadFile(
                new File([blob], 'a.png', {
                    type: 'image/png',
                    lastModified: Date.now(),
                })
            )
            .then((res) => {
                if (res.code === 200) {
                    store.updateUserInfo(res.avatar)
                } else {
                    showToast(i18n.t('Modification_avater_fail'))
                }
            })
    })
}
const reward = async () => {
    const res = await store.ReceiveRewards({
        id: 'bindtelephone',
    })
    if (res.code === 200) {
        store.getProfile()
    }
}
</script>

<style scoped lang="scss">
.sys-set {
    margin: 0 20px 0 20px;
    padding: 9px 35px;
    background-color: rgba(58, 53, 51, 0.6);
    border-radius: 20px;
}
.binded-text {
    font-size: 26px;
    color: #fff;
}
.bind-btn {
    width: 80px;
    height: 40px;
    background: yellow;
    font-size: 20px;
    text-align: center;
}
.tip {
    font-size: 20px;
}
.user-info {
    @apply flex flex-col items-center justify-center;
    padding-top: 95px;

    .avatar {
        @apply rounded-full overflow-hidden;
        position: relative;
        width: 118px;
        height: 118px;
        background: url('@/assets/img/me/avatar_bg.png');
        background-size: 100% 100%;
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            transform: translate3d(-50%, -50%, 0);
            background: rgba(0, 0, 0, 0.5);
        }

        .camara {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 44px;
            height: 36px;
            background: url('@/assets/img/profile/camara.png');
            background-size: 100% 100%;

            transform: translate3d(-50%, -50%, 0);
        }
    }
    color: #fefefe;
    font-weight: bold;
    font-stretch: normal;
    font-size: 30px;

    .user-id {
        @apply flex items-center gap-[10px];
        margin: 25px 0 30px 0;
        line-height: 26px;
    }
}
.user-name {
    width: 200px;
    font-size: 26px;
    color: #fff;

    .edit-input {
        // background-color: rgba(58, 53, 51, 0.6);
        padding-right: 0;
        padding-left: 0;
        color: #fff;
        background-color: rgba(31, 27, 27, 0);

        :deep(.van-field__control) {
            color: #fff;
            text-align: right;
        }
    }
}
.bind-btn {
    width: 136px;
    height: 44px;
    line-height: 44px;
    border-radius: 40px;
    text-align: center;
    color: #fff;
    background: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
}
.cut {
    height: 100vh;
}
.cut-btn {
    position: absolute;
    left: 50%;
    bottom: 20px;
    width: 290px;
    height: 72px;
    line-height: 72px;
    background: linear-gradient(150deg, #ffc44f 0%, #ffb13d 40%, #ff9d2a 100%), linear-gradient(#e59e20, #e59e20);
    border-radius: 36px;
    font-size: 36px;
    font-weight: bold;
    color: #fff;
    text-align: center;

    transform: translateX(-50%);
}
</style>
