/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-10 20:29:24
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-17 21:10:24
 * @FilePath     : /src/stores/jackpotStore.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-10 20:29:24
 */
import { defineStore } from 'pinia'

export const useJackpotStore = defineStore('BetFugu_jackpot', {
    state: () => {
        return {
            homePagePool: 0 as number,
            isRewardClaimed: false as boolean,//是否已领奖
            showJackpot: false as boolean, // 是否显示 Jackpot
            jackpotData: null as any, // Jackpot数据
        }
    },
    actions: {
        // 设置首页数值（popup增长时调用）
        setHomePagePool(value: number) {
            this.homePagePool = value
        },

        // 设置奖励已领取状态
        setClaimDaylyJackpot() {
            this.isRewardClaimed = true
        },

        // 重置首页数值（使用后立即清空）
        resetHomePagePool() {
            this.homePagePool = 0
        },

        // 设置Jackpot数据（只存储实际数据）
        setJackpotData(data: any) {
            this.jackpotData = data
        },

        // 单独设置是否显示Jackpot菜单
        setShowJackpot(show: boolean) {
            this.showJackpot = show
        }
    },
})