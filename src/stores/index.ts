/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-09 12:13:42
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-10 20:31:14
 * @FilePath     : /src/stores/index.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-09 12:13:42
 */
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
export * from './baseStore'
export * from './sessionStore'
export * from './jackpotStore'

// 创建
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
// 导出
export default pinia
