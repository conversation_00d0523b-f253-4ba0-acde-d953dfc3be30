import { Currency } from './../api/types'
import { defineStore } from 'pinia'
import { ServoConfig, WsServo } from '@/api/servo/types'
import { Categorys, Homelist, Announcement } from '@/api/home/<USER>'
import { VideoDetail } from '@/api/video/types'
import { LoginUser } from '@/api/types'
import { GLOBAL_URL, ERROR_CODES, BLACK_LIST } from '@/enums'
import * as type from './types'
import { getErrorMsg, getWsHeader, getChannel, getDeviceInfo, sort } from '@/utils'
import { showToast } from 'vant'
import { changeUserInfo } from '@/api'
import { Wallet } from '@/api/home/<USER>'
import {
    Chat,
    HeaderMsg,
    SignParam,
    SignRes,
    Receive,
    Lamp,
    VipInfo,
    VipConfig,
    ChatTransferParam,
    VipRules,
    JackpotActionParam,
    RedfissionInfo,
    ActionParam,
} from './types'
import router from '@/router'
import { showNotify } from 'vant'
import i18n from '@/language'
import { useSubscript } from '@/components/notice/hook/useSubscribe'
import bus, { EVENT_KEY } from '@/utils/bus'
import webApp from '@twa-dev/sdk'
import { Log } from '@/api/log'
import dayjs from 'dayjs'
import { WS_SERVER, CURRENCY_LIST, LANGUAGE_LIST } from '@/enums'
import { Channel } from '@/api/wallet/type'
/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useBaseStore = defineStore('BetFugu_base', {
    state: (): Partial<{
        bodyHeight: number
        bodyWidth: number
        userInfo: Partial<LoginUser>
        wallet: Partial<Wallet>
        maskDialog: boolean
        maskDialogMode: string
        excludeNames: string[]
        showLogin: boolean
        token: string
        loading: boolean
        judgeValue: number
        homeRefresh: number
        servoConfig: Partial<ServoConfig>
        gameConfig: Partial<WsServo>
        gameserver: any
        isMuted: boolean
        lang: string
        systemNum: number
        transactionNum: number
        activeNum: number
        gameLogined: boolean
        loginCb: any[]
        SYSTEMList: Chat[]
        TRANSACTIONList: Chat[]
        chatList: {
            [key: string]: Chat[]
        }
        unReadCount: {
            [key: string]: number
        }
        shareId: number
        sharefrom: string
        partner: number
        link_id: string
        promote_url_id: string
        ntvfrom: string
        curSymbol: {
            [key: string]: string
        }
        signId: string
        videoCach: Map<string, string>
        newUser: boolean
        loginTime: number
        showVideoDrive: boolean
        showDownload: boolean
        tgInitData: { [key: string]: any }
        showMenu: boolean
        conneting: boolean
        wsList: string[]
        wsIdx: number
        tabData: Categorys[]
        gameList: Homelist
        homeShow: boolean
        showBuoy: boolean
        lampList: Lamp[]
        vipInfo: VipInfo
        vipConfig: VipConfig[]
        vipRules: VipRules
        reelsConfig: VideoDetail
        chargeDate: {
            [key: string]: string[]
        }
        signDate: {
            [key: string]: string[]
        }
        dramaStatus: {
            [key: string]: {
                like: number
                retweet: number
                favorite: number
            }
        }
        coinData: {
            date: number | number
            finish: boolean
        }
        buyVideoParam: null | {
            [key: string]: any
        }
        bonus: number
        closeBind: boolean
        depositOrder: string
        prevGameTag: string
        depositeChannel: Channel[]
        announcementData: Announcement[]
        showAnnouncement: {
            [key: string]: string[]
        }
        redfissionInfo: RedfissionInfo
        // [key: string]: any
    }> => {
        return {
            bodyHeight: document.body.clientHeight as number,
            bodyWidth: document.body.clientWidth as number,
            userInfo: {},
            wallet: {},
            maskDialog: false,
            maskDialogMode: 'dark',
            excludeNames: ['notice'],
            showLogin: false,
            token: '', // 用户token
            loading: false,
            judgeValue: 20,
            homeRefresh: 60,
            servoConfig: {},
            gameConfig: {},
            gameserver: null,
            isMuted: true, // 视频静音
            lang: '',
            systemNum: 0, // 系统消息数量
            transactionNum: 0, // 交易消息数量
            activeNum: 0, // 活动消息数量
            gameLogined: false, // 是否登录到游戏服务器
            loginCb: [], // 未登录游戏服务时发出的请求
            SYSTEMList: [], // 系统消息列表
            TRANSACTIONList: [], // 交易消息列表
            chatList: {}, // 聊天信息
            unReadCount: {}, // 未读消息数量
            shareId: 0,
            sharefrom: '',
            partner: 66666666,
            link_id: '',
            promote_url_id: '',
            ntvfrom: '',
            curSymbol: {}, // 货币符号配置
            signId: '',
            videoCach: new Map(),
            newUser: false,
            loginTime: 0, // 上次登录时间
            showVideoDrive: false,
            showDownload: true,
            tgInitData: {},
            showMenu: false,
            conneting: false,
            wsList: WS_SERVER,
            wsIdx: 0,
            tabData: [],
            gameList: [],
            homeShow: true,
            showBuoy: true,
            lampList: [],
            vipInfo: {},
            vipConfig: [],
            vipRules: [],
            reelsConfig: {},
            chargeDate: {},
            signDate: {},
            dramaStatus: {},
            coinData: {
                date: '',
                finish: false,
            },
            buyVideoParam: null,
            bonus: 0,
            closeBind: false,
            depositOrder: '',
            prevGameTag: '',
            depositeChannel: [],
            announcementData: [],
            showAnnouncement: {},
            redfissionInfo: {
                status: 0,
            },
        }
    },
    getters: {
        // 未读消息数量
        noticeNum(state) {
            console.log((state.unReadCount, '(state.unReadCount'))
            return Object.keys(state.unReadCount).reduce((prev, key) => {
                return prev + (state.unReadCount[key] || 0)
            }, 0)
        },
        tabMenu(state) {
            // @ts-ignore
            const history = state.gameList?.history
            if (history?.length && history?.length > 0) {
                const tabData = [...state.tabData]
                // @ts-ignore
                tabData.splice(1, 0, {
                    name: 'Game_type_history',
                    picture: '',
                    type: 'history',
                })
                return tabData
            }
            return state.tabData
        },
        menuData(state) {
            return state.tabData.filter((item) => !['HOT', 'NEW'].includes(item.type.toUpperCase()))
        },
        getVideoCache(state) {
            return (key) => state.videoCach.get(key)
        },
        // 货币符号
        cureencySymbol(state) {
            return (key = '') => {
                return state.curSymbol[key] || state.curSymbol[state.wallet?.currency]
            }
        },
        currencyList() {
            return CURRENCY_LIST
        },
        languageList() {
            return LANGUAGE_LIST.map((item) => {
                return {
                    ...item,
                    value: item.language.replace(/_([a-zA-Z])/g, (match, letter) => '-' + letter.toUpperCase()),
                }
            })
        },
        defaultCurrency() {
            if (this.wallet?.currency) {
                return CURRENCY_LIST.find((item) => item.currency === this.wallet?.currency)
            }
            return CURRENCY_LIST[0]
        },
        defaultLangguage() {
            return LANGUAGE_LIST[0]
        },
        showDown(state) {
            return (
                !localStorage.getItem('__betfugu_858956856_data') &&
                state.showDownload &&
                !webApp.initData &&
                !(
                    !!window.matchMedia('(display-mode: standalone)').matches ||
                    window.navigator.standalone ||
                    !!window.matchMedia('(display-mode: fullscreen)').matches
                ) &&
                !(getDeviceInfo().os === 'iOS' && window.navigator.standalone)
            )
        },
        isTg() {
            return false
            //暂时屏蔽掉telegram lzj
            return !!webApp.initData
        },
        // 是否是大区代理
        isAgent(state) {
            return state.token && state.userInfo?.agentid === state.userInfo?.uid
        },
        getWsServer() {
            return this.wsList[this.wsIdx++]
        },
        getBanner() {
            return this.gameList.banner
        },
        curVipConfig() {
            return this.vipConfig.find((item) => item.level === this.vipInfo.curLevel)
        },
        getApp() {
            if (this.isTg) {
                return 'telegram'
            }
            if (
                window.matchMedia('(display-mode: standalone)').matches ||
                (getDeviceInfo().os === 'iOS' && window.navigator.standalone) ||
                localStorage.getItem('__betfugu_858956856_data') ||
                localStorage.getItem('pwa-link-id')
            ) {
                return 'pwa'
            }
            return 'h5'
        },
    },
    actions: {
        setMenu(data) {
            this.showMenu = data
        },
        setvipInfo(data) {
            this.vipInfo = data.info
            this.vipConfig = data.config
            this.vipRules = data.rules
        },
        setHomeData(data) {
            this.tabData = data.categorys
            console.log(data, 'setHomeData')

            this.gameList = data

            // 打印 gameList 的完整结构
            console.log('%c------ this.gameList 完整数据 ------', 'background-color:green;font-size:14px;color:#fff', this.gameList)
            console.log('%c------ gameList.gamepages ------', 'background-color:blue;font-size:12px;color:#fff', this.gameList.gamepages)
            console.log('%c------ gameList.gameconfigs ------', 'background-color:purple;font-size:12px;color:#fff', this.gameList.gameconfigs)
            console.log('%c------ gameList.gameStat ------', 'background-color:orange;font-size:12px;color:#fff', this.gameList.gameStat)

            // 🔍 专门查找供应商/厂商相关字段
            console.log('%c------ 查找供应商/厂商字段 ------', 'background-color:red;font-size:14px;color:#fff')
            console.log('%c------ gameList.gamefirms ------', 'background-color:yellow;font-size:12px;color:#000', this.gameList.gamefirms)

            // 打印 gameList 的所有 key，看看有哪些字段
            console.log('%c------ gameList 所有字段 ------', 'background-color:cyan;font-size:12px;color:#000', Object.keys(this.gameList))
        },
        getGameName(gamefirm) {
            return this.gameList.gamefirms[gamefirm].tag
        },
        // 获取厂商数组
        getGameFirms() {
            if (!this.gameList.gamefirms) return []

            // return Object.values(this.gameList.gamefirms).filter(firm => !firm.isstop)
						return Object.values(this.gameList.gamefirms)
        },
        getGameList(type) {
            console.log(`%c------ getGameList(${type}) 调用 ------`, 'background-color:red;font-size:12px;color:#fff', this.gameList)
            const { gameconfigs, gamepages, gameStat, history } = this.gameList
            if (type === 'history') {
                return history
                    ?.sort((a, b) => b.time - a.time)
                    .map((game) => {
                        return { ...game, ...gameconfigs[game.gametag], ...gameStat[game.gametag] }
                    })
            }

            // const selfGame = sort(gameselfpages[type])?.map((game) => {
            //     return { ...game, ...gameconfigs[game.gametag], ...gameStat[game.gametag] }
            // })

            const game = sort(gamepages[type])?.map((game) => {
                return { ...game, ...gameconfigs[game.gametag], ...gameStat[game.gametag] }
            })
            // ...selfGame,
            return [...game]
        },
        updatLamp(lamp) {
            if (lamp.title === 'SystemMaintain' || this.wallet.currency === lamp.cron) {
                this.lampList = [...this.lampList, lamp]
            }
        },
        clearLamp() {
            this.lampList = []
        },
        setTgData(data) {
            this.tgInitData = data
        },
        setVideoCache(key, val) {
            this.videoCach.set(key, val)
        },
        setInit(data) {
            const { currency, language } = data
            this.setWallet(currency)
            this.lang = language
            this.userInfo = { ...this.userInfo, curcurrency: currency.currency, curlanguage: language, guideCurrency: true, guideLanguage: true }
        },
        setCurSym() {
            const config = {}
            CURRENCY_LIST.forEach((item) => {
                config[item.currency] = item.clientcurrency
            })
            this.curSymbol = { ...config }
        },
        setMaskDialog(val) {
            this.maskDialog = val.state
            if (val.mode) {
                this.maskDialogMode = val.mode
            }
        },
        updateExcludeNames(val) {
            if (val.type === 'add') {
                if (!this.excludeNames.find((v) => v === val.value)) {
                    this.excludeNames.push(val.value)
                }
            } else {
                const resIndex = this.excludeNames.findIndex((v) => v === val.value)
                if (resIndex !== -1) {
                    this.excludeNames.splice(resIndex, 1)
                }
            }
        },
        // 设置服务器配置
        setServoConfig(val) {
            this.servoConfig = val
        },
        setGameConfig() {
            this.setCurSym()
        },
        setToken(token) {
            this.token = token
        },
        setNewUser(isNew) {
            this.showVideoDrive = this.newUser = isNew || false
        },
        setUserInfo(val) {
            this.userInfo = val
        },
        setWallet(val) {
            this.wallet = { ...this.wallet, ...val }
        },
        updateUserInfo(val) {
            this.userInfo = { ...this.userInfo, avatar: val }
        },
        setAnnouncementData(val: Announcement[]) {
            this.announcementData = val
        },
        logOut() {
            this.token = ''
            this.userInfo = {}
            this.unReadCount = {}
            this.chatList = {}
            this.SYSTEMList = []
            this.TRANSACTIONList = []
            this.diconnect()
            bus.emit(EVENT_KEY.LOGOUT)
        },
        // 手动系统，充值，好友消息分发
        distributeMsg(msg) {
            if (getChannel(10001, this.userInfo.uid) === msg.channel) {
                return
            }
            const channel = msg.channel
            const isExit = this.chatList[channel]?.some((item) => item.id === msg.id)
            if (!isExit) {
                this.chatList[channel] = [...(this.chatList[channel] || []), { ...msg }]
            }
            if (msg?.content.sender !== this.userInfo.uid && msg.event !== 'login' && !isExit) {
                this.unReadCount[channel] = (this.unReadCount[channel] || 0) + 1
            }

            bus.emit(EVENT_KEY.MESSAGE_UPDATE, msg)
            const isNotify = msg?.event === 'notify'
            if (channel.includes('20000') || isNotify) {
                const { translateTtoText } = useSubscript('')
                showNotify({
                    className: 'common-notice',
                    duration: 5000,
                    //@ts-ignore
                    message: i18n.global.t(msg.content.text, translateTtoText(msg)),
                    onClick: () => {
                        router.push({
                            path: '/message',
                        })
                    },
                })
            }
        },
        diconnect() {
            this.wsIdx = 0
            if (this.gameserver) {
                this.gameserver.disconnect(-1, 'logout')
                this.gameserver.cleanup()
                localStorage.removeItem('current.session')
                this.gameserver = null
            }
        },
        // 请求封装
        async request<T>(url, params = {}, conf): Promise<T> {
            const config = Object.assign({ loading: true, tip: true }, conf)
            console.log(params, 'params ', url)
            return new Promise((resolve) => {
                // 处理刷新页面时 未登录游戏服务时发送的请求
                if (!this.gameLogined && !BLACK_LIST.includes(url)) {
                    this.loginCb.push(resolve)
                    return
                }
                resolve(0)
            })
                .then(() => {
                    this.gameserver && config.loading && (this.loading = true)
                    return this.gameserver
                        ?.request(url, params)
                        .then((response) => {
                            config.loading && (this.loading = false)
                            console.log(response, 'response ', url, params)
                            if (!response) {
                                this.gameserver.handleClosed()
                                return Promise.reject('soket断连无返回值')
                            }

                            if (response.code && response.code !== ERROR_CODES.OK) {
                                if (config.tip) {
                                    const errorTip = getErrorMsg(url, response.code)
                                    showToast({
                                        message: i18n.global.t(errorTip),
                                        zIndex: 4000,
                                    })
                                }
                            }
                            return response
                        })
                        .catch((e) => {
                            console.log(e)
                            config.loading && (this.loading = true)
                        })
                })
                .catch(() => {
                    config.loading && (this.loading = true)
                })
        },
        // 获取个人信息
        async getProfile() {
            const response: any = await this.request(GLOBAL_URL.ProfilesAsync, {})
            this.userInfo = response.metadata
        },
        // 更改个人信息
        async changeUserInfo(pramas: type.ChangeUserInfo) {
            const response: any = await changeUserInfo(pramas)
            if (response.code === ERROR_CODES.OK) {
                this.userInfo = { ...this.userInfo, nickname: response.pro.nickname }
            }
            return response
        },
        // 绑定手机号
        async bindPhone(params: type.BindPhone) {
            const response: any = await this.request(GLOBAL_URL.BindPhone, params)
            if (response.code === ERROR_CODES.OK) {
                this.getProfile()
            }
            return response
        },
        // 绑定邀请人
        async bindInviter(params: type.BindInviter, conf = {}) {
            const response: any = await this.request(GLOBAL_URL.ShareInviteBind, params, conf)
            if (response?.code === 200) {
                this.getProfile()
            }
            return response
        },
        // 游戏订阅
        async gameSubscribe(params: type.GameSubscribe) {
            return this.request(GLOBAL_URL.GameSubscribe, params, { tip: true })
        },
        async gameUnSubscribe() {
            return this.request(GLOBAL_URL.GameUnSubscribe)
        },
        // 获取vip信息
        async getVipInfo(loading = true, tip = true) {
            return this.request(
                GLOBAL_URL.Vip,
                {},
                {
                    tip,
                    loading,
                }
            )
        },
        // vip领取
        async pickVipRewards(params = {}) {
            return this.request(GLOBAL_URL.PickRewads, params)
        },

        async triggerVip(params = {}) {
            return this.request(GLOBAL_URL.triggerVip, params)
        },

        async gameLogOut() {
            if (!this.gameserver) {
                return
            }
            this.gameserver.disconnect(-1, 'logout')
            this.gameserver.cleanup()
            localStorage.removeItem('current.session')
            this.gameserver = null
        },

        async SessionLogin() {
            Log({
                event: 'ws_login',
                content: {
                    ctime: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS'),
                    retryCounter: this.gameserver.retryCounter,
                    host: this.gameserver._remote.href,
                },
            })
            const response: any = await this.request(
                GLOBAL_URL.SessionLogin,
                {
                    token: this.token,
                    package: getWsHeader(),
                },
                { tip: false }
            )

            if (response.code === ERROR_CODES.Gone) {
                this.logOut()
                router.push('/login')
                return
            } else if (response.code === 100) {
                // this.gameLogOut()
                // this.gameserver.emit('reconnect')
                this.gameserver.handleClosed()
                return
            }
            this.token = ''
            const { newtoken, metadata, items } = response
            // if (!this.lang && metadata.curlanguage) {
            //     this.lang = metadata.curlanguage
            //     location.reload()
            // }
            // 是tg分享
            if (metadata?.tgShareId) {
                this.bindInviter(
                    {
                        shareid: metadata.tgShareId, // 邀请人id
                        auto: true, //是否自动绑定
                        from: metadata.shareFrom, //渠道来源
                    },
                    { loading: false, tip: false }
                )
            }
            this.userInfo = metadata
            this.token = newtoken
            this.wallet = { ...items, currency: metadata.curcurrency }
            this.gameLogined = true
            if (this.loginCb.length) {
                this.loginCb.forEach((cb) => {
                    cb && cb()
                })
                this.loginCb = []
            }
            Log({
                event: 'ws_logined',
                content: {
                    ctime: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS'),
                    retryCounter: this.gameserver.retryCounter,
                    host: this.gameserver._remote.href,
                },
            })
        },
        // 提现
        async payOut(params: type.PayOut): Promise<type.PayOutRes> {
            return this.request(GLOBAL_URL.PayOut, params)
        },
        // 重新进入游戏
        async Rejoingames() {
            return this.request(GLOBAL_URL.Rejoingames, {})
        },
        // 礼物兑换码
        async exchangeGiftCdk(cdkcode: string) {
            return this.request(GLOBAL_URL.GiftCdk, { cdkcode })
        },
        //   查询领取bounus
        async QueryBonuse(type = 0) {
            return this.request(GLOBAL_URL.QueryBonuse, { type }, { loading: false })
        },
        // 获取未读数据
        async getMsgUnread(loading = false) {
            return this.request(GLOBAL_URL.GetFriendsMeta, {}, { loading }).then((res) => {
                if (res.code === 200) {
                    this.unReadCount = {}
                    Object.keys(res.unreadmessages || {}).forEach((channel) => {
                        if (channel !== getChannel(10001, this.userInfo.uid)) {
                            const [, sender, reciever] = channel.match(/^(\d+):(\d+)$/)
                            const code = this.userInfo.uid == sender ? `${reciever}:${sender}` : `${sender}:${reciever}`
                            this.unReadCount[code] = res.unreadmessages[channel]
                        }
                    })
                    this.activeNum = res.activityzone || 0
                }
                return res
            })
        },
        // 获取最近20条数据
        async getHot20Msg(channel) {
            return await this.request(GLOBAL_URL.GetHotMsg, { channels: [channel] }, { loading: false })
        },
        async getChatHis({ channel, id, size = 10 }) {
            return await this.request(GLOBAL_URL.GetHisMsg, { channel, id, size }, { loading: false })
        },
        async sendText(params) {
            return this.request(GLOBAL_URL.SendText, params, { loading: false })
        },

        //  消息订阅
        async subscribe(channel) {
            console.trace()
            return this.request(GLOBAL_URL.FriendsSubscribe, { channel }, { loading: false })
        },
        // 取消消息订阅
        async deSubscribe(channel) {
            return this.request(GLOBAL_URL.FriendsUnsubscribe, { channel }, { loading: false })
        },
        async Beanexchange(count: string) {
            return this.request(GLOBAL_URL.Beanexchange, { count: +count })
        },
        async getHeadermessage(pramas): Promise<HeaderMsg> {
            return this.request(GLOBAL_URL.Headermessage, pramas)
        },
        // 获取签到活动
        async getSignEvent(params: SignParam, tip = true, loading = true): Promise<SignRes> {
            return this.request(GLOBAL_URL.Sign, params, { tip, loading })
        },

        // 获取Jackpot相关数据的通用方法
        async getJackpotAction(params: JackpotActionParam, tip = true, loading = true) {
            return this.request(GLOBAL_URL.JackpotAction, params, { tip, loading })
        },
        // 开启活动
        async actionEvent(params: ActionParam, tip = true, loading = true): Promise<SignRes> {
            return this.request(GLOBAL_URL.action, params, { tip, loading })
        },
        async ReceiveRewards(params: Receive, loading: boolean = true): Promise<{ code: number; awards: { [key: string]: any } }> {
            return this.request(GLOBAL_URL.ReceiveRewards, params, { loading })
        },
        async OrderCancel(orderid: string) {
            return this.request(GLOBAL_URL.OrderCancel, { orderid })
        },
        // 聊天交易
        async SendChatGold(params: ChatTransferParam) {
            return this.request(GLOBAL_URL.ChatTransfer, params)
        },
        /**
         * 获取实时可领取bonus
         * hybird.player.bonusexchange
         * @param msg {type:0,1 }  0:查询，1:领取
         * @return
         * {code: ERROR_CODES.OK, gold:0}
         * {code: ERROR_CODES.OK, gold:exchange}
         */
        async getBonus(type: number, loading = true) {
            return this.request(GLOBAL_URL.BonusExchange, { type }, { loading })
        },
    },
    persist: {
        key: 'BetFugu_base',
        storage: window.localStorage,
        paths: [
            'userInfo',
            'token',
            'lang',
            'wallet',
            'curSymbol',
            'newUser',
            'loginTime',
            'showVideoDrive',
            'chargeDate',
            'reelsConfig',
            'dramaStatus',
            'closeBind',
            'depositOrder',
            'signDate',
            'prevGameTag',
            'showAnnouncement',
        ],
    },
})
