import { Result } from '@/api/types'
import { Numeric } from 'vant/lib/utils'

export interface ServoConfig {}

export interface ChangeUserInfo {
    nickname?: string
    avatar?: string
    sex?: number
    country?: string
    birthday?: string
    picture?: string[]
    firebasetoken?: string
}

export interface BindPhone {
    country: string
    code: string
    telephone: string
}
export interface BindInviter {
    shareid: number
    auto: boolean //auto：是否自动绑定，
    from?: string // from：从哪个渠道来的
}

export interface GameSubscribe {
    gameid: number
    srctype: string
}

export interface PayOut {
    nickname: string
    gold: number
    paytype: string
}

export interface PayOutRes extends Result {}

export interface Chat {
    channel: string
    content: {
        context?: {
            item?: {
                channels: []
                worth: {
                    gold: number
                }
                gifts: {
                    bonus: number
                }
                discount: number
                cny: number
                instore: boolean
                first: boolean
                id: string
                name: string
                limittype: string
                currency: string
            }
        }
        receiver: number
        sender: number
        text: string
    }
    event: string
    id: number
    time: number
    showTime?: boolean
}
export interface HeaderMsg {
    code: number
    hotdatas: {
        [key: string]: string[]
    }
    users: {
        [key: string]: {
            uid: number
            nickname: string
            avatar: string
            reseller?: boolean
        }
    }
}
export interface SignParam {
    id: string
    version?: string
}

export interface JackpotActionParam {
    id: string
    method: string
    params: Record<string, any>
}

export interface ActionParam {
    id: string
    version?: string
    method: string
    params?: {
        [key: string]: any
    }
}

interface Context {
    condition: string
    conditiontype: string
    conditionvalue: string
    date: string
    index: string
    rewardnum: string
    rewardtype: string
    _id: string
    pick?: number
}
export interface Activity {
    avail?: number
    begintime?: string
    context?: Context[]
    endtime?: string
    index?: number
    status?: number
    title?: string
    _id?: string
    currency?: string
}
export interface Acticity {
    activity?: Activity
    name?: string
    title?: string
    day?: string
    status?: number
    user?: {
        begintime?: string
        endtime?: string
        currency?: string
        amount?: number
    }
    awardgold?: number
    bshowcount?: number
    count?: number
    currency?: string
    [key: string]: any
}
export interface SignRes extends Result {
    acticity: Acticity
    [key: string]: any
}

export interface ActionRes extends Result {
    user: RedfissionUser
    [key: string]: any
}
export interface Receive {
    id: string
    content?: {
        actid?: string
        cid?: string
        type?: string
        time?: number
        index?: number
        day?: string
    }
}
export interface Lamp {
    cron: string
    title: string
    context: {
        winner?: {
            uid?: number
            nickname?: string
        }
        gold?: number
        start?: number
        end?: number
    }
    time: number
}
export interface VipConfig {
    level: number
    exp: number
    keepExp: number
    reward: {
        gold: number
    }
    monthReward: {
        gold: number
    }
    weekReward: []
    betRebate: number
    depositBonus: number
    benefit: boolean
    withdrawAmount: number
    withdrawNoFeeTimes: number
    withdrawTimes: number
    weekRewardWorth: number
    birthday: boolean
}
export interface LevelupVip {
    level?: number
    reward?: {
        gold: number
    }
    monthReward?: {
        gold: number
    }
    weekReward: []
    betRebate?: number
    depositBonus?: number
    benefit?: boolean
    advanced?: boolean
    serviceUrl?: string
}
export interface VipInfo {
    uid?: number
    exp?: number
    version?: number
    checkType?: string
    lockLevelUp?: boolean
    curLevel?: number
    maxLevel?: number
    max?: number
    keepExp?: number
    keepMax?: number
    recoverExp: number
    recoverMax: number
    monthlevelup: boolean
    month?: string
    claimed?: []
    daily?: string
    dailyReward?: number
    advanced?: boolean
    serviceUrl?: string
    triggerRecover?: number
    levelUpReward?: LevelupVip[]
    monthReward?: LevelupVip
    weekReward?: LevelupVip[]
    triggerAdvanced: boolean
    depositBonus: number
    triggerDowngrade: boolean
    maintainLevel?: number
    recharge?: number
    rechargeMax?: number
    withdrawTimes?: number
    withdrawNoFeeTimes?: number
    withdrawAmount?: number
    birthday?: boolean
    weekRemain?: number
    monthRemain?: number
}
export interface VipRules {
    levelupFlowmult?: number
    maintainFlowmult?: number
    dailyFlowmult?: number
}
export interface ChatTransferParam {
    gold: number
    uid: number
}

export interface RedfissionConfig {
    status: boolean
    redinit: [] //[29800, 29900] // 红包初始范围
    period: number //99 // 期数
    periodday: number //5 // 一期多少天
    flowmult: number //流水倍数
    rewardNum: number //30000 // 奖励金币数
    dayfreespin: number //1 // 每天奖励免费次数
    rewardplayers: number //50 // 奖金池人数
    needrewardnum: number //10 // 加入奖金池最少人数
    needplayernum: [] //[1, 1] //
    ispayment: boolean //true // 有效人数是否充值
    freegamescount: number //10 // 有效人数免费游戏次数
    freespinconfig: [] // freespin配置
    freeaspinddlist: [] // freespin add配置
}

export interface RedfissionUser {
    version: number
    rednum: number //当前红包数值，进度
    initrednum: number //初始的数值
    successInviteNum: number //-1, //成功邀请人数多少人后可以领取, -1表示没有操作现在
    rewardNum: number //-1, //奖励金币数
    endTime: string // moment().add(redfissionOptions.periodday, 'd').toISOString(), //结束时间
    needRealReward: number //总奖金，用这个值显示，不要用config里的值显示总数
    freespin: number //Number(redfissionOptions.dayfreespin||0),   //免费次数
    freespintime: string //moment().toISOString()
    cheat: number //：1或0或不存在， 1代表是作弊，
    canexamine: number //1是达到了有效人数，可以手动审核 需要判断用户的rednum和needRealReward相等，否则还需要转一次转盘
    examine: number //1是提交手动审核后，正在审核中，2是审核完成，可以领取奖励了
    pick: number //：1是领取完成
}

export interface RedfissionInfo {
    Configs?: RedfissionConfig
    status?: number //status是0 不显示红包 status是1， 没有user，可以开启红包
    user?: RedfissionUser
    rankList?: any[]
    redrecords?: []
    // show?: number
    randomInit?: number[]
    [key: string]: any
}
