import { createI18n } from 'vue-i18n'
// import { getLocalLang } from '@/utils'
import { loadLocaleMessages } from './locales'

// import messages from './locales/index.js'

// 支持的语言列表
export const SUPPORT_LOCALES = ['zh-CN', 'en-US']
const i18n = createI18n({
    legacy: false,
    locale: 'en-US', // 默认显示语言
    fallbackLocale: 'en-US',
    messages: loadLocaleMessages('en-US').default,
})
const originT = i18n.global.t
i18n.global.t = function (key, ...res) {
    try {
        const result = originT.call(this, key, ...res)
        return result === '^nbsp$' ? '' : result
    } catch (e) {
        return key
    }
}
export default i18n
// 在需要切换语言时调用此函数
export async function setLocaleLang(locale) {
    const messages = await loadLocaleMessages(locale)
    i18n.global.setLocaleMessage(locale, messages.default)
    i18n.global.locale.value = locale
}
