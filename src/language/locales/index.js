export function loadLocaleMessages(locale) {switch (locale) {
case 'tl-PH':
            return import('./tl-PH.json')
case 'en-US':
            return import('./en-US.json')
case 'id-ID':
            return import('./id-ID.json')
case 'hi-IN':
            return import('./hi-IN.json')
case 'pt-BR':
            return import('./pt-BR.json')
case 'tr-TR':
            return import('./tr-TR.json')
 default:
            return import('./en-US.json')}}