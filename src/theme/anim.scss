.anim-bounce {
    animation: bounce 0.1s ease-in-out 5 alternate;
}

@keyframes bounce {
    from {
        transform: translateX(-1rem);
    }
    to {
        transform: translateX(1rem);
    }
}
// 点赞动画
.love-dbclick {
    position: absolute;
    $width: 90px;
    width: $width;
    height: $width;
    z-index: 3;

    &.left {
        animation: loveLeft 1.1s linear;
    }

    &.right {
        animation: loveRight 1.1s linear;
    }

    $scale: scale(1.2);
    $start-scale: scale(2.2);
    $rotate: 10deg;

    @keyframes loveLeft {
        0% {
            opacity: 0;
            transform: $start-scale rotate(0-$rotate);
        }
        10% {
            opacity: 1;
            transform: scale(1) rotate(0-$rotate);
        }
        15% {
            opacity: 1;
            transform: $scale rotate(0-$rotate);
        }
        40% {
            opacity: 1;
            transform: $scale rotate(0-$rotate);
        }
        100% {
            transform: translateY(-12px) scale(2) rotate(0-$rotate);
            opacity: 0;
        }
    }
    @keyframes loveRight {
        0% {
            opacity: 0;
            transform: $start-scale rotate(0 + $rotate);
        }
        10% {
            opacity: 1;
            transform: scale(1) rotate(0 + $rotate);
        }
        15% {
            opacity: 1;
            transform: $scale rotate(0 + $rotate);
        }
        40% {
            opacity: 1;
            transform: $scale rotate(0 + $rotate);
        }
        100% {
            transform: translateY(-12px) scale(2) rotate(0 + $rotate);
            opacity: 0;
        }
    }
}

.love {
    position: absolute;
    width: 50px;
    height: 50px;
    z-index: 30;
}

li:active {
    opacity: 1;
    transition: 0.1s opacity;
}
@keyframes move {
    100% {
        transform: rotate(0) translate(0, -600px);
    }
}
@keyframes rotate {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}
@keyframes rotated {
    0% {
        transform: translate3d(-50%, -50%, 0) rotateZ(360deg);
    }
    100% {
        transform: translate3d(-50%, -50%, 0) rotateZ(0deg);
    }
}
