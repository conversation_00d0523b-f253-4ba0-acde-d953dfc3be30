.wallet-nav {
    .van-tabs__wrap {
        height: 80px;
        overflow: inherit;
    }
    .van-tabs__nav {
        padding-bottom: 0;
        border-bottom: 4px solid #c6c6c6;
        border-radius: 2px;
    }
    .van-tab__text {
        font-size: 36px;
        font-weight: bold;
        color: #6d6d6d;
    }
    .van-tab--active {
        .van-tab__text {
            color: #a11ccf;
        }
    }
    .van-tabs__line {
        width: 220px;
        height: 11px;
        bottom: -7px;
        background-image: linear-gradient(rgba(180, 38, 255, 0.4), rgba(180, 38, 255, 0.4)),
            linear-gradient(150deg, #c240a4 0%, #b22fb9 40%, #a11dce 100%);
        background-blend-mode: normal, normal;

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            margin-left: -20px;
            width: 0;
            height: 0;
            border-left: 20px solid transparent; /* 左边框透明 */
            border-right: 20px solid transparent; /* 右边框透明 */
            border-top: 20px solid #ad25dc; /* 上边框为黑色，形成箭头的头部 */
            display: inline-block;
        }
    }
}
.common-notice {
    padding: 0 20px;
    height: 80px;
    line-height: 80px;
    font-size: 26px;
    z-index: 150;
    text-align: center;
    background: rgba(0, 0, 0, 0.9) !important;
    color: #fff;
}
.vip-animate {
    border-radius: 20px;
    animation: glow 1.5s infinite alternate;

    @keyframes glow {
        from {
            box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }
        to {
            box-shadow: 0 0 20px rgba(255, 215, 0, 1);
        }
    }
}
