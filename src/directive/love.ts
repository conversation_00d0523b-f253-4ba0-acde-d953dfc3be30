import type { App } from 'vue'
import Dom from '@/utils/dom'
import bus, { EVENT_KEY } from '@/utils/bus'
import { random } from '@/utils'

export function useLove(app: App) {
    app.directive('love', {
        mounted(el, binding) {
            let isDbClick = false
            let clickTimer = null
            let dbClickTimer = null
            let lastClickTime = null
            let isLiked = false
            let isDown = false
            let isMove = false
            const checkTime = 200
            const dbCheckCancelTime = 500
            const dbClick = (e) => {
                const id = 'a' + Date.now()
                const rotate = random(0, 80)
                const expression = ['😀', '🤣', '❤️', '😻', '👏', '🤘', '🤡', '🤩', '👍🏼', '🐮', '🎈', '💕', '💓', '💚']
                const template = `<div class="love" id="${id}" >${expression[random(0, expression.length - 1)]}</div>`
                const el = new Dom().create(template)
                el.css({
                    transform: `rotate(${rotate - 30}deg)`,
                    animation: ` move ${random(0, 2500) + 1500}ms 1 linear`,
                    top: e.y - 80,
                    left: e.x - 20,
                    fontSize: '50px',
                })
                new Dom(`#${binding.value}`).append(el)
                const dom = el.els[0]
                dom.addEventListener('animationend', () => {
                    dom.remove()
                })
            }
            const check = (e) => {
                if (isDbClick) {
                    clearTimeout(dbClickTimer)
                    dbClick(e)
                    if (!isLiked) {
                        isLiked = true
                        bus.emit(EVENT_KEY.DOUBLE_CLICK, binding.value)
                    }
                    dbClickTimer = setTimeout(() => {
                        isDbClick = false
                        isLiked = false
                    }, dbCheckCancelTime)
                    return
                }
                const nowTime = new Date().getTime()
                if (nowTime - lastClickTime < checkTime) {
                    clearTimeout(clickTimer)
                    dbClick(e)
                    isDbClick = true
                    dbClickTimer = setTimeout(() => {
                        isDbClick = false
                    }, dbCheckCancelTime)
                } else {
                    clickTimer = setTimeout(() => {
                        bus.emit(EVENT_KEY.SINGLE_CLICK, binding.value)
                    }, checkTime)
                }
                lastClickTime = nowTime
            }
            const up = (e) => {
                if (!isDown) return
                if (!isMove) check(e)
                isMove = isDown = false
            }
            el.addEventListener('pointerdown', () => (isDown = true))
            el.addEventListener('pointermove', () => isDown && (isMove = true))
            el.addEventListener('pointerup', up)
        },
    })
}
