import { createApp } from 'vue'

import 'amfe-flexible'
import pinia from '@/stores/index'
import App from '@/App.vue'
import router from '@/router'
import i18n from '@/language'
import { tgLogin } from '@/api/login'
import { useBaseStore } from '@/stores'
// chekVideoMuteState,
import { getUrlQuery, changeImgHost, getHashParams } from '@/utils'
import { directive } from '@/directive'
import { Lazyload } from 'vant'
import { H5Login } from '@/utils/H5Login'
import aes from '@/utils/aes'
import webApp from '@twa-dev/sdk'
import { Log } from '@/api/log'
import '@/theme/index.scss'
import { getDefault } from '@/api/servo'

// import { registerSW } from 'virtual:pwa-register'
// import { Workbox } from 'workbox-window'
// import { showConfirmDialog } from 'vant'

// import eruda from 'eruda'

// if (import.meta.env.MODE !== 'production') {
//     eruda.init()
// }

// if ('serviceWorker' in navigator && !['betfugu.com'].includes(location.host)) {
//     const wb = navigator.serviceWorker.register('/_sw.js')
//     // wb.register()
//     // 监听激活事件（可选）
//     // wb.addEventListener('activated', (event) => {
//     //     if (event.isUpdate) {
//     //         console.log('Service Worker 已更新，刷新页面生效')
//     //     }
//     // })
// }
if ('serviceWorker' in window.navigator && !['betfugu.com'].includes(location.host)) {
    navigator.serviceWorker
        .register('/_sw.js', { scope: '/' })
        .then(function (registration) {
            registration.update()
        })
        .catch((e) => {
            console.log(e)
        })
}

const checkUpdate = async () => {
    // console.log('checkUpdate------1---', 'connection' in navigator && !navigator.onLine)

    if ('connection' in navigator && !navigator.onLine) return
    const url = location.protocol + '//' + location.host + location.pathname + 'version.json?date=' + Date.now()
    // console.log('checkUpdate------2---', url)
    try {
        const resp = await fetch(url, {
            cache: 'no-store',
            headers: {
                cache: 'no-store',
                'cache-control': 'no-cache',
            },
        })
        // console.log('checkUpdate------3---', __APP_VERSION__, resp)
        if (resp?.status === 200) {
            const data = await resp.json()
            // console.log('checkUpdate------4---', __APP_VERSION__, data.version)
            if (__APP_VERSION__ !== data.version) {
                showConfirmDialog({
                    title: i18n.global.t('sys_update_info'),
                    message: i18n.global.t('new_version_update_info'),
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    zIndex: 9999,
                })
                    .then(() => {
                        location.reload()
                    })
                    .catch(() => {})
            }
        }
    } catch (e) {
        console.log(e)
    }
}

window.addEventListener('visibilitychange', async () => {
    if (document.visibilityState === 'visible') {
        setTimeout(() => {
            checkUpdate()
        }, 2000)
    }
})

const app = createApp(App)

app.use(pinia)
    .use(router)
    .use(i18n)
    .use(Lazyload, {
        lazyComponent: true,
        filter: {
            // 在加载前处理图片 URL
            process(lazy) {
                // 替换旧域名为新域名

                lazy.src = changeImgHost(lazy.src)
                return lazy
            },
        },
        preLoad: 1.1,
    })
directive(app)
app.config.errorHandler = (err, instance, info) => {
    Log({
        event: 'app_error',
        content: {
            err,
            message: err?.message,
            stack: err?.stack,
            info,
            url: location.href,
        },
    })
    console.error(err)
}

function expandApp() {
    if (webApp.isExpanded) return
    webApp.expand()
}

const store = useBaseStore()
aes.setPureKey(import.meta.env.VITE_PURE)

store.setGameConfig()

let initData = webApp.initData
console.log('getDefault------1-----', store.wallet?.currency)
if (!store.wallet?.currency) {
    const currency = localStorage.getItem('currency')?.toUpperCase() || getHashParams('currency')
    console.log('getDefault------2-----', currency)
    if (currency) {
        store.setWallet({
            currency: currency,
        })
    } else {
        getDefault()
            .then((res) => {
                console.log('getDefault------3-----', res)

                if (res.code === 200 && res.data.defCurrency) {
                    store.setWallet({
                        currency: res.data.defCurrency,
                    })
                } else {
                    store.setWallet({
                        currency: 'PHP',
                    })
                }
            })
            .catch(() => {
                store.setWallet({
                    currency: 'PHP',
                })
            })
    }
}
if (initData) {
    store.setToken('')
    initData = getUrlQuery(initData)
    store.setTgData(initData)
    webApp.ready()
    expandApp()
    webApp.disableVerticalSwipes()
    const enParam = aes.aesEncrypt({
        userinfo: {
            partner: '',
            telcontry: '',
            unionid: initData.id,
            nickname: '',
            verificode: '',
            accesstoken: '',
            deviceid: '',
            avatar: '', //fbinfo.picture.data.url
        },
    })
    tgLogin({ enParam }).then((res) => {
        store.setToken(res.token)
        checkUpdate()
        app.mount('#app')
    })
} else {
    checkUpdate()
    app.mount('#app')
}
// 延迟加载google facebook js
if (window.requestIdleCallback) {
    window.requestIdleCallback(() => {
        H5Login.init()
    })
} else {
    H5Login.init()
}

// })
// })
// chekVideoMuteState().then((canPlay) => {
//     const store = useBaseStore()
//     if (!canPlay) {
//         store.isMuted = true
//     }
// })
// document.addEventListener('touchstart', function (e) {
//     // e.stopPropagation()
//     // 处理触摸开始事件
// })
// document.addEventListener('touchmove', function (e) {
//     console.log('touchmove', e)
//     // e.stopPropagation()
//     // 处理触摸移动事件
// })
// document.addEventListener('touchend', function (e) {
//     e.stopPropagation()
//     // 处理触摸结束事件
// })
