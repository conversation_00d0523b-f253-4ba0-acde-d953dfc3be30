import request from '@/utils/request'
import { ResultData, Result } from '../types'
import { GLOBAL_URL } from '@/enums'
import { Events, Avatar } from './types'

// 获取活动列表
export function getEventsList(): Promise<ResultData<Events[]>> {
    return request({
        method: 'post',
        url: GLOBAL_URL.GetActivities,
    })
}

// 通知活动信息已读

export function sendActivityReaded(_id: string): Promise<Result> {
    return request({
        method: 'post',
        url: GLOBAL_URL.ActivityzoneRead,
        data: {
            _id,
        },
        loading: false,
    })
}

export function getAvatar(uids: string[]): Promise<Avatar> {
    return request({
        method: 'post',
        url: GLOBAL_URL.GetAvatar,
        data: {
            uids,
        },
        loading: false,
    })
}
