export interface Events {
    avail: number
    begintime: string
    endtime: string
    forever: number
    index: number
    link: string
    payOnly: boolean
    picture: string
    status: number
    title: string
    _id: string
    read: boolean
    [key: string]: any
}
export interface Avatar {
    code: number
    users: {
        [key: string]: {
            avatar: string
            nickname: string
            uid: number
        }
    }
}
