import request, { uploadServer } from '@/utils/request'
import { GLOBAL_URL } from '@/enums'
import { ResultData, Currency, CurrencySet } from './types'
export * from './login/index'
export * from './servo/index'
export * from './profile/index'
export * from './security/index'
export * from './notice/index'
export * from './video/index'
export * from './jackpot/index'

export function getCurrency(loading = false): Promise<ResultData<Currency>> {
    return request({
        url: GLOBAL_URL.GetCurrency,
        method: 'get',
        loading,
    })
}

export function setCurrency(currency: string): Promise<ResultData<CurrencySet>> {
    return request({
        url: GLOBAL_URL.SetCurrency,
        method: 'post',
        data: {
            currency,
        },
    })
}
export function getUserInfo(uids: string[]): Promise<{
    code: number
    users: {
        [key: string]: any
    }
}> {
    return request({
        url: GLOBAL_URL.getUser,
        method: 'post',
        data: {
            uids,
        },
    })
}
export function getExtentionId(params): Promise<ResultData<Currency>> {
    return request({
        url: 'https://pwa-backend-prod.roibest.com/fbclid/get',
        method: 'get',
        loading: false,
        params,
        showTip: false,
    })
}
// 输返记录
// Rebatelogs

export function getRebateLogs() {
    return request({
        url: GLOBAL_URL.Rebatelogs,
        method: 'post',
        showTip: false,
        loading: false,
    })
}

export function uploadPictrue(file: File) {
    const form = new FormData()
    form.append('file', file)
    return uploadServer({
        url: GLOBAL_URL.UploadPic,
        method: 'post',
        data: form,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    })
}

export function getFreeGamesConfig(): Promise<{
    code: number
    confs: []
    gifts: {
        [key: string]: any
    }
}> {
    return request({
        url: GLOBAL_URL.FreeGameItems,
        method: 'post',
        showTip: false,
        loading: false,
    })
}
