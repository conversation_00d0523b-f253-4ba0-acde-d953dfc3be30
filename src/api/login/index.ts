import request from '@/utils/request'
import { Sms, <PERSON><PERSON><PERSON>, FaceBookLogin, GoogleLogin, GuestLogin } from './type'
import { Result } from '../types'
import { GLOBAL_URL } from '@/enums'

/**
 * 用户登录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
/**
 * facebook登录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function facebookLogin(params: object, headers = {}): Promise<FaceBookLogin> {
    return request({
        url: GLOBAL_URL.FbLogin,
        method: 'post',
        data: params,
        headers,
    })
}
/**
 * google登录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function googleLogin(params: object, headers = {}): Promise<GoogleLogin> {
    return request({
        url: GLOBAL_URL.GgLogin,
        method: 'post',
        data: params,
        headers,
    })
}
// telegram 登录
export function tgLogin(params: object): Promise<GoogleLogin> {
    return request({
        url: GLOBAL_URL.TgLogin,
        method: 'post',
        data: params,
    })
}

export function getSms(params: Sms): Promise<Result> {
    return request({
        url: GLOBAL_URL.Sms,
        method: 'post',
        data: params,
    })
}
export function mobileLogin(params: Moblie, headers = {}): Promise<GoogleLogin> {
    return request({
        url: GLOBAL_URL.MobileLogin,
        method: 'post',
        data: params,
        headers,
    })
}
export function guestLogin(params: GuestLogin, headers = {}): Promise<GoogleLogin> {
    return request({
        url: GLOBAL_URL.GuestLogin,
        method: 'post',
        data: params,
        headers,
    })
}
