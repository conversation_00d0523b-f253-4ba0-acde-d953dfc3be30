import { Result } from '../types'

export interface Sms {
    apptitle: string
    country: string
    grant: string
    sendnum: number
    sign: string
    telephone: string
}
interface Userinfo {
    partner: number
    telcontry: string
    telephone: string
    code: string
    verificode: string
    grant: string
    country: string
    linkId?: string
    itemuserfor: string
}

export interface Moblie {
    userinfo: Userinfo
}

export interface GoogleLogin extends Result {
    token: string
    newuser: boolean
}
export interface FaceBookLogin extends Result {
    token: string
    newuser: boolean
}
export interface GuestLogin {
    wechatData: {
        openid: string
        nickname: string
        sex: number
        language: string
        city: string
        province: string
        country: string
        headimgurl: string
        privilege: string[]
        unionid: string
        partner: number
        telcontry: string
        linkId: string
    }
}
