import { ResultData } from '@/api/types'
import request, { paymentServer } from '@/utils/request'
import { GLOBAL_URL } from '@/enums'
import * as type from './type'
import service from '@/utils/request'

// 获取充值商品
export function getGamestore(uid: number): Promise<type.Gamestore> {
    return request({
        url: GLOBAL_URL.GameStorev1,
        method: 'post',
        data: {
            uid,
        },
    })
}

// 获取充值渠道
export function getAllChannels(uid: number, loading = true): Promise<type.AllChannels> {
    return request({
        url: GLOBAL_URL.GetAllChannels,
        method: 'post',
        data: {
            uid,
        },
        loading,
    })
}

// 充值下单
export function createPaysoOrder(params: type.PaysoOrder): Promise<type.CreatePaysoOrder> {
    return paymentServer({
        url: GLOBAL_URL.CreatePaysoOrder,
        method: 'post',
        data: params,
        loading: false,
    })
}

// 获取提现账号信息
export function getAccountInfo(loading = true): Promise<ResultData<type.AccountInfo>> {
    return request({
        url: GLOBAL_URL.AccountInfo,
        method: 'post',
        data: {},
        loading,
    })
}

//设置提提现账号
export function setAccountInfo(params: type.SetAccount): Promise<ResultData<type.SetAccountRes>> {
    return request({
        url: GLOBAL_URL.SetAccountInfo,
        method: 'post',
        data: params,
    })
}
// 获得提现记录

export function getPayoutOrders(params: type.Pages, loading = true): Promise<ResultData<type.PayoutOrders>> {
    return request({
        url: GLOBAL_URL.PayoutOrders,
        method: 'post',
        data: params,
        loading,
    })
}
// 获得充值记录
export function getOrders(params: type.Pages, loading = true): Promise<ResultData<type.PayoutOrders>> {
    return request({
        url: GLOBAL_URL.PayOrders,
        method: 'post',
        data: params,
        loading,
    })
}
// 游戏历史
export function getGameRecords(params: type.Pages, loading = true): Promise<ResultData<type.PayoutOrders>> {
    return request({
        url: GLOBAL_URL.GameRecords,
        method: 'post',
        data: params,
        loading,
    })
}
// 获取充值director弹窗
export function getDirector(): Promise<{ code: number }> {
    return request({
        url: GLOBAL_URL.getDirector,
        method: 'post',
        data: {},
        loading: false,
        showTip: false,
    })
}

export function uploadDirector(): Promise<{ code: number }> {
    return request({
        url: GLOBAL_URL.UpDirector,
        method: 'post',
        data: {},
        loading: false,
        showTip: false,
    })
}

export function getPayoutstore(uid): Promise<{ code: number } & type.PayoutStore> {
    return request({
        url: GLOBAL_URL.Payoutstore,
        method: 'post',
        data: { uid },
    })
}

// 获取reseller 列表
export function getResellers(uid): Promise<type.ResellersRes> {
    return paymentServer({
        url: GLOBAL_URL.GetResellers,
        method: 'post',
        data: { uid },
    })
}
export function getSelectreseller(param: { uid: number; reseller: number }): Promise<{ code: number; channel: string }> {
    return paymentServer({
        url: GLOBAL_URL.Selectreseller,
        method: 'post',
        data: param,
    })
}

export function checkOrder(param: { uid: number; orderId: string }): Promise<type.CheckOrder> {
    return paymentServer({
        url: GLOBAL_URL.CheckOrder,
        method: 'post',
        data: param,
        loading: false,
        showTip: false,
    })
}
