import { Result } from '../types'

export interface Store {
    channels: any[]
    worth: {
        gold: number
    }
    gifts: {
        bonus?: number
        gold?: number
    }
    discount: number
    cny: number
    instore: boolean
    first: boolean
    id: string
    name: string
    limittype: string
    currency: string
    giftpercentage: string
}

export interface Shoppayment {
    start: string //开始时间
    end: string //结算时间
    goldratio: number //筹码百分比
    bonusratio: number //bonus百分比
    flowmult: number //流水倍数
}

export interface Gamestore extends Result {
    methods?: {}[]
    channels?: {}[]
    items: Store[]
    reliefRecharge: number
    shoppayment: Shoppayment
}

export interface Channel {
    channel: string
    icon: string
    productList: string[]
    status: number
    index: number
    pkgList: any[]
}

export interface AllChannels extends Result {
    result: Channel[]
}
export interface CreatePaysoOrder extends Result {
    body: {
        amount: string
        channel: string
        guideUrl: string
        remitno: string
        tipType: string
        orderId: string
    }
}

export interface PaysoOrder {
    uid: number
    productId: string
    channel: string
    osType: string
    tradeType: string
    redirectUrl: string
    bflowmult: boolean
    source: any
}
export interface SetAccount {
    firstname?: string
    lastname?: string
    address?: string
    phone?: string
    paytype: string
    [key: string]: any
}
export interface AccountInfo {
    balance?: number
    balanceTimesInfo?: {
        state: boolean
        times: number
        limit?: number
        noFeeTimes?: number
    }
    content?: {
        gcash: {
            address: string
            firstname: string
            lastname: string
            paytype: string
            phone: string
        }
        paytype: string
    }
    limit?: number
    bind?: boolean
    remainBet?: number
}
export interface SetAccountRes {
    result: {}
}
export interface Pages {
    from: number
    size: number
}
export interface PayoutOrders {
    hits: []
    max_score: number | null
    total: {
        relation: string
        value: number
    }
}
export interface Conditon {
    cashout: number
    cashoutstate: boolean
    cashouttimes: number
    currency: string
    fee: number
    feepercent: number
}
export interface PayoutStore {
    isAuto: boolean
    items: { channels: []; cny: number; currency: string; id: string; name: string; vip: number }[]
    conditon?: Conditon
    channels: any[]
}

export interface Resellers {
    uid: number
    nickname: string
    avatar: string
    online: number
    index: number
}
export interface ResellersRes {
    code: number
    resellers: Resellers[]
}
export interface CheckOrder {
    code: number
    status: string
    info: {
        channelNo: string
        timestamp: string
        id: string
        expireat: string
        uid: number
        item: {
            channels: []
            worth: {
                gold: number
            }
            gifts: {
                bonus?: number
                gold?: number
            }
            discount: number
            cny: number
            instore: boolean
            usefor: string
            first: false
            id: string
            name: string
            limittype: string
            giftpercentage: string
            currency: string
            source: string
        }
        state: string
        osType: string
        paysoTax: number
        partner: number
    }
}
