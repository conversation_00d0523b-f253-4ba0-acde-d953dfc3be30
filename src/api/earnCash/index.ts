import request from '@/utils/request'
import { ResultData } from '../types'
import { GLOBAL_URL } from '@/enums'
import { EarnInfo, Exchange, RankTop, Page, Toppromoter, PromoterInvite, Contribution, Beanexchange, AgentStatus } from './types'

export const getEarnInfo = (): Promise<ResultData<EarnInfo>> => {
    return request({
        url: GLOBAL_URL.EarnInfo,
        method: 'post',
        data: {},
    })
}
export const getNotLoginEarnInfo = (): Promise<ResultData<EarnInfo>> => {
    return request({
        url: GLOBAL_URL.EarnInfoNotLogin,
        method: 'post',
        data: {},
    })
}

export const getAgentStats = (): Promise<ResultData<AgentStatus>> => {
    return request({
        url: GLOBAL_URL.AgentStats,
        method: 'post',
        data: {},
    })
}

export const getEarnBroadcast = (): Promise<ResultData<Exchange[]>> => {
    return request({
        url: GLOBAL_URL.BeanExchangeRecords,
        method: 'post',
        data: {},
    })
}
export const getRankTop = (params: Page): Promise<ResultData<RankTop>> => {
    return request({
        url: GLOBAL_URL.RankTop,
        method: 'post',
        data: params,
        loading: false,
    })
}
export const getNotLoginRankTop = (params: Page): Promise<ResultData<RankTop>> => {
    return request({
        url: GLOBAL_URL.RankTopNotLogin,
        method: 'post',
        data: params,
        loading: false,
    })
}
export const getPpopulars = (params: Page): Promise<ResultData<RankTop>> => {
    return request({
        url: GLOBAL_URL.Ppopulars,
        method: 'post',
        data: params,
        loading: false,
    })
}
export const getNotLoginPpopulars = (params: Page): Promise<ResultData<RankTop>> => {
    return request({
        url: GLOBAL_URL.PpopularsNotLogin,
        method: 'post',
        data: params,
        loading: false,
    })
}
export const showENStar = (): Promise<ResultData<{}>> => {
    return request({
        url: GLOBAL_URL.ShowENStar,
        method: 'post',
        data: {},
    })
}
// T1邀请列表
export const getPromoterInvite = (params: Page): Promise<ResultData<PromoterInvite[]>> => {
    return request({
        url: GLOBAL_URL.PromoterInvite,
        method: 'post',
        data: params,
        loading: false,
    })
}
// 返利记录
export const getContribution = (params: Page): Promise<ResultData<Contribution[]>> => {
    return request({
        url: GLOBAL_URL.Contributionlogs,
        method: 'post',
        data: params,
        loading: false,
    })
}

//拉手top100 收益详情
export const getToppromoter = (params: Page): Promise<ResultData<Toppromoter>> => {
    return request({
        url: GLOBAL_URL.Toppromoter,
        method: 'post',
        data: params,
        loading: false,
    })
}
export const getBeanexchangelogs = (params: Page): Promise<ResultData<Beanexchange[]>> => {
    return request({
        url: GLOBAL_URL.Beanexchangelogs,
        method: 'post',
        data: params,
        loading: false,
    })
}
