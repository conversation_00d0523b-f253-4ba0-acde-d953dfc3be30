export interface EarnInfo {
    current: number
    total: number
    invitetotal: number
    today: number
    day7: number
    day30: number
    agentrate: number
    sharerate: number
    sharerate2: number
    starinfo: {
        shareStar?: number
        showenstar?: any
    }
}
export interface Exchange {
    uid: number
    count: number
    nickname: string
}

export interface Members {
    uid: number
    nickname: string
    avatar: string
    sex?: number
    timestamp?: number
}
export interface Score {
    [key: string]: number
}
export type RankItem = Partial<Members & Score>
export type Ranks = RankItem[]
export interface RankTop {
    populars: {
        members: Members[]
        scores: Score
    }
    self: {
        index: number
        score: number
    }
}
export interface Page {
    start?: number
    stop?: number
    type?: string
    from?: number
    size?: number
}
export interface Toppromoter {
    results: {
        members: Members[]
        scores: Score
    }
}
export interface PromoterInvite {
    [key: string]: any
    _source: {
        time: string
        uid: number
        nickname: string
        avatar: string
        [key: string]: any
    }
}
export interface Contribution {
    _source: {
        context: {
            income: number
            uid: number
            game: string
            currency: string
        }
    }
    [key: string]: any
}
export interface Beanexchange {
    _index: string
    _id: string
    _score: number
    _source: {
        context: {
            count: number
            currency: string
            latest: number
        }
        time: string
    }
    sort: any
}
export interface AgentStatus {
    current: number // 金豆余额
    starinfo: number // 星级信息
    invitetotal: number // 总邀请数
    invitetoday: number // 今日新增邀请
    total: number // 总返利
    today: number // 今日返利
    activetoday: number // 今日活跃
    inviteweek: number // 本周新增邀请
    activeweek: number // 本周活跃
    incomeweek: number // 本周返利
}
