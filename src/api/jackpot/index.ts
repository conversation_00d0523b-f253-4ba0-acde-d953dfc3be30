/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-25 13:49:27
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-25 14:42:15
 * @FilePath     : /src/api/jackpot/index.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-25 13:49:27
 */
import request from '@/utils/request'
import { ResultData } from '../types'
import { DaylyJackpotData } from '@/components/jackpot/types'

/**
 * 通过短链接方式获取每日Jackpot数据
 * @param params 请求参数，包含id和version
 * @returns Promise<ResultData<DaylyJackpotData>>
 */
export const getDaylyJackpot = (params: { id: string; version?: string }, loading = true): Promise<ResultData<DaylyJackpotData>> => {
    return request({
        url: '/opendata/getdaylyjackpot',
        method: 'post',
        data: params,
        loading,
        showTip: false, // 不显示错误提示
    })
}
