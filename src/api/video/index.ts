import { ResultData, Result } from '@/api/types'
import request from '@/utils/request'
import { GLOBAL_URL } from '@/enums'
import { VideoParams, VideoRes, Favorite, Thumb, VideoMsg, VideoDetail } from './types'

export function getVideoList(params: VideoParams): Promise<ResultData<VideoRes>> {
    return request({
        url: GLOBAL_URL.VideoList,
        method: 'post',
        data: params,
        loading: false,
    })
}

export function doFavorite(params: Favorite): Promise<Result> {
    return request({
        url: GLOBAL_URL.Favorite,
        method: 'post',
        data: params,
        loading: false,
    })
}
export function doThumb(params: Thumb): Promise<Result> {
    return request({
        url: GLOBAL_URL.Thumb,
        method: 'post',
        data: params,
        loading: false,
    })
}
export function doRetweet(id: string): Promise<Result> {
    return request({
        url: GLOBAL_URL.VideoRetweet,
        method: 'post',
        data: {
            id,
        },
        loading: false,
    })
}
export function getVideoMsg({ id, gameid }: { id: string; gameid: string }): Promise<ResultData<VideoMsg>> {
    return request({
        url: GLOBAL_URL.VideoMsg,
        method: 'post',
        data: {
            id,
            gameid,
        },
        loading: false,
        showTip: false,
    })
}
//视频已付费集数列表
export function getVideoPayByDrama(params: { dramaid: string }): Promise<Result & { unlock: number[] }> {
    return request({
        url: GLOBAL_URL.GetVideoPayByDrama,
        method: 'post',
        data: {
            ...params,
        },
        loading: false,
        showTip: true,
    })
}
//获取视频付费信息
export function getVideoPayAmountByDrama(params: { dramaid: string; index: number }): Promise<Result & { amount: number; all: number }> {
    return request({
        url: GLOBAL_URL.GetVideoPayAmountByDrama,
        method: 'post',
        data: {
            ...params,
        },
        loading: true,
        showTip: true,
    })
}

//获取剧集某一集信息
export function getVideoDramaIndex(params: { dramaid: string; index: number }): Promise<
    Result & {
        video: VideoDetail
        self: {
            [key: string]: {
                favorite: number
                like: number
            }
        }
    }
> {
    return request({
        url: GLOBAL_URL.GetVideoDramaIndex,
        method: 'post',
        data: {
            ...params,
        },
        loading: true,
        showTip: true,
    })
}

//视频支付
export function payVideoPayDrama(params: { dramaid: string; index: number }): Promise<Result & { unlock: number[] }> {
    return request({
        url: GLOBAL_URL.VideoPayDrama,
        method: 'post',
        data: {
            ...params,
            send: true,
        },
        loading: true,
        showTip: true,
    })
}
