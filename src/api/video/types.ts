export interface VideoParams {
    id?: string
    page: number
    size: number
    withDrama?: boolean
}
export interface VideoDetail {
    srctype?: number
    avail?: number
    creatat?: string
    rank?: number
    language?: string
    id?: string
    videolink?: string
    videopic?: string
    gametag?: string
    type?: number
    dramaid?: string
    name?: string
    maxsize?: number
    serialindex?: number
    pay?: boolean
    paymin?: number
    conf?: {
        language: string
        gametag: string
        name: string
        gamefirm: string
        rtp: number
        maxbet: number
        picture: string
    }
}
export interface VideoRes {
    from: number
    total: number
    page: number
    videos: VideoDetail[]
    selfVideo: {
        [key: string]: {
            favorite: number
            like: number
        }
    }
}
export interface Favorite {
    id: string
    favorite: boolean
}

export interface Thumb {
    id: string
    like: boolean
}
export interface VideoMsg {
    gameonlines: number
    message: {
        uid: string
        nickname: string
        avatar: string
        time: string
    }[]
    avators: string[]
}
