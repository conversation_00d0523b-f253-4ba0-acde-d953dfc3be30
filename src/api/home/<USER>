// 移除无效的导入
interface PicItem {
    link: string
    picture: string
    params: string
}
interface Banner {
    end: string
    index: string
    jump: string
    name: string
    param: string
    picture: string
    start: string
    logout: boolean
    onlylogout: boolean
}

export interface Categorys {
    language?: string
    name?: string
    picture?: string
    menupicture: string
    platform?: string[]
    rank?: number
    type?: string
    carouselpictures?: PicItem[]
    treepictures?: PicItem[]
}
interface Gameconfigs {
    gamefirm: String
    gametag: String
    language: String
    maxbet: number
    name: String
    picture: String
    rtp: number
    activity: boolean
    activitylink: string
}
interface Gamepages {
    category: String
    gametag: String
    language: String
    platform: String[]
    rank: String
}
interface GameStat {
    gameHot: number
    gameonline: number
}
interface RegistGiftDataConfig {
    worth: number
    win: number
    items: { id: string; num: number }[]
}
interface RegistGiftDataItems {
    [key: string]: { id: string; name: string; img: string; desc: string; subDesc: string; worth: string; useFor: string }
}
export interface RegistGiftData {
    config?: RegistGiftDataConfig
    items?: RegistGiftDataItems
    claimed?: boolean
}
export interface Homelist {
    config?: RegistGiftDataConfig
    items?: RegistGiftDataItems
    claimed?: boolean
    gameCategorys: {
        [key: string]: Categorys
    }
    categorys: Categorys[]
    gameconfigs: {
        [key: string]: Gameconfigs
    }
    gamepages: {
        [key: string]: Gamepages[]
    }
    gameselfpages: {
        [key: string]: Gameconfigs[]
    }
    gameStat: {
        [key: string]: GameStat
    }
    // 🔍 添加供应商/厂商字段
    gamefirms: {
        [key: string]: {
            name: string
            tag: string
            abbr: string
            avatar: string
            isstop: boolean
            [key: string]: any
        }
    }
    history: { count: number; gametag: string; time: number }[]
    banner: Banner[]
    marqueeInterval: number
    isReseller?: boolean
}
type game = Gamepages & Gameconfigs
export interface PropTypes {
    index: number
    list?: game[]
    carousel?: PicItem[]
    events?: PicItem[]
    item?: any
    tag?: string
}
export interface Wallet {
    currency: string
    gold: number
    balance: number
    bonus: number
    betbalance?: number
}
export interface InitLangAndCurrency {
    language: string
    currency: Wallet
}
export interface PackageConfs {
    id: string
    name: string
    img: string
    desc: string
    subDesc: string
    worth: string
    useFor: string
    jump: string
}
export interface PackageFree {
    [key: string]: number
}
export interface Package {
    freePackage: {
        [key: string]: number
    }
    confs: {
        [key: string]: PackageConfs
    }
}

export interface Announcement {
    index: number
    begintime: string
    endtime: string
    showtype: number
    hidetype: number
    link: string
    noticetitle: string
    noticecontent: string
    picture: string
    status: number
    title: string
}
