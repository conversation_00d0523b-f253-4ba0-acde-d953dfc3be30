import request, { uploadServer } from '@/utils/request'
import { ResultData } from '../types'
import { UploadAvatar, UserInfoRes, BindPhone } from './types'
import { GLOBAL_URL } from '@/enums/api'

// 上传头像
export function uploadFile(file: File): Promise<UploadAvatar> {
    const form = new FormData()
    form.append('file', file)
    return uploadServer({
        url: GLOBAL_URL.UploadAvatar,
        method: 'post',
        data: form,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    })
}
// 看修改个人信息
export function changeUserInfo(params): Promise<ResultData<UserInfoRes>> {
    return request({
        url: GLOBAL_URL.ChangeUserInfo,
        method: 'post',
        data: params,
    })
}

// 绑定手机号

export function bindPhone(params: BindPhone): Promise<ResultData<UserInfoRes>> {
    return request({
        url: GLOBAL_URL.BindPhone,
        method: 'post',
        data: params,
    })
}
