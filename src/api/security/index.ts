import request from '@/utils/request'
import { GLOBAL_URL } from '@/enums'
import { ThirdParam, BindRes } from './types'
import { ResultData } from '../types'

/**
 * facebook绑定
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function facebookBind(params: ThirdParam): Promise<ResultData<BindRes>> {
    return request({
        url: GLOBAL_URL.BindFacebook,
        method: 'post',
        data: params,
    })
}

/**
 * google绑定
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function googleBind(params: ThirdParam): Promise<ResultData<BindRes>> {
    return request({
        url: GLOBAL_URL.BindGoogle,
        method: 'post',
        data: params,
    })
}
/**
 * 发送邮箱验证码
 * @param params 要传的参数值
 * @returns 返回接口数据
 */

export function sendEmailCode(mailto: string): Promise<ResultData<BindRes>> {
    return request({
        url: GLOBAL_URL.SendEmail,
        method: 'post',
        data: {
            grant: 'authorize',
            mailto,
        },
    })
}
/**
 * 绑定邮箱
 * @param params 要传的参数值
 * @returns 返回接口数据
 */

export function bindEmail(params: { mail: string; code: string }): Promise<ResultData<BindRes>> {
    return request({
        url: GLOBAL_URL.BindEmail,
        method: 'post',
        data: {
            grant: 'authorize',
            ...params,
        },
    })
}
