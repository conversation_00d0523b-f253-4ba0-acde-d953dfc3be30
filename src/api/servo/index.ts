import request, { serVo } from '@/utils/request'
import { ServoConfig, WsServo } from './types'
import { GLOBAL_URL } from '@/enums/api'
import { ResultData } from '../types'
import webApp from '@twa-dev/sdk'

// 获取服务端配置
export const getServoConfig: () => Promise<ServoConfig> = () => {
    const { VITE_APPID, VITE_PUBLISHER, VITE_CHANNEL, VITE_TG_CHANNEL } = import.meta.env
    return serVo({
        url: `/${VITE_APPID}/${VITE_PUBLISHER}/${webApp.initData ? VITE_TG_CHANNEL : VITE_CHANNEL}/${__APP_VERSION__}`,
        method: 'get',
    })
}

export const getWsServoConfig: () => Promise<ResultData<WsServo>> = () => {
    return request({
        url: GLOBAL_URL.MetaData,
        method: 'get',
    })
}
export function getDefault(): Promise<
    ResultData<{
        defCurrency: string
    }>
> {
    return request({
        url: GLOBAL_URL.Default,
        method: 'get',
        loading: false,
    })
}
