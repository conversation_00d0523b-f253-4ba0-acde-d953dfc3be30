import { language } from '@/language'
export interface ServoConfig {
    geo: {
        area: number
        city: string
        country: string
        eu: string
        ip: string
        ll: string[]
        metro: number
        range: number[]
        region: string
        timezone: string
    }
    ll: number[]
    name: string
    pure?: string
    servers: string[]
    status: {
        text: string
        tips: string
    }
}
export interface LANGUAGE {
    language: string
    show: string
}
export interface CURRENCY {
    currency: string
    picture: string
    clientcurrency: string
    type: number
}

export interface WsServo {
    download: string
    env: string
    gameforbid: boolean
    gameserver: string[]
    payment: string
    reserve: string
    upload: string
    languages: LANGUAGE[]
    currencies: CURRENCY[]
    version: { android: string; ios: string }
    partner: string
}
