export interface Result {
    code: number
}
export interface ResultData<T> extends Result {
    data: T
}
interface Link {
    unionid: number
    nickname: string
}
export interface LoginUser {
    partner: number
    linkId: string
    avatar: string
    birthday: string
    city: string
    country: string
    createat: string
    ip: string
    nickname: string
    telephone: string
    bindtelephonepick?: string
    uid: number
    agentid?: number
    curlanguage: string
    curcurrecny: string
    linkgoogle: Link
    linkfacebook: Link
    linkmail: Link
    shareid?: string
    director: number
    guideLanguage?: string
    guideCurrency?: string
    [key: string]: any
}

export interface Gold {
    gold: number
}

export interface LoginGame extends Result {
    metadata: LoginUser
    items: Gold
}

export interface ConfsItem {
    currency: string
    type: number
}
export interface Currency {
    confs: ConfsItem[]
    currencys: {
        [key: string]: number
    }
}

export interface CurrencySet {
    currency: {
        currency: string // 货币类型cny
        gold: number // 钱数
        balance: number // 提现余额
    }
}
