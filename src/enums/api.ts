// 接口
export const GLOBAL_URL = {
    // 长链接
    SessionLogin: 'connector.session.login', // 游戏登录
    ProfilesAsync: 'hybird.profile.fetch', // 用户信息获取
    // ChangeUserInfo: 'hybird.profile.updatemetadata', //修改个人信息
    // BindPhone: 'hybird.profile.bindtelephone', // 绑定手机号
    ShareInviteBind: 'hybird.share.bind', //绑定邀请
    GameSubscribe: 'hybird.thirdgames.subscribe', // 游戏订阅
    GameUnSubscribe: 'hybird.thirdgames.unsubscribe', //游戏取消订阅
    Rejoingames: 'hybird.thirdgames.rejoingames', // 重新进入游戏
    GameInit: 'hybird.thirdgames.init', // 游戏订阅
    PayOut: 'hybird.pay.payout', // 提现
    GiftCdk: 'hybird.newplayer.cdk', // 礼物兑换码
    QueryBonuse: 'hybird.newplayer.bonusexchange', // 查询bounues
    GetHotMsg: 'hybird.friends.hotmessages', //获取频道热数据，保留20调记录
    GetHisMsg: 'hybird.friends.histories', //获取指定频道的历史记录
    FriendsSubscribe: 'hybird.friends.subscribe', // 进入实时订阅消息模式，点击channel，进入某个订阅
    FriendsUnsubscribe: 'hybird.friends.unsubscribe', // 取消实时订阅聊天消息
    GetFriendsMeta: 'hybird.friends.meta', // 获取未读数量
    Beanexchange: 'hybird.newplayer.beanexchange', // 兑换金币
    SendText: 'hybird.friends.sendtext', // 发送聊天消息
    Headermessage: 'hybird.friends.headermessage', // 查询聊天记录列表
    Sign: 'hybird.activities.fetch', // 签到活动
    action: 'hybird.activities.action', // 活动开启
    ReceiveRewards: 'hybird.activities.checkout', // 领取奖励
    JackpotAction: 'hybird.activities.action', // Jackpot相关操作
    OrderCancel: 'hybird.pay.payoutOrderCancel',
    Vip: 'hybird.betvip.vipInfo',
    PickRewads: 'hybird.betvip.pickReward',
    triggerVip: 'hybird.betvip.triggerClick',
    ChatTransfer: 'hybird.friends.sendgoldV2', // 聊天交易
    BonusExchange: 'hybird.player.bonusexchange', // 兑换bonus

    // 短链接
    Sms: '/sms', // 短信验证码
    UploadAvatar: '/user/upload/uploadimg', // 上传头像
    MetaData: '/opendata/configs?name=metadata', // 获取用户信息
    Default: '/opendata/configs?name=default',
    FreeGameItems: '/opendata/itemConfig', // 获取免费游戏配置
    MobileLogin: '/authorize/mobile', // 手机号登录
    ChangeUserInfo: '/user/profile/updatemetadata', //修改个人信息
    GgLogin: '/authorize/google',
    FbLogin: '/authorize/facebook', //
    GuestLogin: '/authorize/wechat', // 游客登录
    GetCurrency: '/user/profile/currency', //获得所有币种和金额
    SetCurrency: '/user/profile/setcurrency', // 设置币种
    GameStore: '/opendata/pay/gamestore', // 充值商品
    GameStorev1: '/opendata/pay/gamestorev1', // 商城信息
    GetAllChannels: '/opendata/pay/allChannels', // 充值商品
    CreatePaysoOrder: '/pay/createPaysoOrder', // 充值下单
    InitLangAndCurrency: '/user/profile/initLanguageCurrency', // 初始化语言和货币
    AccountInfo: '/user/pay/getAccountInfo', // 获取提现账号信息
    SetAccountInfo: '/user/pay/setAccountInfo', // 设置提提现账号
    PayoutOrders: '/user/pay/payoutOrders', // 获得提现记录
    PayOrders: '/user/pay/payOrders', // 获得充值记录
    Payoutstore: '/opendata/pay/payoutstore', // 提现档位
    Setlanguage: '/user/profile/setlanguage', // 设置语言
    GetHis: '/user/profile/history', // 获取home history数据历史
    GameRecords: '/user/profile/gameRecords', // 游戏记录
    BindPhone: '/user/bind/telephone', // 绑定手机号
    BindFacebook: '/user/bind/facebook', // 绑定facebook
    BindGoogle: '/user/bind/google', // 绑定google
    BindEmail: '/user/bind/mail', // 绑定邮箱
    SendEmail: '/user/bind/sendmail', // 绑定邮箱
    GetActivities: '/user/activities/activityzone', // 获取活动信息
    ActivityzoneRead: '/user/activities/activityzoneRead', // 设置活动信息已读
    VideoList: '/video/videolistByCy', // 视频列表
    GetVideoPayByDrama: '/video/getVideoPayByDrama', //视频已付费集数列表
    GetVideoPayAmountByDrama: '/video/getVideoPayAmountByDrama', // 获取视频付费信息
    GetVideoDramaIndex: '/video/getVideoDramaIndex', // 获取剧集某一集信息
    VideoPayDrama: '/video/videoPayDrama', // 视频支付
    Favorite: '/user/video/favorite', // 视频收藏
    Thumb: '/user/video/like', // 点赞
    VideoRetweet: '/user/video/retweet', // 视频转发
    Log: '/client/log', // 发送log
    VideoMsg: '/video/videomessageByCy', // 获取游戏在线人数
    EarnInfo: '/user/promoter/stats', // earnmoney 基础数据
    EarnInfoNotLogin: '/opendata/earn/stats', // earnmoney 未登录基础数据
    AgentStats: '/user/promoter/agentStats', //大区代理统计数据
    BeanExchangeRecords: '/user/promoter/beanExchangeRecords', // 获取兑换日志
    RankTop: '/user/promoter/rankCommissionTotal', // 收益总排行榜
    RankTopNotLogin: '/opendata/earn/rankCommissionTotal', // 未登录收益总排行榜
    Ppopulars: '/user/promoter/toppopulars', // 排行榜
    PpopularsNotLogin: '/opendata/earn/toppopulars ', // 未登录排行榜
    ShowENStar: '/user/promoter/showENStar', // earnMoney三星弹窗
    PromoterInvite: '/user/promoter/promoterInviteList', //T1邀请列表
    Toppromoter: '/user/promoter/topcontributor', //拉手top100
    Contributionlogs: '/user/promoter/contributionlogs', // 返利记录
    GetAvatar: '/opendata/userinfo', // 获取用户头像
    GetConfig: '/opendata/configs', // 获取配置文件信息
    Beanexchangelogs: '/user/promoter/beanexchangelogs', // reedem 提现记录
    getUser: '/opendata/userinfo', // 获取用户信息
    getDirector: '/user/profile/popDirectorMsg', // 获取用户充值后是否弹Director弹窗
    UpDirector: '/user/profile/clickpopDirector', // 通知接口
    Rebatelogs: '/user/profile/getrebatelogs', // 获取输返活动记录
    TgLogin: '/authorize/telegram', // telegram 登录
    UploadPic: '/user/upload/uploadFile', // 上传图片
    GetEvents: 'opendata/homepage/events', // 活动接口
    GetResellers: '/pay/resellers', // 获取resellers列表
    ResellerAutoReplay: 'user/profile/resellerAutoReply', // reseller自动回复文本
    Selectreseller: '/pay/selectreseller',
    RegistGifts: '/opendata/homepage/registGifts', // 注册礼
    ClaimRegistGifts: '/user/profile/claimRegistGifts', // 注册礼
    Getfreepackage: '/user/profile/getfreepackage',
    CheckOrder: '/pay/checkOrder', // 检查订单
    LastestBet: '/opendata/homepage/lastestBet',
    HighRoller: '/opendata/homepage/highRoller',
    HighMultiplier: '/opendata/homepage/highMultiplier',
    H5Announcement: '/user/activities/h5announcement', // H5公告
}
// 错误码
export enum ERROR_CODES {
    Continue = 100, ///继续。客户端应继续其请求
    SwitchingProtocols = 101, ///切换协议。服务器根据客户端的请求切换协议。只能切换到更高级的协议

    OK = 200, ///请求成功。一般用于GET与POST请求
    Created = 201, ///已创建。成功请求并创建了新的资源
    Accepted = 202, ///已接受。已经接受请求，但未处理完成
    NonAuthoritativeInformation = 203, ///非授权信息。请求成功。但返回的meta信息不在原始的服务器，而是一个副本
    NoContent = 204, ///无内容。服务器成功处理，但未返回内容。在未更新网页的情况下，可确保浏览器继续显示当前文档
    ResetContent = 205, ///重置内容。服务器处理成功，用户终端（例如：浏览器）应重置文档视图。可通过此返回码清除浏览器的表单域
    PartialContent = 206, ///部分内容。服务器成功处理了部分GET请求

    MultipleChoices = 300, ///多种选择。请求的资源可包括多个位置，相应可返回一个资源特征与地址的列表用于用户终端（例如：浏览器）选择
    MovedPermanently = 301, ///永久移动。请求的资源已被永久的移动到新URI，返回信息会包括新的URI，浏览器会自动定向到新URI。今后任何新的请求都应使用新的URI代替
    Found = 302, ///临时移动。与301类似。但资源只是临时被移动。客户端应继续使用原有URI
    SeeOther = 303, ///查看其它地址。与301类似。使用GET和POST请求查看
    NotModified = 304, ///未修改。所请求的资源未修改，服务器返回此状态码时，不会返回任何资源。客户端通常会缓存访问过的资源，通过提供一个头信息指出客户端希望只返回在指定日期之后修改的资源
    UseProxy = 305, ///使用代理。所请求的资源必须通过代理访问
    Unused = 306, ///已经被废弃的HTTP状态码
    TemporaryRedirect = 307, ///临时重定向。与302类似。使用GET请求重定向

    BadRequest = 400, ///客户端请求的语法错误，服务器无法理解
    Unauthorized = 401, ///请求要求用户的身份认证
    PaymentRequired = 402, ///保留，将来使用
    Forbidden = 403, ///服务器理解请求客户端的请求，但是拒绝执行此请求
    NotFound = 404, ///服务器无法根据客户端的请求找到资源（网页）。通过此代码，网站设计人员可设置"您所请求的资源无法找到"的个性页面
    MethodNotAllowed = 405, ///客户端请求中的方法被禁止
    NotAcceptable = 406, ///服务器无法根据客户端请求的内容特性完成请求
    ProxyAuthenticationRequired = 407, ///请求要求代理的身份认证，与401类似，但请求者应当使用代理进行授权
    RequestTimeout = 408, ///服务器等待客户端发送的请求时间过长，超时
    Conflict = 409, ///服务器完成客户端的PUT请求是可能返回此代码，服务器处理请求时发生了冲突
    Gone = 410, ///客户端请求的资源已经不存在。410不同于404，如果资源以前有现在被永久删除了可使用410代码，网站设计人员可通过301代码指定资源的新位置
    LengthRequired = 411, ///服务器无法处理客户端发送的不带Content-Length的请求信息
    PreconditionFailed = 412, ///客户端请求信息的先决条件错误
    RequestEntityTooLarge = 413, ///由于请求的实体过大，服务器无法处理，因此拒绝请求。为防止客户端的连续请求，服务器可能会关闭连接。如果只是服务器暂时无法处理，则会包含一个Retry-After的响应信息
    RequestURITooLarge = 414, ///请求的URI过长（URI通常为网址），服务器无法处理
    UnsupportedMediaType = 415, ///服务器无法处理请求附带的媒体格式
    RequestedRangeNotSatisfiable = 416, ///客户端请求的范围无效
    ExpectationFailed = 417, ///服务器无法满足Expect的请求头信息
    GameLocked = 418, //游戏已锁定不能加入

    InternalServerError = 500, ///服务器内部错误，无法完成请求
    NotImplemented = 501, ///服务器不支持请求的功能，无法完成请求
    BadGateway = 502, ///充当网关或代理的服务器，从远端服务器接收到了一个无效的请求
    ServiceUnavailable = 503, ///		由于超载或系统维护，服务器暂时的无法处理客户端的请求。延时的长度可包含在服务器的Retry-After头信息中
    GatewayTimeout = 504, ///		充当网关或代理的服务器，未及时从远端服务器获取请求
    VersionNotSupported = 505, ///服务器不支持请求的HTTP协议的版本，无法完成处理
}

const LOGIN_ERROR = {
    [ERROR_CODES.GatewayTimeout]: 'Login_info_unavailable_again',
    [ERROR_CODES.BadRequest]: 'Wrong_mobile_number',
    [ERROR_CODES.Unauthorized]: 'Wrong_verification',
    [ERROR_CODES.Forbidden]: 'Login_failed_please',
}
// 错误码提示
export const ERROR_MSG = {
    [GLOBAL_URL.ShareInviteBind]: {
        [ERROR_CODES.NotFound]: 'User_not_exist',
        [ERROR_CODES.NotImplemented]: 'NO_Bind_time_limit',
        [ERROR_CODES.BadRequest]: 'User_has_been_bound',
        [ERROR_CODES.Forbidden]: 'Binding_prohibite',
        [ERROR_CODES.OK]: 'Binding_successful',
    },
    [GLOBAL_URL.BindPhone]: {
        [ERROR_CODES.BadRequest]: 'Mobile_number_wrong',
        [ERROR_CODES.NotFound]: 'SMS_verification_wrong',
        [ERROR_CODES.NotAcceptable]: 'Mobile_number_bound',
        [ERROR_CODES.Found]: 'Mobile_number_bound',
        [ERROR_CODES.MethodNotAllowed]: 'Nobound_phone_cant_change',
        [ERROR_CODES.OK]: 'Binding_successful',
    },
    [GLOBAL_URL.UploadAvatar]: {
        [ERROR_CODES.Unauthorized]: 'You_no_permission_upload',
        [ERROR_CODES.BadRequest]: 'mime_type_wrong',
        [ERROR_CODES.Forbidden]: 'Upload_timed_out_tryagain',
        [ERROR_CODES.NotAcceptable]: 'Upload_failed_tryagain',
        // [ERROR_CODES.OK]: '上传成功',
    },
    [GLOBAL_URL.Sms]: {
        [ERROR_CODES.BadRequest]: 'Send_failed_wait',
        [ERROR_CODES.ResetContent]: 'Too_many_times_sent_wait',
        [ERROR_CODES.ServiceUnavailable]: 'Send_failed_wait',
        [ERROR_CODES.BadGateway]: 'SMS_num_max_tomor_try',
        [ERROR_CODES.VersionNotSupported]: 'Configuration_error',
        [ERROR_CODES.GatewayTimeout]: 'Send_failed_wait',
    },
    [GLOBAL_URL.ChangeUserInfo]: {
        [ERROR_CODES.Unauthorized]: 'You_do_not_have_perm',
    },

    [GLOBAL_URL.MobileLogin]: {
        ...LOGIN_ERROR,
        [ERROR_CODES.Unauthorized]: 'Incorrect_verification',
    },
    [GLOBAL_URL.GgLogin]: LOGIN_ERROR,
    [GLOBAL_URL.FbLogin]: LOGIN_ERROR,
    [GLOBAL_URL.GuestLogin]: LOGIN_ERROR,
    [GLOBAL_URL.InitLangAndCurrency]: {
        [ERROR_CODES.Unauthorized]: 'You_do_not_have_perm',
        [ERROR_CODES.Forbidden]: 'Language_selection_error',
        [ERROR_CODES.NotFound]: 'Currency_selection_error',
    },
    [GLOBAL_URL.CreatePaysoOrder]: {
        [ERROR_CODES.Unauthorized]: 'Deposit_channel_error_tryagain',
        [ERROR_CODES.ServiceUnavailable]: 'Deposit_amount_error_tryagain',
        [ERROR_CODES.NotAcceptable]: 'Deposit_limited_call_cs',
        [ERROR_CODES.BadRequest]: 'Failed_create_order',
    },
    [GLOBAL_URL.AccountInfo]: {
        [ERROR_CODES.Forbidden]: '[1090]currency_no_found',
        [ERROR_CODES.Unauthorized]: 'No_premission',
    },
    [GLOBAL_URL.SetAccountInfo]: {
        [ERROR_CODES.Forbidden]: 'Currency_no_found',
        [ERROR_CODES.Unauthorized]: 'No_premission',
        [ERROR_CODES.PaymentRequired]: 'Phone_bing_maximum_2',
        [ERROR_CODES.NotAcceptable]: 'Phone_bing_maximum_2',
    },
    [GLOBAL_URL.PayOut]: {
        [ERROR_CODES.Forbidden]: 'Withdrawals_are_proh',
        [ERROR_CODES.Unauthorized]: 'No_premission',
        [ERROR_CODES.PaymentRequired]: 'Withdrawal_balance_i',
        [ERROR_CODES.NotAcceptable]: 'Withdrawal_times_are',
        [ERROR_CODES.RequestTimeout]: 'Withdrawal_Failed',
    },
    [GLOBAL_URL.GameSubscribe]: {
        [ERROR_CODES.BadRequest]: 'Parameter_error',
        [ERROR_CODES.LengthRequired]: 'Set_language_currency',
        [ERROR_CODES.Unauthorized]: 'Main_account_login_again',
        [ERROR_CODES.PaymentRequired]: 'Sub_account_creat_error_tryagin',
        [ERROR_CODES.Conflict]: 'Failed_enter_tryagain',
        [ERROR_CODES.Gone]: 'Failed_convert_currency_tryagain',
    },
    [GLOBAL_URL.SessionLogin]: {
        [ERROR_CODES.ServiceUnavailable]: 'System_maintenance',
        [ERROR_CODES.Continue]: 'Connection_in_use',
        [ERROR_CODES.NotFound]: 'Account_banned_contation_cs',
        [ERROR_CODES.Gone]: 'Token_expired_log_in',
    },
    [GLOBAL_URL.GiftCdk]: {
        [ERROR_CODES.BadRequest]: 'Redemption_code_no_exist',
        [ERROR_CODES.Unauthorized]: 'Not_paying_user_not_exchange',
        // [ERROR_CODES.PreconditionFailed]: 'Wrong_channel_not_exchange',
        [ERROR_CODES.ExpectationFailed]: 'Wrong_channel_not_exchange',
        [ERROR_CODES.Gone]: 'Wrong_upstream_not_exchange',
        [ERROR_CODES.NotAcceptable]: 'Not_within_event_time',
        [ERROR_CODES.ProxyAuthenticationRequired]: 'Redemption_code_unavailable',
        [ERROR_CODES.RequestTimeout]: 'Exceeded_the_daily',
        [ERROR_CODES.LengthRequired]: 'User_has_received',
        [ERROR_CODES.Accepted]: 'Receiving',
        [ERROR_CODES.Conflict]: 'Redemption_code_no_exist',
        [ERROR_CODES.PreconditionFailed]: 'Exceeded_the_maximum',
    },
    [GLOBAL_URL.BindFacebook]: {
        [ERROR_CODES.GatewayTimeout]: 'Verification_failed',
        [ERROR_CODES.Conflict]: 'User_error',
        [ERROR_CODES.LengthRequired]: 'User_has_bound',
        [ERROR_CODES.NotAcceptable]: 'Account_has_bound',
    },
    [GLOBAL_URL.BindGoogle]: {
        [ERROR_CODES.GatewayTimeout]: 'Verification_failed',
        [ERROR_CODES.Conflict]: 'User_error',
        [ERROR_CODES.LengthRequired]: 'User_has_bound',
        [ERROR_CODES.NotAcceptable]: 'Account_has_bound',
    },
    [GLOBAL_URL.BindEmail]: {
        [ERROR_CODES.Forbidden]: 'Email_format_wrong',
        [ERROR_CODES.NotAcceptable]: 'Email_has_used',
        [ERROR_CODES.BadRequest]: 'Verification_code_err',
        [ERROR_CODES.Conflict]: 'User_not_exist',
        [ERROR_CODES.LengthRequired]: 'User_has_bound',
    },
    [GLOBAL_URL.SendEmail]: {
        [ERROR_CODES.Forbidden]: 'Email_format_wrong',
        [ERROR_CODES.Gone]: 'Send_failed_try_again',
        [ERROR_CODES.NotAcceptable]: 'Validity_period_in_5mins',
    },
    [GLOBAL_URL.QueryBonuse]: {
        [ERROR_CODES.BadRequest]: 'Currency_needs_dispose',
        [ERROR_CODES.PaymentRequired]: 'Receiving',
    },
    [GLOBAL_URL.Beanexchange]: {
        [ERROR_CODES.NotAcceptable]: 'rebate_requirement_more5',
        [ERROR_CODES.PaymentRequired]: 'Click_multiple_times',
    },
}
