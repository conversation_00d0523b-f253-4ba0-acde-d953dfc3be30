export * from './api'
import { GLOBAL_URL } from './api'

export enum PageNameTypeInfo {
    Home = 'Home',
    Proto = 'Proto',
    Person = 'Person',
}
/*播放状态*/
export const SlideItemPlayStatus = {
    Play: 'Play',
    Stop: 'Stop',
    Pause: 'Pause',
}
// 滑动模式
export const SlideType = {
    HORIZONTAL: 0,
    VERTICAL: 1,
    VERTICAL_INFINITE: 2,
}

// 不需要动画的页面
//底部tab的按钮，跳转是不需要用动画的
export const NO_ANIMATION = ['/', '/video', '/jackpot', '/earnCash', '/wallets', '/cashout', '/events']

// 不需要登录的页面
export const NOT_LOGIN = ['/', '/login', '/iosInstruction', '/video', '/jackpot', '/earnCash', '/earn-rule', '/events']

// 图片背景默认路径

export const BASE_IMG_URL = '/src/assets/img'

// 客服链接
export const CustomerUrl = 'https://t.me/hanfei0100'

//手机号区号国家
export const COUNTRY_PHONE_CONFIG = [
    { code: '63', value: 'PH' },
    { code: '1', value: 'US' },
    { code: '90', value: 'TR' },
    { code: '55', value: 'BR' },
    { code: '234', value: 'NG' },
    { code: '62', value: 'ID' },
    { code: '91', value: 'IN' },
    { code: '852', value: 'HK' },
    { code: '966', value: 'SA' },
    { code: '65', value: 'SG' },
    { code: '49', value: 'DE' },
    { code: '965', value: 'KW' },
    { code: '36', value: 'HU' },
    { code: '64', value: 'NZ' },
    { code: '81', value: 'JP' },
    { code: '61', value: 'AU' },
    { code: '974', value: 'QA' },
    { code: '971', value: 'AE' },
    { code: '244', value: 'AO' },
    { code: '93', value: 'AF' },
    { code: '355', value: 'AL' },
    { code: '213', value: 'DZ' },
    { code: '376', value: 'AD' },
    { code: '1264', value: 'AI' },
    { code: '1268', value: 'AG' },
    { code: '54', value: 'AR' },
    { code: '374', value: 'AM' },
    { code: '247', value: 'AC' },
    { code: '43', value: 'AT' },
    { code: '994', value: 'AZ' },
    { code: '1242', value: 'BS' },
    { code: '973', value: 'BH' },
    { code: '880', value: 'BD' },
    { code: '1246', value: 'BB' },
    { code: '375', value: 'BY' },
    { code: '32', value: 'BE' },
    { code: '501', value: 'BZ' },
    { code: '229', value: 'BJ' },
    { code: '1441', value: 'BM' },
    { code: '591', value: 'BO' },
    { code: '267', value: 'BW' },
    { code: '673', value: 'BN' },
    { code: '359', value: 'BG' },
    { code: '226', value: 'BF' },
    { code: '95', value: 'MM' },
    { code: '257', value: 'BI' },
    { code: '237', value: 'CM' },
    { code: '1', value: 'CA' },
    { code: '1345', value: 'KY' },
    { code: '236', value: 'CF' },
    { code: '235', value: 'TD' },
    { code: '56', value: 'CL' },
    { code: '57', value: 'CO' },
    { code: '242', value: 'CG' },
    { code: '682', value: 'CK' },
    { code: '506', value: 'CR' },
    { code: '53', value: 'CU' },
    { code: '357', value: 'CY' },
    { code: '420', value: 'CZ' },
    { code: '45', value: 'DK' },
    { code: '253', value: 'DJ' },
    { code: '1890', value: 'DO' },
    { code: '593', value: 'EC' },
    { code: '20', value: 'EG' },
    { code: '503', value: 'SV' },
    { code: '372', value: 'EE' },
    { code: '251', value: 'ET' },
    { code: '679', value: 'FJ' },
    { code: '358', value: 'FI' },
    { code: '33', value: 'FR' },
    { code: '594', value: 'GF' },
    { code: '241', value: 'GA' },
    { code: '220', value: 'GM' },
    { code: '995', value: 'GE' },
    { code: '233', value: 'GH' },
    { code: '350', value: 'GI' },
    { code: '30', value: 'GR' },
    { code: '1809', value: 'GD' },
    { code: '1671', value: 'GU' },
    { code: '502', value: 'GT' },
    { code: '224', value: 'GN' },
    { code: '592', value: 'GY' },
    { code: '509', value: 'HT' },
    { code: '504', value: 'HN' },
    { code: '354', value: 'IS' },
    { code: '98', value: 'IR' },
    { code: '964', value: 'IQ' },
    { code: '353', value: 'IE' },
    { code: '972', value: 'IL' },
    { code: '39', value: 'IT' },
    { code: '1876', value: 'JM' },
    { code: '962', value: 'JO' },
    { code: '855', value: 'KH' },
    { code: '327', value: 'KZ' },
    { code: '254', value: 'KE' },
    { code: '82', value: 'KR' },
    { code: '331', value: 'KG' },
    { code: '856', value: 'LA' },
    { code: '371', value: 'LV' },
    { code: '961', value: 'LB' },
    { code: '266', value: 'LS' },
    { code: '231', value: 'LR' },
    { code: '218', value: 'LY' },
    { code: '423', value: 'LI' },
    { code: '370', value: 'LT' },
    { code: '352', value: 'LU' },
    { code: '853', value: 'MO' },
    { code: '261', value: 'MG' },
    { code: '265', value: 'MW' },
    { code: '60', value: 'MY' },
    { code: '960', value: 'MV' },
    { code: '223', value: 'ML' },
    { code: '356', value: 'MT' },
    { code: '596', value: 'MQ' },
    { code: '230', value: 'MU' },
    { code: '52', value: 'MX' },
    { code: '373', value: 'MD' },
    { code: '377', value: 'MC' },
    { code: '976', value: 'MN' },
    { code: '1664', value: 'MS' },
    { code: '212', value: 'MA' },
    { code: '258', value: 'MZ' },
    { code: '264', value: 'NA' },
    { code: '674', value: 'NR' },
    { code: '977', value: 'NP' },
    { code: '31', value: 'NL' },
    { code: '505', value: 'NI' },
    { code: '227', value: 'NE' },
    { code: '850', value: 'KP' },
    { code: '47', value: 'NO' },
    { code: '968', value: 'OM' },
    { code: '92', value: 'PK' },
    { code: '507', value: 'PA' },
    { code: '675', value: 'PG' },
    { code: '595', value: 'PY' },
    { code: '51', value: 'PE' },
    { code: '48', value: 'PL' },
    { code: '689', value: 'PF' },
    { code: '351', value: 'PT' },
    { code: '1787', value: 'PR' },
    { code: '262', value: 'RE' },
    { code: '40', value: 'RO' },
    { code: '7', value: 'RU' },
    { code: '1758', value: 'LC' },
    { code: '1784', value: 'VC' },
    { code: '684', value: 'AS' },
    { code: '685', value: 'WS' },
    { code: '378', value: 'SM' },
    { code: '239', value: 'ST' },
    { code: '221', value: 'SN' },
    { code: '248', value: 'SC' },
    { code: '232', value: 'SL' },
    { code: '421', value: 'SK' },
    { code: '386', value: 'SI' },
    { code: '677', value: 'SB' },
    { code: '252', value: 'SO' },
    { code: '27', value: 'ZA' },
    { code: '34', value: 'ES' },
    { code: '94', value: 'LK' },
    { code: '249', value: 'SD' },
    { code: '211', value: 'SS' },
    { code: '597', value: 'SR' },
    { code: '268', value: 'SZ' },
    { code: '46', value: 'SE' },
    { code: '41', value: 'CH' },
    { code: '963', value: 'SY' },
    { code: '886', value: 'TW' },
    { code: '992', value: 'TJ' },
    { code: '255', value: 'TZ' },
    { code: '66', value: 'TH' },
    { code: '228', value: 'TG' },
    { code: '676', value: 'TO' },
    { code: '1809', value: 'TT' },
    { code: '216', value: 'TN' },
    { code: '993', value: 'TM' },
    { code: '256', value: 'UG' },
    { code: '380', value: 'UA' },
    { code: '44', value: 'GB' },
    { code: '598', value: 'UY' },
    { code: '233', value: 'UZ' },
    { code: '58', value: 'VE' },
    { code: '84', value: 'VN' },
    { code: '967', value: 'YE' },
    { code: '263', value: 'ZW' },
    { code: '243', value: 'CD' },
    { code: '260', value: 'ZM' },
]

// 提现状态
export const GCASH_PAYStATE = {
    0: 'Unreviewed',
    1: 'Reviewed_order_is_withdrawal',
    3: 'Withdrawal_successful',
    2: 'Withdrawal_Failed',
    4: 'Review_failed',
    5: 'To_be_determined',
    6: 'Cancel',
}
// 系统消息前缀
export const SYSTEM = 10000
// 交易消息前缀
export const TRANSACtiON = 20000

export enum MESSAGE_CODE {
    SYSTEM = 10000, // 系统
    TRANSACTION = 20000, // 交易
}

// 未登录时长链接请求缓存黑名单
export const BLACK_LIST = [GLOBAL_URL.SessionLogin]

export const CUREENCY_SYMBOL = {
    PHP: 'P',
    USD: '$',
}
export const CACHE_HOST_LIST = ['cach', 'cach1', 'cach2', 'cach3', 'cach4', 'cach5']

// vip 权益图标
export const VIP_LEGAL_CONFIG = [
    {
        text: 'vip_reward1',
        key: 'reward.gold',
    },
    {
        text: 'vip_reward2',
        key: 'monthReward.gold',
    },
    {
        text: 'vip_reward3',
        key: 'betRebate',
        type: 'percent',
    },
    {
        text: 'vip_reward4',
        key: 'depositBonus',
        type: 'percent',
    },
    {
        text: 'vip_reward5',
        key: 'benefit',
    },
    {
        text: 'vip_reward6',
        key: 'withdrawAmount',
    },
    {
        text: 'vip_reward7',
        key: 'withdrawTimes',
    },
]
export const FORMATE_CURRENCY = ['IDR', 'NGN']

// 长链接
export const WS_HOST = {
    development: ['wss://tests0.betfuguapi.com'],
    production: ['wss://acc.betfuguapi.com'],
}

export const WS_SERVER = WS_HOST[import.meta.env.MODE]

export const CURRENCY_ENV = {
    development: [
        {
            currency: 'PHP',
            type: 1,
            picture: 'https://betfugu.com/image/currencyicon/d0fd682f-0ea0-41bc-b975-1f480fc447c1.png',
            clientcurrency: '₱',
            exchangeRate: '0.017',
            country: 'PH',
            language: 'tl-PH',
        },
        {
            currency: 'INR',
            type: 1,
            picture: 'https://betfugu.com/image/currencyicon/4d196228-22c4-4d37-8da9-339f2065bbf6.png',
            clientcurrency: '₹',
            exchangeRate: '0.01166',
            country: 'IN',
            language: 'en-US',
        },
        // {
        //     currency: 'USD',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/a786fd81-ab1b-43cf-9513-8b02af624a7a.png',
        //     clientcurrency: '$',
        //     exchangeRate: '1',
        // },
        // {
        //     currency: 'IDR',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/24ecbe85-b1d2-44a4-a34e-7a79be5e61d1.png',
        //     clientcurrency: 'Rp',
        //     exchangeRate: '0.00006168',
        // },

        // {
        //     currency: 'BRL',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/c6fc626a-b7c6-4c10-b7cb-dec78f1833ac.png',
        //     clientcurrency: 'R$',
        //     exchangeRate: '0.16',
        // },
        // {
        //     currency: 'NGN',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/116073ef-d983-4b0c-bd3f-f2cf4504c485.png',
        //     clientcurrency: '₦',
        //     exchangeRate: '0.00064',
        // },
        // {
        //     currency: 'TRY',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/3d9e634d-f7b7-46b4-b7c4-8130c41ce7fa.png',
        //     clientcurrency: '₺',
        //     exchangeRate: '0.028',
        // },
    ],
    production: [
        {
            currency: 'PHP',
            type: 1,
            picture: 'https://betfugu.com/image/currencyicon/d0fd682f-0ea0-41bc-b975-1f480fc447c1.png',
            clientcurrency: '₱',
            exchangeRate: '0.017',
            country: 'PH',
            language: 'tl-PH',
        },
        {
            currency: 'INR',
            type: 1,
            picture: 'https://betfugu.com/image/currencyicon/4d196228-22c4-4d37-8da9-339f2065bbf6.png',
            clientcurrency: '₹',
            exchangeRate: '0.01166',
            country: 'IN',
            language: 'en-US',
        },
        // {
        //     currency: 'USD',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/01c442fe-a292-4bbd-ac52-ba3abf7c32f2.png',
        //     clientcurrency: '$',
        //     exchangeRate: '1',
        // },
        // {
        //     currency: 'TRY',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/55fae9ef-75da-4eb3-9477-7edf224c1698.png',
        //     clientcurrency: '₺',
        //     exchangeRate: '0.028',
        // },
        // {
        //     currency: 'BRL',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/80496525-fc06-4f31-9289-b8465bf91087.png',
        //     clientcurrency: 'R$',
        //     exchangeRate: '0.16',
        // },
        // {
        //     currency: 'NGN',
        //     type: 1,
        //     picture: 'https://betfugu.com/image/currencyicon/b1d8b2a1-e455-4b27-80a3-ef0fea3c1cf0.png',
        //     clientcurrency: '₦',
        //     exchangeRate: '0.00064',
        // },
    ],
}
export const CURRENCY_LIST = CURRENCY_ENV[import.meta.env.MODE]

export const LANGUAGE_ENV = {
    development: [
        {
            language: 'en-US',
            show: 'English(US)',
        },
        {
            language: 'tl-PH',
            show: 'Filipino',
        },

        // {
        //     language: 'hi-IN',
        //     show: 'हिंदी',
        // },
        // {
        //     language: 'pt-BR',
        //     show: 'Português',
        // },
        // {
        //     language: 'id-ID',
        //     show: 'Indonesia',
        // },
        // {
        //     language: 'tr-TR',
        //     show: 'Türkçe',
        // },
    ],
    production: [
        {
            language: 'en-US',
            show: 'English',
        },
        {
            language: 'tl-PH',
            show: 'Filipino',
        },
        // {
        //     language: 'tr-TR',
        //     show: 'Türkçe',
        // },
        // {
        //     language: 'pt-BR',
        //     show: 'Português',
        // },
    ],
}
export const LANGUAGE_LIST = LANGUAGE_ENV[import.meta.env.MODE]
