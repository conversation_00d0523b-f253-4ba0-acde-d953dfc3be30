import { computed, reactive } from 'vue'
export const usePagation = () => {
    const list = reactive({
        loading: false,
        finished: false,
        data: [],
        pageNum: 1,
        pageSize: 10,
    })
    const start = computed(() => (list.pageNum - 1) * list.pageSize)
    const end = computed(() => start.value + list.pageSize - 1)

    const combileList = (list = [], obj = {}, key = '', valueKey = '') => {
        return (list = list.map((item) => {
            return { ...item, [valueKey]: obj[item[key]] }
        }))
    }
    return {
        start,
        end,
        list,
        combileList,
    }
}
