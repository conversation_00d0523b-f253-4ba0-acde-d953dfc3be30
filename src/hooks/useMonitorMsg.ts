import { useBaseStore } from '@/stores'
import bus, { EVENT_KEY } from '@/utils/bus'
// import { storeToRefs } from 'pinia'
// RECIEVE_MESSAGE
const MONITOR_EVENT = {
    UserItem: 'user.items', // 余额更新
    FriendMsg: 'frientds.message', // 好友消息
    payment: 'payment.finish', // 通知充值成功
    UserUpdate: 'user.update', // 用户消息更新
    // Broadcast: 'global.broadcast', // 跑马灯
}

// 处理余额更新
const handleUserItem = (msg, store) => {
    const { wallet } = store
    if (msg.currency === wallet.currency) {
        store.setWallet(msg.changed)
        bus.emit('updateVip')
    }
}
const handleFriendMsg = (msg, store) => {
    store.distributeMsg(msg)
    bus.emit(EVENT_KEY.RECIEVE_MESSAGE, msg)
}
const handleUserUpdate = (msg, store) => {
    store.setUserInfo({ ...store.userInfo, ...msg })
}
// const handleBroadcast = (msg, store) => {
//     store.updatLamp({
//         ...msg,
//         time: Date.now(),
//     })
// }
const handlePayment = (msg, store) => {
    bus.emit('paymentFinish')
    // 首充订单处理
    if (msg.id === store.depositOrder) {
        store.depositOrder = ''
        bus.emit(EVENT_KEY.PAYMENT, msg)
    }
}

const eventMap: {
    [key: string]: (msg: any, store: any) => void
} = {
    [MONITOR_EVENT.UserItem]: handleUserItem,
    [MONITOR_EVENT.FriendMsg]: handleFriendMsg,
    [MONITOR_EVENT.UserUpdate]: handleUserUpdate,
    // [MONITOR_EVENT.Broadcast]: handleBroadcast,
    [MONITOR_EVENT.payment]: handlePayment,
}

export const useMonitorMsg = () => {
    const store = useBaseStore()
    const monitor = () => {
        Object.values(MONITOR_EVENT).forEach((event) => {
            store.gameserver.on(event, (msg) => {
                eventMap[event](msg, store)
            })
        })
    }
    const unBindMonitor = () => {
        if (store.gameserver) {
            Object.values(MONITOR_EVENT).forEach((event) => {
                store.gameserver?.off(event)
            })
        }
    }

    return {
        monitor,
        unBindMonitor,
    }
}
