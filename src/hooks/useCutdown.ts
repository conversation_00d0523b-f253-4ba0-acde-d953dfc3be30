// 登录倒计时
export function useCutdown() {
    const codeOpt = reactive({
        timer: null,
        seconds: 60,
        isSend: false,
        times: 0,
    })
    const doCutDown = () => {
        codeOpt.timer = setInterval(() => {
            if (codeOpt.seconds <= 1) {
                if (codeOpt.timer) {
                    clearInterval(codeOpt.timer)
                    codeOpt.timer = null
                    codeOpt.seconds = 60
                    codeOpt.isSend = false
                }
                return
            }
            codeOpt.seconds = codeOpt.seconds - 1
        }, 1000)
    }

    return {
        codeOpt,
        doCutDown,
    }
}
