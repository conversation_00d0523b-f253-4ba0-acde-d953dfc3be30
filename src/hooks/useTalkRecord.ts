import { useBaseStore } from '@/stores'
import bus, { EVENT_KEY } from '@/utils/bus'

export const useTalkRecord = ({ chatId }: { chatId: string | number }) => {
    const store = useBaseStore()

    const el = ref(null)

    const channel = [chatId, store.userInfo.uid].sort().join(':')

    const list = computed(() => store.chatList[channel] || [])
    const state = reactive({
        loading: false,
        finish: false,
        empty: false,
        count: 0,
    })

    const pushHistory = (msgList = []) => {
        const init = list.value.length < 10
        msgList = msgList.filter((item) => {
            return !list.value.some((ms) => ms.id === item.id)
        })
        store.chatList[channel] = [...msgList, ...list.value]
        const h = getScrollHeight()
        nextTick(() => {
            posFixed(h, init)
        })
    }

    const getEl = () => {
        if (!el.value) {
            el.value = document.querySelector(`#chat-panel${chatId}`)
        }
        return el.value
    }
    const getScrollHeight = () => {
        return getEl()?.scrollHeight
    }
    const posFixed = (h, init = false) => {
        const element = getEl()
        if (init) {
            if (element) {
                element.scrollTop = element.scrollHeight

                setTimeout(() => {
                    element.scrollTop = element.scrollHeight + 1000
                }, 50)
            }
        } else {
            element.scrollTop = getScrollHeight() - h
        }
    }

    // 获取历史热门消息
    const gethotMessage = async () => {
        state.loading = true
        try {
            const res = await store.getHot20Msg(channel)
            if (res.code === 200) {
                const msgList = res.hotdatas[channel]
                if (!msgList.length) {
                    state.empty = true
                    state.finish = true
                    return
                }
                pushHistory(
                    msgList
                        .map((item, index) => {
                            return { ...JSON.parse(item), showTime: index === 0 }
                        })
                        .reverse()
                )
            }
        } finally {
            state.loading = false
        }
    }
    // 加载数据列表
    const load = async () => {
        if (state.finish) {
            return
        }
        state.loading = true
        try {
            const res = await store.getChatHis({ channel, id: list.value[0].id })
            if (res.code === 200) {
                const msgList = res.histories.hits.map((item) => ({ ...item._source.context })).reverse()
                pushHistory(msgList)
                if (msgList.length < 10) {
                    state.finish = true
                    return
                }
            }
        } finally {
            state.loading = false
        }
    }

    // 初始化查最近20条
    const onLoad = async () => {
        if (!list.value.length) {
            await gethotMessage()
        } else {
            if (list.value.length < 10) {
                await load()
            }
        }
    }
    // 消息跳转
    const onJumpMessage = (id, flag = true) => {
        nextTick(() => {
            const element = document.querySelector(`#chat${id}`)
            if (element) {
                element?.scrollIntoView(
                    flag
                        ? {
                              behavior: 'smooth',
                          }
                        : {}
                )
            }
        })
    }
    const onScrollBottom = (flag = true) => {
        const latest = list.value[list.value.length - 1]
        latest?.id && onJumpMessage(latest.id, flag)
        state.count = 0
    }

    // 滚动到顶部查历史
    const onRefreshLoad = () => {
        load()
    }
    // 收到消息
    const receiveMsg = (msg) => {
        state.count++
        if (msg.content.sender == store.userInfo.uid) {
            onScrollBottom()
        }
    }
    onBeforeMount(() => {
        bus.on(EVENT_KEY.RECIEVE_MESSAGE, receiveMsg)
    })
    onBeforeUnmount(() => {
        bus.off(EVENT_KEY.RECIEVE_MESSAGE, receiveMsg)
    })

    return {
        onLoad,
        onRefreshLoad,
        onJumpMessage,
        onScrollBottom,
        list,
        state,
    }
}
