import { useBaseStore } from '@/stores'

let scrollTimeout
export function useScroll() {
    const store = useBaseStore()
    const handleScroll = () => {
        if (store.showBuoy) {
            store.showBuoy = false
        }
        scrollTimeout && clearTimeout(scrollTimeout)

        scrollTimeout = setTimeout(function () {
            store.showBuoy = true
        }, 200)
    }
    return {
        handleScroll,
    }
}
