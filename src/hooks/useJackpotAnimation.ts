import { ref, onUnmounted } from 'vue'

export interface JackpotAnimationOptions {
  /** 动画持续时间（毫秒） */
  animationDuration?: number
  /** 每步持续时间（毫秒） */
  stepDuration?: number
  /** 动画完成后停顿时间（毫秒） */
  pauseDuration?: number
  /** 随机增长范围 */
  growthRange?: number
  /** 初始值是否从0开始 */
  startFromZero?: boolean
}

export function useJackpotAnimation(options: JackpotAnimationOptions = {}) {
  // 动画配置常量
  const ANIMATION_DURATION = options.animationDuration ?? 500
  const STEP_DURATION = options.stepDuration ?? 16
  const ANIMATION_STEPS = Math.floor(ANIMATION_DURATION / STEP_DURATION)
  const PAUSE_DURATION = options.pauseDuration ?? 3000
  const GROWTH_RANGE = options.growthRange ?? 100
  const START_FROM_ZERO = options.startFromZero ?? false

  // 动画相关的响应式数据
  const currentPoolValue = ref(0)
  const targetAmount = ref(0)
  const animationId = ref<number | null>(null)

  // 增长周期控制
  const isAnimating = ref(false)
  const isPaused = ref(false)

  // 跟踪所有定时器，用于清理
  const timeoutIds = ref<number[]>([])

  // 开始一轮增长动画
  const startGrowthCycle = () => {
    if (isAnimating.value || isPaused.value) return

    const randomGrowth = Math.random() * GROWTH_RANGE
    targetAmount.value += randomGrowth
    isAnimating.value = true

    animateToTarget()
  }

  // 动画到目标值
  const animateToTarget = () => {
    const start = currentPoolValue.value
    const target = targetAmount.value
    const difference = target - start

    const stepValue = difference / ANIMATION_STEPS

    let currentStep = 0

    const animate = () => {
      if (currentStep < ANIMATION_STEPS) {
        currentPoolValue.value = start + stepValue * currentStep
        currentStep++

        const timeoutId = setTimeout(() => {
          animationId.value = requestAnimationFrame(animate)
        }, STEP_DURATION)
        timeoutIds.value.push(timeoutId)
      } else {
        currentPoolValue.value = target
        animationId.value = null
        onAnimationComplete()
      }
    }

    animate()
  }

  // 动画完成后的处理
  const onAnimationComplete = () => {
    isAnimating.value = false
    isPaused.value = true

    // 停顿指定时间后开始下一轮
    const timeoutId = setTimeout(() => {
      isPaused.value = false
      startGrowthCycle()
    }, PAUSE_DURATION)
    timeoutIds.value.push(timeoutId)
  }

  // 停止所有动画和定时器
  const stopGrowth = () => {
    if (animationId.value) {
      cancelAnimationFrame(animationId.value)
      animationId.value = null
    }

    // 清理所有定时器
    timeoutIds.value.forEach((id) => {
      clearTimeout(id)
    })
    timeoutIds.value = []
  }

  // 启动增长动画
  const startGrowth = () => {
    // 确保有目标值且当前未在动画中
    if (targetAmount.value > 0 && !isAnimating.value && !isPaused.value) {
      // 根据配置决定是否从0开始
      if (START_FROM_ZERO && currentPoolValue.value === 0) {
        isAnimating.value = true
        animateToTarget()
      } else if (!START_FROM_ZERO && currentPoolValue.value > 0) {
        // 首页模式：延迟后开始增长循环
        const timeoutId = setTimeout(() => {
          startGrowthCycle()
        }, 1000)
        timeoutIds.value.push(timeoutId)
      }
    }
  }

  // 重置动画状态
  const resetAnimation = (newValue: number = 0) => {
    stopGrowth()
    currentPoolValue.value = START_FROM_ZERO ? 0 : newValue
    targetAmount.value = newValue
    isAnimating.value = false
    isPaused.value = false
  }

  // 设置当前值（不触发动画）
  const setCurrentValue = (value: number) => {
    currentPoolValue.value = value
    targetAmount.value = value
    // 重置动画状态，确保后续可以启动
    isAnimating.value = false
    isPaused.value = false
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopGrowth()
    currentPoolValue.value = 0
    targetAmount.value = 0
    isAnimating.value = false
    isPaused.value = false
  })

  return {
    // 状态
    currentPoolValue,
    targetAmount,
    isAnimating,
    isPaused,

    // 方法
    startGrowth,
    stopGrowth,
    resetAnimation,
    setCurrentValue,
    startGrowthCycle,

    // 配置
    animationConfig: {
      ANIMATION_DURATION,
      STEP_DURATION,
      PAUSE_DURATION,
      GROWTH_RANGE,
      START_FROM_ZERO
    }
  }
}