import useClipboard from 'vue-clipboard3'
import { useBaseStore } from '@/stores'
import { getDeviceInfo } from '@/utils'

export const useClipboard1 = () => {
    const store = useBaseStore()
    const { toClipboard } = useClipboard()

    const cliboarded = async (text) => {
        if (store.isTg || (getDeviceInfo().os === 'iOS' && navigator.clipboard)) {
            await navigator.clipboard.writeText(text)
        } else {
            await toClipboard(text)
        }
    }

    return {
        toClipboard: cliboarded,
    }
}
