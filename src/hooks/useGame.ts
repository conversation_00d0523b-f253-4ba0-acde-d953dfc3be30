import { useBaseStore } from '@/stores'
import { ERROR_CODES } from '@/enums'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

// 订阅游戏
export const useGame = () => {
    const router = useRouter()
    const i18n = useI18n()
    const store = useBaseStore()

    const gameSubscibe = async (gameId, srctype = 'game') => {
        let url = ''
        store.prevGameTag = gameId
        const res = await store.gameSubscribe({ gameid: gameId, srctype })
        if (res.code === ERROR_CODES.MethodNotAllowed) {
            await showDialog({
                message: i18n.t('Ingame_re_enter'),
                className: 'common-dialog',
                showCancelButton: true,
                confirmButtonText: 'confirm',
                cancelButtonText: 'cancel',
            })
                .then(async () => {
                    try {
                        const res = await store.Rejoingames()
                        if (res.code === ERROR_CODES.OK) {
                            url = res.metadata?.gameurl
                        } else {
                            showToast(i18n.t('Failed_re_enter_tryagain'))
                        }
                    } catch {
                        showToast(i18n.t('Failed_re_enter_tryagain'))
                    }
                })
                .catch((e) => {
                    console.log(e)
                })
        } else if (res.code === ERROR_CODES.PreconditionFailed) {
            showToast(i18n.t('Pop_up_prompt_text'))
        } else {
            url = res.metadata?.gameurl
        }
        return url
    }
    const goGamePage = async (item) => {
        let url = ''
        if (item.activity) {
            url = item.activitylink
        } else {
            if (!store.token) {
                return router.push('/login')
            }
            url = await gameSubscibe(item.gametag)
        }
        if (url) {
            router.push({
                path: '/iframe',
                query: {
                    u: encodeURIComponent(url),
                    from: 'home',
                },
            })
        } else {
            showToast(i18n.t('Failed_enter_tryagain'))
        }
    }
    return {
        gameSubscibe,
        goGamePage,
    }
}
