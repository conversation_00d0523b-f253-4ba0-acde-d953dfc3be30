<template>
    <div class="bar">
        <div class="bar-inner" :style="barWStyle"></div>
        <span class="bar-num">{{ num ? num.toFixed(2) : num }}/{{ target ? target.toFixed(2) : target }}</span>
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    num: {
        type: Number,
        default: () => 0,
    },
    target: {
        type: Number,
        default: () => 0,
    },
})
const barWStyle = computed(() => {
    const scale = props.num / props.target
    return {
        width: scale >= 1 ? '100%' : scale * 100 + '%',
    }
})
</script>
<style lang="scss" scoped>
.bar {
    position: relative;
    height: 22px;
    background-image: linear-gradient(90deg, #4b0e9f 0%, #3812a5 50%, #2415ab 100%), linear-gradient(#4b1994, #4b1994);
    background-blend-mode: normal, normal;
    border-radius: 16px;
    font-family: MicrosoftYaHei;
    overflow: hidden;

    .bar-inner {
        position: absolute;
        width: 0%;
        height: 100%;
        background: #f9cd49;
    }
    .bar-num {
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 16px;
        transform: translate3d(-50%, -45%, 0);
    }
}
</style>
