<template>
    <van-popup class="channel-pop" v-model:show="show" position="bottom" close-on-popstate round :z-index="3001" @closed="handleClose">
        <div class="wallet-bonus">
            <div class="title">
                <!-- <div class="help_btn" @click="handleHelp"></div> -->
                <span>{{ $t('Event Rules') }}</span>
                <div class="close" @click="handleClose"></div>
            </div>
            <div class="rule-pop">
                <div class="rule-content">
                    <div class="rule">
                        <div class="rule-title">Deposit Frenzy Event</div>
                        <div class="part">
                            This is your chance to turn your deposit into an opportunity to win unlimited free cash. The more you deposit, the higher
                            the rewards —— and every milestone gets your one exclusive chance to spin your wheel of fortune!
                        </div>
                        <div class="rule-title">How It Works</div>
                        <div class="part point">Cumulative Deposits: Every deposit you make counts toward reaching a deposit milestone.</div>
                        <div class="part point">Earn a Spin: Once your total deposit reaches a milestone, you earn one chance to spin.</div>
                        <div class="part point">
                            Unlimited Free Cash: Spin the wheel and win unlimited free cash, which takes 10x turnover requirement!
                        </div>
                        <div class="rule-title">Tiers &amp; Rewards</div>
                        <div class="rule-table">
                            <div class="rule-bar">
                                <div class="bar-left">Total Deposit</div>
                                <div class="bar-right">Rewards</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱500</div>
                                <div class="bar-right">Lucky Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱1500</div>
                                <div class="bar-right">Lucky Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱3000</div>
                                <div class="bar-right">Super Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱6000</div>
                                <div class="bar-right">Super Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱10000</div>
                                <div class="bar-right">Mega Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left">₱20000</div>
                                <div class="bar-right">Mega Spin</div>
                            </div>
                            <div class="rule-bar">
                                <div class="bar-left"><span>Every </span>₱10000 <span>*</span></div>
                                <div class="bar-right">Power Spin</div>
                            </div>
                        </div>
                        <div class="part">* Complete all tiers to unlock <span> Power Spin </span> and start your lucky streak!</div>
                        <div class="part">
                            betfugu.com reserves the right to change any rules and conditions at its sole discretion and without prior notice.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'

const show = defineModel()
const router = useRouter()
const store = useBaseStore()
const { userInfo } = storeToRefs(store)

const handleClose = () => {
    show.value = false
}

const goPage = (type = 1) => {
    router.push({
        path: type === 1 ? '/wallets' : '/cashout',
        query: {
            back: 'true',
        },
    })
    handleClose()
}

watch(show, () => {
    console.log('show.value1', show.value)
    // if (show.value))
})

onMounted(() => {})
</script>
<style lang="scss" scoped>
.wallet-bonus {
    width: 750px;
    max-height: 1300px;
    padding: 0 22px 58px;
    background-color: #232626;
    font-family: MicrosoftYaHei;
    color: #fff;

    .title {
        position: relative;
        @apply flex justify-center items-center;
        padding-top: 33px;
        padding-bottom: 44px;
        position: relative;
        font-size: 26px;
        font-weight: normal;
        align-items: center;

        .help_btn {
            position: absolute;
            left: 15px;
            width: 35px;
            height: 35px;
            background: url('@/assets/img/wallets/qestion_icon.png') no-repeat center center;
            background-size: 35px 35px;
            opacity: 0.7;
        }
        // &::before {
        //     display: inline-block;
        //     content: '';
        //     margin-right: 20px;

        //     width: 52px;
        //     height: 52px;
        //     background: url('@/assets/img/new-home/wallet-icon.png') no-repeat;
        //     background-size: 100% 100%;
        //     position: relative;
        //     top: 10px;
        // }
        .close {
            position: absolute;
            right: 0px;
            width: 30px;
            height: 30px;
            background: url('@/assets/img/close.png') no-repeat center center;
            background-size: 30px 30px;
            align-items: center;
        }
    }
}
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 660px;
    height: 840px;
    // padding: 0 28px 60px;
    border-radius: 30px;
    background-color: #2a2d3d;
    font-family: MicrosoftYaHei;
    color: #ffffff;

    transform: translate3d(-50%, -60%, 0);

    .title {
        position: relative;
        margin-bottom: 29px;
        line-height: 99px;
        font-size: 40px;
        text-align: center;

        &::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            width: 620px;
            height: 4px;
            background-color: #ffffff;
            border-radius: 2px;
            opacity: 0.2;

            transform: translateX(-50%);
        }
        .close {
            position: absolute;
            top: 30px;
            right: 20px;
            width: 33px;
            height: 34px;
            background: url('@/assets/img/new-home/close.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .content {
        flex: 1;
        overflow-y: scroll;
        font-size: 28px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 60px;
        letter-spacing: -1px;

        :deep(span) {
            color: #24eb88;
        }
    }
    .confirm {
        margin: 20px auto 0;
        width: 440px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        text-align: center;
        font-size: 36px;
        color: #ffffff;
    }
}

.rule-pop {
    color: #fff;
    font-size: 24px;
    font-weight: 1000;
}
.rule-pop .rule-content {
    height: 1160px;
}
.rule-pop .rule-content .rule {
    padding: 0 20px;
    height: 100%;
    overflow-y: auto;
}
.rule-pop .rule-content .rule .rule-title {
    margin-bottom: 20px;
    font-size: 30px;
    font-weight: 700;
}
.rule-pop .rule-content .rule .part {
    margin-bottom: 30px;
    line-height: 40px;
}
.rule-pop .rule-content .rule .part.point {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.rule-pop .rule-content .rule .part.point :before {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    position: relative;
    display: inline-block;
    content: '';
    margin-right: 10px;
    width: 10px;
    height: 10px;
    top: 12px;
    border-radius: 50%;
    background: #fff;
}
.rule-pop .rule-content .rule .part span {
    color: #00ff62;
}
.rule-pop .rule-content .rule .rule-table {
    margin-bottom: 0.44rem;
    border-radius: 0.26667rem;
    border: 0.05333rem solid #3a3f51;
    background: #3a3f51;
    overflow: hidden;
}
.rule-pop .rule-content .rule .rule-table .rule-bar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.rule-pop .rule-content .rule .rule-table .rule-bar :nth-child(odd) {
    background: #2a2e3f;
}
.rule-pop .rule-content .rule .rule-table .rule-bar .bar-left,
.rule-pop .rule-content .rule .rule-table .rule-bar .bar-right {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 70px;
    line-height: 70px;
    text-align: center;
}
</style>
