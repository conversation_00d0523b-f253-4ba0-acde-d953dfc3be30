<template>
    <div class="lottery-container">
        <div class="wheel-container" :class="{ 'scale-in': showWheel }">
            <div class="breathing-box" :style="{ backgroundImage: `url(${middleimg})` }"></div>
            <!-- 转盘 -->
            <div class="wheel" :style="wheelStyle" :class="{ rotating: isRotating, active: spininfo?.sindex === 3 }">
                <div class="wheel-light"></div>
                <div class="prize-item" v-for="(prize, index) in prizes" :key="index" :style="getPrizeStyle(index)">
                    <div class="prize-content">
                        <img
                            v-if="prize.id"
                            :src="getImg(prize.id)"
                            :alt="prize.id"
                            class="prize-image"
                            :class="{ 'prize-image-spin': prize.id === 'spin' }"
                        />
                        <div class="prize-name" :class="{ 'prize-name-spin': prize.id === 'spin' }">
                            {{ getPrizeName(prize) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 固定在右侧的指针 -->
            <div class="pointer-right" :class="{ active1: spininfo?.sindex === 3 }"></div>
        </div>

        <div class="spin-tip" :style="{ backgroundImage: `url(${currentImage})` }">
            <div class="spin-tip-text">
                <span class="small-text">{{ spininfo?.title }}</span>
                <span class="medium-text">{{ curSymbol[wallet?.currency] }}</span>
                <span class="large-text">{{ spininfo.maxwin }}</span>
            </div>
        </div>

        <!-- <div class="result" v-if="result" :class="{ 'fade-in': result }">
            恭喜获2得: <strong>{{ result }}</strong>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import eventBus from '@/utils/bus'

const props = defineProps({
    prizes: {
        type: Array as PropType<{ id: string; num: number }[]>,
        default: () => [],
    },
    maxwin: {
        type: Number,
        default: 0,
    },
    startspinindex: {
        type: Number,
        default: 0,
    },
})

const file = import.meta.glob('../../assets/img/luckyspin/*', { eager: true })
const store = useBaseStore()
const { curSymbol, wallet } = storeToRefs(store)
const isRotating = ref(false)
const currentRotation = ref(0)
const showTitle = ref(false)
const showWheel = ref(false)
const showControls = ref(false)

const spininfo = inject<{ sindex: number; title: string; maxwin: number }>('spininfo')

const middleimg = computed(() => {
    return !spininfo?.sindex ? 'images/luckyspin/middle1.png' : `images/luckyspin/middle${spininfo?.sindex + 1}.png`
})

const currentImage = computed(() => {
    return !spininfo?.sindex ? 'images/luckyspin/cd1.png' : `images/luckyspin/cd${spininfo?.sindex + 1}.png`
})

const wheelStyle = computed(() => {
    return {
        backgroundimage: `url(${middleimg.value})`,
        transform: `rotate(${currentRotation.value}deg)`,
        transition: isRotating.value ? 'transform 5s cubic-bezier(0.5, 0.2, 0, 1)' : 'none',
    }
})

onMounted(() => {
    // 组件加载后依次触发动画
    setTimeout(() => {
        showTitle.value = true
    }, 100)
    setTimeout(() => {
        showWheel.value = true
    }, 300)
    setTimeout(() => {
        showControls.value = true
    }, 600)
})

watch(
    () => spininfo?.sindex,
    () => {
        showTitle.value = false
        showWheel.value = false
        showControls.value = false
        setTimeout(() => {
            showWheel.value = true
        }, 10)
    }
)

watch(
    () => props.startspinindex,
    () => {
        if (props.startspinindex === -1) return
        startLottery(props.startspinindex)
    }
)

const getImg = (id: string) => {
    const name = id === 'spin' ? 'spin' + spininfo?.sindex : 'cury_com'
    const mod = file['../../assets/img/luckyspin/' + name + '.png'] as { default: string } | undefined
    return mod?.default
}

const getPrizeName = (prize) => {
    return prize.id === 'spin' ? `Spin*${prize.num}` : prize.num
}

const getPrizeStyle = (index) => {
    const angle = 360 / props.prizes.length
    return {
        transform: `rotate(${angle * index - angle / 2}deg)`,
        '--item-angle': `${angle}deg`,
    }
}

const startLottery = (prizeIndex: number) => {
    if (isRotating.value) return

    isRotating.value = true

    // 计算需要旋转的角度
    const anglePerItem = 360 / props.prizes.length
    const targetPosition = 180 // 指针在右侧(90度)，所以奖品需要转到270度位置
    const targetAngle = 360 * 5 + (targetPosition - anglePerItem * prizeIndex)

    // 当前角度取模360，确保从正确位置开始旋转
    const normalizedCurrent = currentRotation.value % 360
    currentRotation.value += targetAngle + (360 - normalizedCurrent)

    setTimeout(() => {
        isRotating.value = false
        // 保持最终角度在360度以内
        currentRotation.value = currentRotation.value % 360

        // 通知父组件旋转结束
        eventBus.emit('deposit-spin-end')
    }, 5000)
}
</script>

<style lang="scss" scoped>
.lottery-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 0 auto;

    .spin-tip {
        position: absolute;
        width: 584px;
        height: 608px;
        margin-left: -35px;
        margin-top: 120px;
        background-size: contain;

        .spin-tip-text {
            position: absolute;
            display: flex;
            width: 100%;
            bottom: 0;
            line-height: 106px;
            font-size: 34px;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: bolder;
            .small-text {
                padding: 10px;
            }
            .medium-text {
                color: #ffee6e;
                margin-top: 10px;
                font-size: 46px;
                text-shadow: 0 0.05333rem 0.05333rem rgba(69, 6, 6, 0.5);
            }
            .large-text {
                color: #ffee6e;
                font-size: 66px;
                text-shadow: 0 0.05333rem 0.05333rem rgba(69, 6, 6, 0.5);
            }
        }
    }
}

h2 {
    color: #e74c3c;
    margin-bottom: 20px;
    transform: scale(0);
    transition: transform 0.5s ease-out;
}

h2.scale-in {
    transform: scale(1);
}

.wheel-container {
    position: relative;
    width: 620px;
    height: 620px;
    margin: 20px auto;
    margin-left: -40px;
    // border-radius: 50%;
    // border: 3px solid #e74c3c;
    transform: scale(0.5) rotate(-30deg);
    // transition: transform 0s ease-in-out;
}

.wheel-container.scale-in {
    transform: scale(1) rotate(0deg);
    transition: transform 0.1s ease-out;
}

.wheel {
    position: relative;
    width: 100%;
    height: 100%;

    // border-radius: 60%;
    overflow: hidden;
    // box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    background-image: url('@/assets/img/luckyspin/lucky_bg1.png');
    background-size: 750px;
    background-position: center;
    background-repeat: no-repeat;
    &.active {
        background-image: url('@/assets/img/luckyspin/lucky_bg2.png');
    }
    .wheel-light {
        position: absolute;
        top: -9px;
        left: -9px;
        width: 103.5%;
        height: 103.5%;
        background-image: url('@/assets/img/luckyspin/spin_light.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        animation: jumpRotate 3s steps(2, jump-none) infinite;
    }
}

.prize-item {
    position: absolute;
    width: 50%;
    height: 50%;
    left: 0;
    top: 0;
    transform-origin: right bottom;
    display: flex;
    align-items: flex-end;
    /* justify-content: space-between; */
    // color: #fff;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-size: 36px;
}

.prize-content {
    display: flex;
    height: 10%;
    height: auto;
    text-align: center;

    :deep(img) {
        margin-bottom: 32px;
        margin-left: 30px;
    }
}
.prize-name {
    display: flex;
    width: 100px;
    height: 8px;
    transform: rotate(192deg);
    text-align: center;
    margin-top: 50px;
    margin-left: 0px;
    font-size: 30px;
    font-weight: 550;
}

.prize-name-spin {
    margin-top: 60px;
}

.prize-image {
    width: 40px;
    height: 40px;
}

.prize-image-spin {
    width: 50px;
    height: 50px;
}

/* 固定在右侧的指针 */
.pointer-right {
    position: absolute;
    right: -52px;
    top: 232px;
    width: 288px;
    height: 160px;
    background-image: url('@/assets/img/luckyspin/pointer1.png');
    background-size: cover;
    z-index: 10;
    &.active1 {
        right: -62px;
        width: 300px;
        height: 160px;
        background-image: url('@/assets/img/luckyspin/pointer2.png');
    }
}

.result {
    margin: 20px 0;
    font-size: 18px;
    color: #e74c3c;
    height: 30px;
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.result.fade-in {
    opacity: 1;
}

.controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.controls.fade-in {
    opacity: 1;
}

.controls button {
    font-size: 16px;
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.controls button:hover {
    background-color: #2980b9;
}

.controls button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

.breathing-box {
    position: absolute;
    display: flex;
    width: 166px;
    height: 90px;
    background-size: 100% 100%;
    color: white;
    align-items: center;
    justify-content: center;
    margin: 275px 230px;
    z-index: 20;
    animation: breathe-and-tilt 2.5s ease-in-out infinite;
}

@keyframes breathe-and-tilt {
    0%,
    100% {
        transform: scale(1) rotate(-2deg);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.12) rotate(2deg);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
}

@keyframes jumpRotate {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(22.5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}
</style>
