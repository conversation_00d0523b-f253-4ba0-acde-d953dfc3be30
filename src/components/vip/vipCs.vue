<template>
    <van-overlay v-model:show="visible" @click="handleClose">
        <div class="level-content" @click.stop>
            <div class="level-num">{{ store.vipInfo?.curLevel }}</div>
            <div :class="`vip-icon vip-${store.vipInfo?.curLevel}`"></div>
            <div class="cs-btn" @click="goCs"></div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'

const store = useBaseStore()
const visible = defineModel()

const handleClose = async () => {
    await store.triggerVip({ type: 'Advanced' })
    visible.value = false
}
const goCs = async () => {
    window.open(store.vipInfo.serviceUrl)
    handleClose()
}
</script>
<style scoped lang="scss">
@use './common.scss' as *;

.level-content {
    width: 710px;
    height: 1116px;
    background-image: url('@/assets/img/vip/kefu_bg.png');

    .level-num {
        display: inline-block;
        width: 400px;
        padding: 122px 0 0 320px;
        font-size: 88px;
        text-align: left;
    }

    .cs-btn {
        position: absolute;
        left: 50%;
        bottom: 134px;
        width: 618px;
        height: 99px;

        transform: translate3d(-50%, 0, 0);
    }
    .vip-icon {
        position: relative;
        top: 8%;
        left: 35%;
        width: 175px;
        height: 154px;

        background-repeat: no-repeat;
        background-size: 100% 100%;

        transform: scale(2.5);
        @for $i from 0 through 12 {
            &.vip-#{$i} {
                background-image: url('@/assets/img/vip/badge/#{$i}.png');
            }
        }
    }
}
</style>
