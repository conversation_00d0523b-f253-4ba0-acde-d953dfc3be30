<template>
    <van-overlay v-model:show="visible">
        <div class="level-content">
            <div class="close" @click="handleClose"></div>
            <div class="level-title">{{ $t('popup_downgrade_title1', { level: vipInfo.curLevel }) }}</div>
            <div class="down-line"></div>
            <div class="level-info">
                <p
                    class="info-title"
                    v-html="
                        $t('popup_downgrade_title2', {
                            value: vipInfo.recoverMax,
                        })
                    "
                ></p>
                <div class="vip-level">
                    <img
                        v-for="(num, index) in `${vipInfo.maxLevel}`.split('')"
                        :key="index"
                        :src="`//betfugu.com/static/img/playtok/vip/number/number_${num}.png`"
                    />
                </div>
            </div>
            <div class="down-wrap">
                <div class="legal-wrap">
                    <template v-for="(item, index) in VIP_LEGAL_CONFIG" :key="index">
                        <div v-if="getKeyValue(config, item.key)" :class="['legal-item', `legal-item${index}`]">
                            <div class="legal-cion">
                                {{
                                    item.key !== 'benefit'
                                        ? ['betRebate', 'depositBonus'].includes(item.key)
                                            ? formatPercent(getKeyValue(config, item.key) || 0, 2)
                                            : getKeyValue(config, item.key)
                                        : ''
                                }}
                            </div>
                            <p class="legal-name">{{ $t(item.text) }}</p>
                        </div>
                    </template>
                </div>
            </div>
            <div class="down-btn" @click="goWallet">{{ $t('popup_downgrade_button') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { VIP_LEGAL_CONFIG } from '@/enums'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { getKeyValue } from '@/utils/index'
import { formatPercent } from '@/utils/toolsValidate'

const visible = defineModel()

const router = useRouter()
const store = useBaseStore()
const { vipInfo, vipConfig } = storeToRefs(store)

const trigger = () => {
    return store.triggerVip({ type: 'Downgrade' })
}
const handleClose = async () => {
    await trigger()
    visible.value = false
}
const goWallet = async () => {
    await trigger()
    visible.value = false
    router.push('/wallets')
}
const config = computed(() => {
    return vipConfig.value.find((item) => item.level === vipInfo.value.maxLevel)
})
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.level-content {
    width: 711px;
    height: 847px;
    padding: 0;
    background-image: url('@/assets/img/vip/down_bg.png');
    .close {
        position: absolute;
        right: 22px;
        top: 18px;
        width: 31px;
        height: 31px;
        background: url('@/assets/img/vip/down_close.png') no-repeat;
        background-size: 100% 100%;
    }
    .level-title {
        padding-top: 40px;
        font-weight: bold;
        color: #618ee4;
        font-size: 32px;
    }
    .down-line {
        height: 18px;
        background: url('@/assets/img/vip/down_line.png') no-repeat;
        background-size: 100% 100%;
    }
    .level-info {
        padding: 0 20px;
        .info-title {
            // margin-bottom: 123px;
            font-size: 32px;
            font-weight: bold;
            text-align: center;
            :deep(.special) {
                font-size: 54px;
                color: #ffd144;
            }
        }
        .vip-level {
            @apply flex items-end;
            margin-top: 110px;
            padding-left: 196px;

            &::before {
                position: relative;
                top: -6px;
                content: '';
                display: inline-block;
                width: 208px;
                height: 189px;
                background: url('@/assets/img/vip/down_vip.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .down-wrap {
        margin: 30px 0 33px;
        height: 177px;
        .legal-wrap {
            @apply flex items-center;
            overflow-x: auto;
            height: 100%;
            // padding: 0 16px 0 20px;
            background: url('@/assets/img/vip/d_line.png') no-repeat;
            background-size: 100% 100%;

            .legal-item {
                @apply flex-1 flex flex-col items-center;
                margin-right: 20px;
                .legal-cion {
                    @apply flex flex-col justify-start items-center;
                    width: 117px;
                    height: 98px;
                    padding-top: 10px;

                    background: url('@/assets/img/vip/down_legal.png') no-repeat;
                    background-size: 100% 100%;

                    font-size: 24px;
                    line-height: 25px;

                    &::before {
                        align-self: top;
                        content: '';
                        display: inline-block;
                        margin-bottom: 10px;
                        width: 60px;
                        height: 49px;
                        background-size: 100%;
                        background-repeat: no-repeat;
                    }
                }
                .legal-name {
                    margin-top: px;
                    font-size: 18px;
                    text-align: center;
                }
            }
            @for $i from 0 through 6 {
                .legal-item#{$i} {
                    .legal-cion {
                        &::before {
                            background-image: url(@/assets/img/vip/legal/icon_#{$i}.png);
                        }
                    }
                }
            }
        }
    }
    .down-btn {
        margin: 0 auto;
        width: 424px;
        height: 91px;
        line-height: 91px;
        background: url('@/assets/img/vip/down_btn.png') no-repeat;
        background-size: 100% 100%;
        font-size: 41px;
        font-weight: bold;
        text-align: center;
    }
}
</style>
