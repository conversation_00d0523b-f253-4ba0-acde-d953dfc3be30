<template>
    <div class="main">
        <div class="vip-benefits" :class="`vip-card-bg${getVipBadge(level)}`">
            <div class="header">
                <!-- <button class="selectbtn left" :disabled="level === 0" @click="--level" :class="{ disabled: level === 0 }">‹</button>
                <div class="badge">
                    <img :src="getBadgeUrl(level)" />
                    <span class="vip-text">VIP {{ level }}</span>
                    <span class="level">0</span>
                </div>
                <button class="selectbtn right" :disabled="level === maxLevel" @click="++level" :class="{ disabled: level === maxLevel }">›</button> -->
                <div class="badge-container">
                    <button class="selectbtn left" :disabled="level === 0" @click="prevBadge" :class="{ disabled: level === 0 }">‹</button>

                    <div class="badge-wrapper">
                        <transition :name="transitionDirection">
                            <div class="badge" :key="level">
                                <img :src="getBadgeUrl(level)" />
                                <div class="badge-text">
                                    <span class="vip-text">VIP {{ level }}</span>
                                    <span class="level" :class="{ 'text-green': level <= vipInfo.curLevel }"> {{ vipconfigs[level].exp }} XP</span>
                                </div>
                            </div>
                        </transition>
                    </div>

                    <button class="selectbtn right" :disabled="level === maxLevel" @click="nextBadge" :class="{ disabled: level === maxLevel }">
                        ›
                    </button>
                </div>
            </div>

            <div class="benefit-card" v-for="(item, index) in items[0]" :key="index" :class="{ disabled: false }">
                <div class="info">
                    <div class="title">{{ $t(item.title) }}</div>
                    <div
                        class="desc"
                        v-html="
                            $t(item.desc, {
                                value:
                                    item.key == 'betRebate'
                                        ? formatBetRebate(getKeyValue(vipconfigs[level], item.key) || 0)
                                        : getKeyValue(vipconfigs[level], item.key) || 0,
                            })
                        "
                    ></div>
                </div>
                <div class="icon"><img :src="item.icon" /></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { getKeyValue } from '@/utils/index'

const file = import.meta.glob('../../assets/img/vip/*', { eager: true })

const level = ref(0)
const maxLevel = 12
const transitionDirection = ref('slide-right')

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})
const store = useBaseStore()
const { vipConfig, vipInfo } = storeToRefs(store)
const vipconfigs = computed(() => {
    console.log(vipConfig.value, 'vipConfig.value')
    return vipConfig.value.slice(props.type)
})

const formatBetRebate = (betRebate: number) => {
    return betRebate * 1000 + '‰'
}
const prevBadge = () => {
    if (level.value > 0) {
        transitionDirection.value = 'slide-right'
        level.value--
    }
}
const nextBadge = () => {
    if (level.value < maxLevel) {
        transitionDirection.value = 'slide-left'
        level.value++
    }
}

const items = {
    0: [
        {
            key: 'reward.gold',
            icon: new URL('@/assets/img/vip/reward_levelup.png', import.meta.url).href,
            title: 'vip_config_levelup_title',
            desc: 'vip_config_levelup_desc',
        },
        {
            key: 'betRebate',
            icon: new URL('@/assets/img/vip/reward_bonus.png', import.meta.url).href,
            title: 'vip_config_daily_title',
            desc: 'vip_config_daily_desc',
        },
        {
            key: 'weekRewardWorth',
            icon: new URL('@/assets/img/vip/reward_weekly.png', import.meta.url).href,
            title: 'vip_config_weekly_title',
            desc: 'vip_config_weekly_desc',
        },
        {
            key: 'monthReward.gold',
            icon: new URL('@/assets/img/vip/reward_monthly.png', import.meta.url).href,
            title: 'vip_config_monthly_title',
            desc: 'vip_config_monthly_desc',
        },
        {
            key: 'withdrawNoFeeTimes',
            icon: new URL('@/assets/img/vip/nofee.png', import.meta.url).href,
            title: 'vip_config_nofee_title',
            desc: 'vip_config_nofee_desc',
        },
        {
            key: 'withdrawAmount',
            icon: new URL('@/assets/img/vip/dailywithdrawlimit.png', import.meta.url).href,
            title: 'vip_config_withdrwalimit_title',
            desc: 'vip_config_withdrwalimit_desc',
        },
    ],
}

const getVipBadge = (level: number) => {
    var index = 0
    if (level >= 1 && level <= 3) {
        index = 1
    } else if (level >= 4 && level <= 6) {
        index = 2
    } else if (level >= 7 && level <= 9) {
        index = 3
    } else if (level >= 10 && level <= 11) {
        index = 4
    } else if (level >= 12) {
        index = 5
    }
    return index
}

const getBadgeUrl = (level: number) => {
    let index = getVipBadge(level)
    const img = file['../../assets/img/vip/vip' + index + '.png'] as { default: string } | undefined
    return img?.default
}

onActivated(() => {
    level.value = vipInfo.value.curLevel
})
</script>

<style scoped lang="scss">
@use './vip.scss' as *;
@use 'sass:map';
.text-green {
    color: #00ff00;
}
.main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
}
.vip-benefits {
    width: 100%;
    padding: 0px 50px 50px 50px;
    font-family: 'Microsoft YaHei';
    border-radius: 20px;

    @for $i from 0 through 5 {
        &.vip-card-bg#{$i} {
            background-image: linear-gradient(0deg, #2a2d3d 85%, map.get(map.get($vip-list, $i), color) 115%);
            background-blend-mode: normal, normal;
        }
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 14px;
    margin-bottom: 20px;

    .selectbtn {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        font-size: 50px;
        font-weight: 1000;
        color: #fff;
        border: none;
    }
}

.header button.disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.badge img {
    width: 200px;
    height: 200px;
}

.badge-text {
    display: flex;
    flex-direction: column;
    padding: 10px;
    padding-bottom: 0px;
    gap: 10px;
    .vip-text {
        font-size: 44px;
        font-weight: bold;
        color: #fff;
    }

    .level {
        font-size: 26px;
        // color: rgb(179 190 193);
        font-weight: bold;
        .text-green {
            color: #24ee89;
        }
    }
}

.benefit-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 25px 0 25px 0;
    border-top: 0.5px solid #545557;
}

.benefit-card.disabled {
    opacity: 0.35;
}

.icon {
    width: 110px;
    height: 110px;
    background-color: rgb(58, 65, 66, 0.7);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(img) {
        width: 90px;
        height: 90px;
    }
}

.info {
    flex: 1;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    opacity: 0.9;
    margin-bottom: 6px;
}

.desc {
    font-size: 23px;
    color: #b0b8c0;
    opacity: 0.8;
    line-height: 1.3;
    font-family: avertastd-semibold;
    :deep(span) {
        color: #00ff00;
        font-size: 25px;
        font-weight: bold;
    }
}

.badge-wrapper {
    position: relative;
    width: 420px; /* 根据实际内容调整 */
    height: 260px; /* 根据实际内容调整 */
    overflow: hidden;
}

.selectbtn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 5px 15px;
}

.selectbtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.badge {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}
/* 切换动画 */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
    transition: all 0.3s ease;
}

.slide-left-enter-from {
    transform: translateX(100%);
    opacity: 0;
}
.slide-left-leave-to {
    transform: translateX(-100%);
    opacity: 0;
}

.slide-right-enter-from {
    transform: translateX(-100%);
    opacity: 0;
}
.slide-right-leave-to {
    transform: translateX(100%);
    opacity: 0;
}
</style>
