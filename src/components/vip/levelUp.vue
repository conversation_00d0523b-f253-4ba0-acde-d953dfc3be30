<template>
    <van-overlay v-model:show="visible">
        <div class="level-content">
            <div class="level-title">{{ $t(type === 0 ? 'popup_upgrade_title' : 'popup_maint_title') }}</div>
            <div class="vip-level">
                <img
                    v-for="(num, index) in `${level}`.split('')"
                    :key="index"
                    :src="`//betfugu.com/static/img/playtok/vip/number/number_${num}.png`"
                />
            </div>
            <div :class="['level-des', { active: levelUpText.length > 6 }]">
                <template v-for="(item, index) in levelUpText" :key="index">
                    <div v-if="getKeyValue(tipText, item.key)" class="des-item">
                        {{
                            $t(
                                item.text,
                                item.valKey
                                    ? {
                                          [item.valKey]: ['betRebate', 'depositBonus'].includes(item.key)
                                              ? formatPercent(getKeyValue(tipText, item.key) || 0)
                                              : getKeyValue(tipText, item.key),
                                      }
                                    : {}
                            )
                        }}
                    </div>
                </template>
            </div>
            <div class="level-info">
                <p class="info-title">{{ $t(type === 0 ? 'popup_upgrade_info' : 'popup_maint_info') }}</p>
                <div class="info-num">
                    <p class="info-symbol">{{ store.curSymbol[store.wallet?.currency] }}</p>
                    <p class="info-money">{{ (type === 0 ? tipText.reward?.gold : vipInfo?.monthReward?.gold) || 0 }}</p>
                </div>
            </div>
            <div class="level-btn" @click="handleReward">
                {{ (type === 0 ? tipText.reward?.gold : vipInfo?.monthReward?.gold) ? $t('Claim') : $t('Confirm') }}
            </div>
            <div
                v-if="store.vipRules?.levelupFlowmult"
                class="level-tip"
                v-html="
                    $t('claim_cash_times_info', {
                        number: store.vipRules.levelupFlowmult,
                    })
                "
            ></div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { LevelupVip } from '@/stores/types'
import { useI18n } from 'vue-i18n'
import { formatPercent } from '@/utils/toolsValidate'
import { getKeyValue } from '@/utils/index'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})
const emits = defineEmits(['close'])
const visible = defineModel()

const store = useBaseStore()
const i18n = useI18n()

const levelUpIdx = ref(0)

const { vipInfo, vipConfig } = storeToRefs(store)

const levelUpText = computed(() => {
    const config = [
        {
            text: 'popup_vipreward_maintain',
            key: 'monthReward?.gold',
            valKey: 'maintreward',
        },
        {
            text: 'popup_vipreward_bet',
            key: 'betRebate',
            valKey: 'betrebate',
        },
        {
            text: 'popup_vipreward_deposit3',
            key: 'depositBonus',
            valKey: 'deposit',
        },
        {
            text: 'popup_vipreward_withdraw1',
            key: 'withdrawAmount',
            valKey: 'limit',
        },
        {
            text: 'popup_vipreward_withdraw2',
            key: 'withdrawTimes',
            valKey: 'times',
        },
        {
            text: 'popup_vipreward_birthday',
            key: 'benefit',
            valKey: '',
        },
    ]
    if (props.type === 1) {
        return config
    }
    return [
        {
            text: 'popup_vipreward_upgrade',
            key: 'reward?.gold',
            valKey: 'upreward',
        },
        ...config,
    ]
})

const levelReward = computed<LevelupVip>(() => {
    return vipInfo.value?.levelUpReward[levelUpIdx.value] || {}
})

const level = computed(() => {
    return props.type === 0 ? levelReward.value?.level : vipInfo.value?.maintainLevel ? vipInfo.value?.maintainLevel : vipInfo.value?.curLevel
})
const levelRestore = computed<LevelupVip>(() => {
    return vipInfo.value?.monthReward ? vipConfig.value.find((item) => item.level === vipInfo.value?.curLevel) : {}
})
const tipText = computed<LevelupVip>(() => {
    return props.type === 0 ? levelReward.value : levelRestore.value
})
watch(
    () => vipInfo.value,
    (val) => {
        if (props.type === 0) {
            if (val.levelUpReward.length) {
                visible.value = true
                levelUpIdx.value = 0
            } else {
                visible.value = false
                emits('close', 1)
            }
        }
    }
)
const handleReward = async () => {
    try {
        const res = await store.pickVipRewards({
            type: props.type === 0 ? 'levelUp' : 'month',
            level: level.value,
        })
        if (res.code === 200) {
            if (props.type === 0 && levelUpIdx.value < vipInfo.value?.levelUpReward.length - 1) {
                levelUpIdx.value = levelUpIdx.value + 1
            } else {
                visible.value = false
                emits('close', props.type === 0 ? 1 : 2)
            }
            if (tipText.value.reward?.gold) {
                showSuccessToast(i18n.t('message_claim_success'))
            }
        } else {
            showFailToast(i18n.t('message_claim_fail'))
        }
    } catch (e) {
        console.log(e)
    }
}
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.level-content {
    width: 710px;
    height: 1006px;
    background-image: url('@/assets/img/vip/up_bg.png');

    .level-des {
        @apply grid grid-cols-2 gap-[10px] grid-rows-[35px];
        margin-top: 30px;
        height: 120px;
        .des-item {
            position: relative;
            padding-left: 15px;
            font-size: 26px;
            line-height: 35px;
            height: 35px;
            letter-spacing: -1px;
            color: #ffffff;

            &::before {
                content: '';
                display: inline-block;
                position: absolute;
                top: 50%;
                left: 5px;
                width: 5px;
                height: 5px;
                background-color: #ffffff;
                border-radius: 50%;
                transform: translateY(-50%);
            }
        }
        &.active {
            margin-top: 5px;
            height: 145px;
        }
    }
    .level-info {
        margin-top: 20px;

        .info-title {
            // line-height: 120px;
            padding-top: 40px;
            font-size: 28px;
            font-weight: bold;
            color: #663ce8;
            text-align: center;
        }
        .info-num {
            @apply flex justify-center;
            margin-bottom: 10px;
            color: #663ce8;
            font-weight: bold;

            .info-symbol {
                position: relative;
                top: 20px;
                font-size: 65px;
            }
            .info-money {
                font-size: 117px;
            }
        }
    }
}
.level-tip {
    color: #663ce8;
    font-family: 'Microsoft YaHei';
    font-size: 26px;
    text-align: center;
}
</style>
