<template>
    <div class="card">
        <div v-if="vipInfo.curLevel != 0" class="card-title">{{ $t(title, titleConfig) }}</div>
        <div :class="['card-info', { active: vipInfo.curLevel == 0 }]">
            <div class="card-info-0" v-if="vipInfo.curLevel == 0">
                <div class="brade"><img src="@/assets/img/vip/badge/0.png" /></div>
                <div class="text">
                    <div>{{ $t('vip_unlock') }}</div>
                </div>
            </div>
            <p class="info-title">{{ $t('info_reward') }}:</p>
            <div class="legal-wrap">
                <template v-for="(item, index) in config" :key="index">
                    <div
                        v-if="getKeyValue(levelConfig, item.key)"
                        :class="['legal-item', `legal-item${config.length === 7 ? index : index + 1}`, { scale: getKeyLen(levelConfig) <= 2 }]"
                    >
                        <div class="legal-cion">
                            {{
                                item.key !== 'benefit'
                                    ? item.type === 'percent'
                                        ? `${formatPercent(getKeyValue(levelConfig, item.key) || 0)}`
                                        : getKeyValue(levelConfig, item.key)
                                    : ''
                            }}
                        </div>
                        <p class="legal-name">{{ $t(item.text) }}</p>
                    </div>
                </template>
            </div>
            <div v-if="vipInfo.curLevel != 0" class="card-bar">
                <div class="bar-des">{{ $t(barTitle) }}</div>
                <div class="bar-wrap"><Bar :num="barNum.num" :target="barNum.target" /></div>
            </div>
        </div>
        <div class="unlock" v-if="vipInfo.curLevel == 0" @click="goWallet">{{ $t('vip_unlock_button', { number: vipInfo.rechargeMax || 0 }) }}</div>
    </div>
</template>
<script setup lang="ts">
import { VIP_LEGAL_CONFIG } from '@/enums'
import Bar from '@/components/vip/bar.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { formatPercent } from '@/utils/toolsValidate'
import { getKeyValue } from '@/utils/index'
import { useRouter } from 'vue-router'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})

const store = useBaseStore()
const router = useRouter()

const { vipInfo, vipConfig } = storeToRefs(store)

// 1 升级 2 恢复等级到 3 保级
const cardType = computed(() => {
    return props.type === 0 ? (!vipInfo.value.lockLevelUp ? 1 : 2) : 3
})
const config = computed(() => {
    if (cardType.value !== 3) {
        console.log(VIP_LEGAL_CONFIG, 'VIP_LEGAL_CONFIG')
        return VIP_LEGAL_CONFIG
    }

    return VIP_LEGAL_CONFIG.filter((item) => item.key !== 'reward.gold')
})
const title = computed(() => {
    return `info_upgrade_title${cardType.value}`
})
const titleConfig = computed(() => {
    if (cardType.value === 2) {
        return {
            maxlevel: vipInfo.value?.maxLevel,
        }
    }
    return {
        level: vipInfo.value?.curLevel + (cardType.value === 1 && vipInfo.value?.curLevel < 12 ? 1 : 0),
    }
})
const barTitle = computed(() => {
    return 'info_upgrade_goal' + cardType.value
})
const barNum = computed(() => {
    switch (cardType.value) {
        case 1:
            return {
                num: vipInfo.value?.exp,
                target: vipInfo.value?.max,
            }
        case 2:
            return {
                num: vipInfo.value?.recoverExp,
                target: vipInfo.value?.recoverMax,
            }
        case 3:
            return {
                num: vipInfo.value?.keepExp,
                target: vipInfo.value?.keepMax,
            }
        default:
            return {
                num: 0,
                target: 0,
            }
    }
})

const levelConfig = computed(() => {
    const level = cardType.value === 2 ? vipInfo.value?.maxLevel : vipInfo.value?.curLevel + (cardType.value !== 3 ? 1 : 0)
    return vipConfig.value.find((item) => item.level === level) || vipConfig.value[vipConfig.value.length - 1]
})
const getKeyLen = (config) => {
    let index = 0
    const keys = ['reward', 'monthReward', 'betRebate', 'depositBonus', 'benefit']
    keys.forEach((key) => {
        if (config[key]) {
            index++
        }
    })
    return index
}
const goWallet = () => {
    router.push({
        path: '/wallets',
        query: {
            back: 'true',
        },
    })
}
</script>
<style lang="scss" scoped>
.card {
    font-family: MicrosoftYaHei;
    color: #fff;

    .card-title {
        margin-bottom: 15px;
        font-size: 40px;
        font-weight: bold;
        text-align: center;
    }
    .card-info {
        width: 681px;
        padding: 21px 30px;
        background: url('@/assets/img/vip/card_bg.png') no-repeat;
        background-size: 100% 100%;

        &.active {
            padding: 0 0px 21px;

            .info-title {
                padding: 0 30px;
            }
            .legal-wrap {
                padding: 0 30px;
            }
        }

        .card-info-0 {
            @apply flex items-center;
            margin-bottom: 60px;
            width: 680px;
            height: 282px;
            padding: 0 36px;
            background: linear-gradient(90deg, #dcb587 0%, #b27c4c 100%);
            border-radius: 30px 30px 0 0;

            .brade {
                width: 200px;
                height: 220px;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            .text {
                flex: 1;
                font-family: MicrosoftYaHei;
                font-size: 56px;
                font-weight: bold;
                line-height: 38px;

                div {
                    line-height: 60px;
                    text-align: center;
                    color: #fff;
                    text-shadow: #a26937 2px 5px;

                    &:first-child {
                        margin-bottom: 10px;
                    }
                }
            }
        }
        .info-title {
            margin-bottom: 12px;
            font-size: 20px;
        }
        .legal-wrap {
            @apply flex;
            overflow-x: auto;

            .legal-item {
                margin-right: 30px;
                @apply flex-1 flex flex-col items-center;
                .legal-cion {
                    @apply flex flex-col justify-start items-center;
                    width: 90px;
                    height: 76px;
                    padding-top: 5px;
                    padding-bottom: 1px;
                    background: url('@/assets/img/vip/legal_bg.png') no-repeat;
                    background-size: 100% 100%;
                    font-size: 14px;
                    text-align: center;

                    &::before {
                        content: '';
                        display: inline-block;
                        width: 60px;
                        height: 49px;
                        background-size: 100%;
                        background-repeat: no-repeat;
                    }
                }
                .legal-name {
                    margin-top: 8px;
                    font-size: 17px;
                    text-align: center;
                }
            }
            @for $i from 0 through 6 {
                .legal-item#{$i} {
                    .legal-cion {
                        &::before {
                            background-image: url(@/assets/img/vip/legal/icon_#{$i}.png);
                        }
                    }
                }
            }
        }
        .card-bar {
            @apply flex items-center;
            margin-top: 30px;

            .bar-des {
                margin-right: 20px;
                font-size: 20px;
                color: #ffea76;
            }
            .bar-wrap {
                flex: 1;
            }
        }
    }

    .unlock {
        @apply flex items-center justify-center;
        margin: 45px auto 0;
        width: 584px;
        height: 91px;
        background-image: linear-gradient(150deg, #1acea1 0%, #0dcda0 40%, #00cc9e 100%);
        border-radius: 38px;
        font-family: MicrosoftYaHei;
        font-size: 40px;
        font-weight: bold;
        line-height: 26px;
        color: #ffffff;
    }
}
</style>
