<template>
    <div :class="['rule-table', `rule-table${type}`]">
        <div :class="['table-content', `table-content${type}`]">
            <div class="table-title">
                <div v-for="(item, index) in tableConfig" :key="index" class="title-item">
                    <div class="item-title" v-html="$t(item.title)"></div>
                    <p class="tip" v-if="item.tip">({{ item.tip }})</p>
                </div>
            </div>
            <div class="content">
                <div v-for="(item, index) in vipconfigs" :key="index" class="content-item">
                    <div :class="['item-icon']">{{ item.level }}</div>
                    <template v-if="type === 0">
                        <div>{{ item.reward?.gold || '-' }}</div>
                        <div>{{ item.monthReward?.gold || '-' }}</div>
                        <div>{{ formatPercent(item.betRebate || 0) || '-' }}</div>
                        <div>{{ item.withdrawAmount || '-' }}</div>
                    </template>

                    <template v-else>
                        <div>{{ item.exp || '-' }}</div>
                        <div>{{ item.keepExp || '-' }}</div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { formatPercent } from '@/utils/toolsValidate'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})
const store = useBaseStore()

const { vipConfig } = storeToRefs(store)
const vipconfigs = computed(() => {
    // return vipConfig.value.slice(props.type + 1)
    return vipConfig.value
})

const tableConfig = computed(() => {
    if (props.type === 1) {
        return [
            {
                title: 'form_vipreward_1',
                tip: '',
            },
            {
                title: 'form_viplevel_1',
                tip: 'Cumulative Bet',
            },
            {
                title: 'form_viplevel_2',
                tip: 'Monthly Bet',
            },
        ]
    }
    return [
        {
            title: 'form_vipreward_1',
            tip: '',
        },
        {
            title: 'form_vipreward_2',
        },
        {
            title: 'form_vipreward_3',
            tip: 'Monthly',
        },
        {
            title: 'form_vipreward_4',
            tip: 'Bonus → Balance',
        },
        {
            title: 'form_vipreward_7',
            tip: '2',
        },
    ]
})
</script>
<style lang="scss" scoped>
.rule-table {
    font-family: MicrosoftYaHei;
    color: #fff;

    .title {
        padding-left: 6px;
        // margin-bottom: 22px;
        font-size: 28px;
    }
    .sub-title {
        padding-left: 6px;
        font-size: 28px;
        // line-height: 34px;
        color: #9d9ea1;
    }

    .table-content {
        margin-top: 22px;
        padding: 6px;
        background-image: linear-gradient(rgba(75, 70, 68, 0.5), rgba(75, 70, 68, 0.5)), linear-gradient(rgba(58, 53, 51, 0.5), rgba(58, 53, 51, 0.5));
        background-blend-mode: normal, normal;
        border-radius: 30px;
    }
    .table-content0 {
        width: 100%;
        overflow-x: auto;
        .table-title {
            @apply flex flex-nowrap;
        }
        .table-title,
        .content-item {
            @apply flex flex-nowrap items-center;
            width: max-content;
        }
        .content {
            width: max-content;
        }
        .title-item {
            width: 150px;
            flex-shrink: 0;
            &:first-child {
                width: 122px;
            }
        }
        .content-item {
            flex-shrink: 0;
            div {
                width: 150px;
                &:first-child {
                    width: 122px;
                }
            }
        }
    }
    .table-content1 {
        .table-title,
        .content-item {
            @apply grid grid-cols-3;
        }
    }
    .table-title {
        box-sizing: border-box;
        margin: 0 auto;
        width: 708px;
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
        padding: 24px 0 10px;
        min-height: 108px;
        background-color: #454548;
        .title-item {
            // @apply flex items-center break-words;
            font-size: 20px;
            color: #eef3f9;
            text-align: center;
            .item-title {
                word-wrap: break-word;
                color: #ffd144;
            }

            .tip {
                font-size: 14px;
                color: #92929a;
            }
        }
    }
    .content-item {
        height: 100%;
        font-size: 24px;
        text-align: center;
        color: #fff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        &:last-child {
            border-bottom: 0;
        }
        .item-icon {
            width: 122px;
            height: 108px;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            transform: scale(0.8);

            @for $i from 0 through 12 {
                &.vip-#{$i} {
                    background-image: url('@/assets/img/vip/badge/#{$i}.png');
                }
            }
        }
        div {
            @apply flex items-center justify-center;
            height: 100%;
        }
    }
    &.rule-table1 {
        .table-title {
            padding-left: 18px;
            .title-item {
                // text-align: left;
                &:first-child {
                    padding-left: 15px;
                    text-align: left;
                }
            }
        }
        .content {
            .content-item {
                // .item-icon {
                //     margin: 0 auto;
                // }
                div {
                    // justify-self: flex-start;
                }
            }
        }
    }
}
</style>
