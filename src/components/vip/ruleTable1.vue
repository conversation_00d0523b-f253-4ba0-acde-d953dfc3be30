<template>
    <div class="vip-table-container">
        <div class="vip-table-wrapper" ref="tableWrapper">
            <table class="vip-table">
                <thead>
                    <tr>
                        <th v-for="(item, index) in tableConfig" :key="index" v-html="$t(item.title)"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in vipconfigs" :key="index">
                        <td>{{ item.exp }}</td>
                        <td>{{ item.level }}</td>
                        <td>{{ item.reward?.gold || 0 }}</td>
                        <td>{{ item.monthReward?.gold || 0 }}</td>
                        <td>{{ item.weekRewardWorth || 0 }}</td>
                        <td>{{ formatPercent(item.betRebate || 0) }}</td>
                        <td>{{ formatNumber(item.withdrawAmount || '0') }}</td>
                        <td>{{ formatNumber(item.withdrawNoFeeTimes || '0') }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="scroll-track">
            <div class="scroll-thumb" :style="{ width: thumbWidth + 'px', left: thumbPosition + 'px' }" @mousedown="startDrag"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { formatPercent } from '@/utils/toolsValidate'

const tableWrapper = ref(null)
const isDragging = ref(false)
const startX = ref(0)
const scrollLeft = ref(0)
const thumbWidth = ref(0)
const thumbPosition = ref(0)

const store = useBaseStore()

const { vipConfig } = storeToRefs(store)

const vipconfigs = computed(() => {
    return vipConfig.value
})

const tableConfig = computed(() => {
    return [
        {
            title: 'XP',
        },
        {
            title: 'form_vipreward_1',
            tip: '',
        },
        {
            title: 'form_vipreward_2',
            tip: '',
        },
        {
            title: 'vip_reward_month_title',
            tip: '',
        },
        {
            title: 'vip_reward_week_title',
            tip: '',
        },
        {
            title: 'vip_config_daily_title',
            tip: '',
        },
        {
            title: 'form_vipreward_8',
            tip: '',
        },
        {
            title: 'form_vipreward_9',
            tip: '',
        },
    ]
})

const formatNumber = function (num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}
const startDrag = function (e) {
    isDragging.value = true
    startX.value = e.pageX
    scrollLeft.value = this.$refs.tableWrapper.scrollLeft
    document.addEventListener('mousemove', onDrag)
    document.addEventListener('mouseup', stopDrag)
    document.body.style.cursor = 'grabbing'
}
const onDrag = function (e) {
    if (!isDragging.value) return
    e.preventDefault()
    const x = e.pageX - startX.value
    this.$refs.tableWrapper.scrollLeft = scrollLeft.value - x
    this.updateScrollThumb()
}
const stopDrag = function () {
    isDragging.value = false
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', stopDrag)
    document.body.style.cursor = ''
}
const updateScrollThumb = function () {
    const wrapper = tableWrapper.value
    const scrollWidth = wrapper.scrollWidth
    const clientWidth = wrapper.clientWidth
    const scrollLeft = wrapper.scrollLeft

    // 计算滚动条滑块宽度
    thumbWidth.value = (clientWidth / scrollWidth) * 0.5 * clientWidth

    // 计算滚动条滑块位置
    thumbPosition.value = (scrollLeft / (scrollWidth - clientWidth)) * (clientWidth - thumbWidth.value)
}
const handleResize = function () {
    updateScrollThumb()
}

onMounted(() => {
    updateScrollThumb()
    tableWrapper.value.addEventListener('scroll', updateScrollThumb)
    window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
    tableWrapper.value.removeEventListener('scroll', updateScrollThumb)
    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.vip-table-container {
    width: 95%;
    overflow: hidden;
    padding-bottom: 100px;
    border-radius: 10px;
}

.vip-table-wrapper {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    margin-bottom: 8px;
}

.vip-table-wrapper::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.vip-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 600px; /* 确保表格足够宽 */
    background-color: rgb(50, 55, 56);
    color: rgba(255, 255, 255, 0.9);
}

.vip-table th,
.vip-table td {
    font-size: 20px;
    padding: 12px 16px;
    text-align: center;
    /* border: 1px solid #e0e0e0; */
    white-space: nowrap;
    font-weight: 600;
}

.vip-table th {
    background-color: #232626;

    position: sticky;
    top: 0;
}

.vip-table tr:nth-child(even) {
    background-color: #232626;
}

.vip-table tr:hover {
    /* background-color: #f0f0f0; */
}

.scroll-track {
    width: 100%;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 8px;
    position: relative;
}

.scroll-thumb {
    height: 10px;
    background-color: #888;
    border-radius: 48px;
    position: absolute;
    cursor: grab;
}

.scroll-thumb:active {
    cursor: grabbing;
}
</style>
