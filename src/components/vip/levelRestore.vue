<template>
    <van-overlay v-model:show="visible">
        <div class="level-content">
            <div class="level-title">{{ $t('popup_restore_title') }}</div>
            <div class="vip-level">
                <img
                    v-for="(num, index) in `${store.vipInfo.triggerRecover}`.split('')"
                    :key="index"
                    :src="`//betfugu.com/static/img/playtok/vip/number/number_${num}.png`"
                />
            </div>
            <div class="on-wrap">
                <div class="legal-wrap">
                    <template v-for="(item, index) in VIP_LEGAL_CONFIG" :key="index">
                        <div v-if="getKeyValue(config, item.key)" :class="['legal-item', `legal-item${index}`]">
                            <div class="legal-cion">
                                {{
                                    item.key !== 'benefit'
                                        ? ['betRebate', 'depositBonus'].includes(item.key)
                                            ? formatPercent(getKeyValue(config, item.key) || 0, 2)
                                            : getKeyValue(config, item.key)
                                        : ''
                                }}
                            </div>
                            <p class="legal-name">{{ $t(item.text) }}</p>
                        </div>
                    </template>
                </div>
            </div>
            <div class="level-btn" @click="handleClick">{{ $t('Confirm') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { VIP_LEGAL_CONFIG } from '@/enums'
import { formatPercent } from '@/utils/toolsValidate'
import { getKeyValue } from '@/utils/index'

const emits = defineEmits(['close'])

const visible = defineModel()
const store = useBaseStore()

const trigger = () => {
    return store.triggerVip({ type: 'Recover' })
}

const handleClick = async () => {
    await trigger()
    visible.value = false
    emits('close', 0)
}
const config = computed(() => {
    return store.vipConfig.find((item) => item.level === store.vipInfo.triggerRecover)
})
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.level-content {
    width: 710px;
    height: 787px;
    background-image: url('@/assets/img/vip/on_bg.png');

    .level-des {
        @apply grid grid-cols-2 gap-[10px];
        margin-top: 30px;
        .des-item {
            font-size: 26px;
            line-height: 35px;
            letter-spacing: -1px;
            color: #ffffff;
        }
    }
    .on-wrap {
        margin: 48px 0 33px;
        height: 177px;
        .legal-wrap {
            @apply flex  items-center;
            height: 100%;
            overflow-x: auto;
            // padding: 0 16px 0 20px;

            .legal-item {
                @apply flex-1 flex flex-col items-center;
                margin-right: 20px;
                .legal-cion {
                    @apply flex flex-col justify-start items-center;
                    width: 117px;
                    height: 98px;
                    padding-top: 10px;
                    background: url('@/assets/img/vip/down_legal.png') no-repeat;
                    background-size: 100% 100%;

                    font-size: 24px;
                    line-height: 25px;

                    &::before {
                        align-self: top;
                        content: '';
                        display: inline-block;
                        margin-bottom: 10px;
                        width: 60px;
                        height: 49px;
                        background-size: 100%;
                        background-repeat: no-repeat;
                    }
                }
                .legal-name {
                    margin-top: px;
                    font-size: 18px;
                    text-align: center;
                }
            }
            @for $i from 0 through 6 {
                .legal-item#{$i} {
                    .legal-cion {
                        &::before {
                            background-image: url(@/assets/img/vip/legal/icon_#{$i}.png);
                        }
                    }
                }
            }
        }
    }
}
</style>
