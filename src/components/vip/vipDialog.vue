<template>
    <div>
        <LevelDown v-model="dialog.down" />
        <LevelRestore v-model="dialog.restore" @close="handleClose" />
        <LevelUp v-model="dialog.up" @close="handleClose" />
        <LevelUp :type="1" v-model="dialog.on" />
        <VipCs v-model="dialog.vip" />
    </div>
</template>
<script setup lang="ts">
import LevelUp from '@/components/vip/levelUp.vue'
import LevelDown from '@/components/vip/levelDown.vue'
import LevelRestore from '@/components/vip/levelRestore.vue'
import VipCs from '@/components/vip/vipCs.vue'

import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'

const store = useBaseStore()
const { vipInfo } = storeToRefs(store)

const dialog = reactive({
    up: false,
    down: false,
    restore: false,
    on: false,
    vip: false,
})
const handleDialog = () => {
    if (vipInfo.value.triggerDowngrade) {
        dialog.down = true
        return
    }
    if (vipInfo.value.triggerRecover) {
        dialog.restore = true
    } else if (vipInfo.value?.levelUpReward && vipInfo.value?.levelUpReward.length) {
        dialog.up = true
    } else if (vipInfo.value.monthReward) {
        dialog.on = true
    } else if (vipInfo.value?.triggerAdvanced) {
        dialog.vip = true
    }
}
const handleClose = (type = 0) => {
    switch (type) {
        case 0:
            if (vipInfo.value?.levelUpReward && vipInfo.value?.levelUpReward.length) {
                dialog.up = true
            } else {
                handleClose(1)
            }
            break
        case 1:
            if (vipInfo.value.monthReward) {
                dialog.on = true
            } else {
                handleClose(2)
            }
            break
        case 2:
            if (vipInfo.value?.triggerAdvanced) {
                dialog.vip = true
            }
            break
        default:
            break
    }
}
watch(vipInfo, () => {
    handleDialog()
})
onMounted(() => {
    handleDialog()
})
</script>
<style lang="scss" scoped></style>
