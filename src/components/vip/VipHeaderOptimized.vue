<template>
    <div class="vip-header-box">
        <!-- VIP 等级卡片 -->
        <div class="vip-card" :class="[`vip-card-bg${getVipBadge(vipInfo.curLevel)}`]">
            <div class="vip-info">
                <div class="vip-title">
                    VIP {{ vipInfo.curLevel }} <span class="vip-sub"> ( Deposit {{ store.curSymbol[store.wallet?.currency] }} 1 = 1 XP )</span>
                </div>
                <img class="vip-badge" :src="getVipIcon(vipInfo.curLevel)" alt="vip-badge" />
                <div class="vip-xp">
                    <span :style="{ color: '#fff' }">{{ vipInfo.exp }} XP</span> / {{ vipInfo.max }} XP
                </div>
                <div class="vip-progress">
                    <div class="bar">
                        <div class="fill" :style="{ width: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                        <div class="bar_point1" :style="{ left: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                        <div class="bar_point2" :style="{ left: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn-box green" @click="goWallet">Deposit</button>
                    </div>
                </div>
            </div>
            <!-- 奖励卡片 -->
            <div class="vip-rewards">
                <div class="reward-card" v-for="item in rewardList" :key="item.key">
                    <div class="title">
                        <div class="icon"><img :src="item.icon" /></div>
                        {{ $t(item.title) }}
                    </div>

                    <div class="footer-card">
                        <div class="claim-btn" v-if="!isDisabled(item)">
                            <div class="amount">
                                <span>{{ store.curSymbol[store.wallet?.currency] }} </span>
                                {{ item.key == 'dailyReward' ? dailyReward : getReward(item.key) }}
                            </div>
                            <button :class="['btn-claim press-effect-btn', { disabled: isDisabled(item) }]" @click="ClaimReward(item)">
                                <span class="text-wrapper">
                                    {{ checkReward(item) ? i18n.t('Claim') : countdowns[item.type] }}
                                </span>
                            </button>
                        </div>
                        <div v-if="isDisabled(item)" class="claim-recieved">
                            <button :disabled="isDisabled(item)" class="btn-revieved">Recieved</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="vip-footer-text">Upgrade VIP level and Enjoy more benefits!</div>
        </div>
    </div>
    <signaward v-model="claimawardinfo.isshow" v-if="claimawardinfo.isshow" :type="claimawardinfo.type" :msg="claimawardinfo.items" />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import dayjs from 'dayjs'
import signaward from '../dialog/signaward.vue'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { getKeyValue } from '@/utils/index'
import { useI18n } from 'vue-i18n'
import eventBus, { EVENT_KEY } from '@/utils/bus'
import GoldPic from '@/assets/img/dialog/sign/cash.png'
const file = import.meta.glob('../../assets/img/vip/*', { eager: true })
const store = useBaseStore()
const router = useRouter()
const { vipInfo, vipConfig } = storeToRefs(store)
const i18n = useI18n()

const losetime = ref(0)
const dailyReward = ref(0)
const claimawardinfo = ref({
    isshow: false,
    type: 2,
    items: {},
})

const getReward = (key) => {
    let reward = 0
    switch (key) {
        case 'dailyReward':
            reward = dailyReward.value || 0
            break
        case 'monthReward':
            reward = vipConfig.value[vipInfo.value.curLevel]?.monthReward['gold'] || 0
            break
        case 'weekReward':
            reward = vipConfig.value[vipInfo.value.curLevel]?.weekRewardWorth || 0
            break
        case 'levelUpReward':
            reward = vipInfo.value.levelUpReward ? vipInfo.value.levelUpReward[0]?.reward['gold'] : 0
            break
        default:
            break
    }
    return reward
}

//判断点击按钮显示文本
const checkReward = (item) => {
    if (item.type === 'weekReward' && vipInfo.value.weekReward.length >= 0) {
        return true
    }

    if (item.type === 'monthReward' && !!vipInfo.value.monthReward) {
        return true
    }

    return countdowns.value[item.type] === '00:00' || item.key === 'dailyReward' || item.key === 'levelUpReward' ? true : false
}

const isDisabled = (item) => {
    if (item.type === 'levelup') {
        return !getReward(item.key)
    } else {
        return false
    }
}

const countdowns = ref({
    daily: '',
    weekly: '',
    monthly: '',
    levelup: '',
})

const rewardList = [
    {
        key: 'dailyReward',
        title: 'vip_reward_daily_title',
        desc: 'vip_reward_daily_desc',
        icon: new URL('@/assets/img/vip/reward_bonus.png', import.meta.url).href,
        type: 'daily',
    },
    {
        key: 'weekReward',
        title: 'vip_reward_week_title',
        desc: 'vip_reward_week_desc',
        icon: new URL('@/assets/img/vip/reward_weekly.png', import.meta.url).href,
        type: 'weekly',
    },
    {
        key: 'monthReward',
        title: 'vip_reward_month_title',
        desc: 'vip_reward_month_desc',
        icon: new URL('@/assets/img/vip/reward_monthly.png', import.meta.url).href,
        type: 'monthly',
    },
    {
        key: 'levelUpReward',
        title: 'vip_reward_levelup_title',
        desc: 'vip_reward_levelup_desc',
        desc2: 'curLevel',
        icon: new URL('@/assets/img/vip/reward_levelup.png', import.meta.url).href,
        type: 'levelup',
    },
]

const getVipBadge = (level: number) => {
    var index = 0
    if (level >= 1 && level <= 3) {
        index = 1
    } else if (level >= 4 && level <= 6) {
        index = 2
    } else if (level >= 7 && level <= 9) {
        index = 3
    } else if (level >= 10 && level <= 11) {
        index = 4
    } else if (level >= 12) {
        index = 5
    }
    return index
}

const getVipIcon = (level: number) => {
    let idx = getVipBadge(level)
    const img = file['../../assets/img/vip/vip' + idx + '.png'] as { default: string } | undefined
    return img?.default
}

const formatCountdown = (target: number) => {
    const diff = target || 0
    if (diff <= 0) return '00:00'
    const hours = Math.floor(diff / (60 * 60))
    const minutes = Math.floor((diff / 60) % 60)
    const seconds = Math.floor(diff % 60)
    const days = Math.floor(hours / 24)

    // 补零函数
    const padZero = (num: number): string => num.toString().padStart(2, '0')

    // 格式化各部分
    const formattedHours = padZero(hours)
    const formattedMinutes = padZero(minutes)
    const formattedSeconds = padZero(seconds)

    // 根据是否有天数返回不同格式
    return days > 0 ? `${days}day:${formattedMinutes}:${formattedSeconds}` : `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
}

// const getNextZero = (offset = 0) => dayjs().add(offset, 'day').startOf('day').toDate()
// const getNextSunday = () =>
//     dayjs()
//         .add((7 - dayjs().day()) % 7 || 7, 'day')
//         .startOf('day')
//         .toDate()
// const getNextMonth1st = () => dayjs().add(1, 'month').startOf('month').toDate()

let timer: number
const updateCountdowns = () => {
    losetime.value += 1
    countdowns.value.daily = '00:00'
    countdowns.value.weekly = formatCountdown((vipInfo.value?.weekRemain || 0) - losetime.value)
    countdowns.value.monthly = formatCountdown((vipInfo.value?.monthRemain || 0) - losetime.value)
    countdowns.value.levelup = '00:00'
}
onMounted(() => {
    losetime.value = 0
    updateCountdowns()
    timer = window.setInterval(updateCountdowns, 1000)
})
onUnmounted(() => clearInterval(timer))

const goWallet = () => {
    // 跳转充值页
    router.push({ path: '/wallets', query: { back: 1 } })
}
const ClaimReward = async (item) => {
    if (!checkReward(item)) return
    try {
        var t = 'levelUp'
        let level = vipInfo.value?.curLevel
        if (item.type === 'daily') {
            t = 'daily'
            if (dailyReward.value <= 0) {
                return
            }
            claimBouns()
            return
        } else if (item.type === 'monthly') {
            t = 'month'
        } else if (item.type === 'weekly') {
            t = 'week'
        } else {
            const rewardinfo = getKeyValue(vipInfo.value, 'levelUpReward[0]') || {}
            level = rewardinfo.level
        }
        const awardnum = getReward(item.key) || 0
        if (awardnum <= 0) {
            return
        }
        const res = await store.pickVipRewards({
            type: t,
            level: level,
        })
        if (res.code === 200) {
            console.log('-------------------------', res)
            // :
            // 200
            // reward
            // :
            // Minesfreebet5
            // :
            // 5
            // [[Prototype]]
            // :
            // Object
            // worth
            // :
            // 25

            // showSuccessToast(i18n.t('message_claim_success'))

            if (res.reward && res.reward.Minesfreebet5) {
                claimawardinfo.value = {
                    isshow: true,
                    type: 2,
                    items: {
                        img: GoldPic,
                        name: 'Cash',
                        worth: awardnum,
                    },
                }
            }
            claimawardinfo.value = {
                isshow: true,
                type: 2,
                items: {
                    img: GoldPic,
                    name: 'Cash',
                    worth: awardnum,
                },
            }
            getVipInfo(false)
        } else {
            showFailToast(i18n.t('message_claim_fail'))
        }
    } catch (e) {
        console.log(e)
    }
}

const checkBonusClaim = () => {
    store.getBonus(0, false).then((res) => {
        if (res.code === 200) {
            dailyReward.value = res.gold
        }
    })
}

const claimBouns = () => {
    store.getBonus(1, false).then((res) => {
        if (res.code === 200 && res.gold > 0) {
            claimawardinfo.value = {
                isshow: true,
                type: 2,
                items: {
                    img: GoldPic,
                    name: 'Cash',
                    worth: dailyReward.value,
                },
            }
            dailyReward.value = 0
        }
    })
}

const getVipInfo = async (loading = true) => {
    try {
        const info = await store.getVipInfo(loading)
        if (info.code === 200) {
            losetime.value = 0

            store.setvipInfo(info)
            //console.error(info)
            checkBonusClaim()
        }
    } catch (e) {
        console.log(e)
    }
}
onActivated(() => {
    getVipInfo(false)
})
</script>

<style scoped lang="scss">
@use './vip.scss' as *;
@use 'sass:map';

.vip-header-box {
    max-width: 750px;
    margin: 0 auto;
    padding: 16px;
    padding-top: constant(safe-area-inset-top);
    padding-top: max(env(safe-area-inset-top), 38px);
    font-family: 'Microsoft YaHei', sans-serif;
    color: #fff;
    display: flex;
    flex-direction: column;

    .vip-card {
        margin-top: 50px;
        border-radius: 20px;
        padding: 24px 20px 20px 40px;
        position: relative;
        height: 780px;

        @for $i from 0 through 5 {
            &.vip-card-bg#{$i} {
                background-color: #2a2d3d;
                //map.get(map.get($vip-list, $i), color);;
                background-image: url('@/assets/img/vip/vipbg#{$i}.png');
                background-size: 100% auto;
                background-repeat: no-repeat;

                // background-image: linear-gradient(0deg, map.get(map.get($vip-list, $i), color) 80%, #403930 100.63%);
                // background-blend-mode: normal, normal;
            }
        }

        .vip-info {
            width: 100%;
            height: 180px;
            .vip-title {
                height: 50px;
                font-size: 42px;
                font-weight: 500;
                align-items: center;
                display: flex;
                margin-top: 10px;

                .vip-sub {
                    margin-left: 20px;
                    font-size: 20px;
                    font-weight: normal;
                }
            }

            .vip-xp {
                font-size: 22px;
                color: rgb(179 190 193);
                margin: 20px 0 -15px 0;
            }
            .vip-progress {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 8px;

                .bar {
                    flex: 1;
                    position: relative;
                    height: 11px;
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 6px;

                    .fill {
                        height: 100%;
                        border-radius: 6px;
                        background: linear-gradient(-90deg, #43b152, #42b691 50%, #4cb5c8);
                    }
                    .bar_point1 {
                        height: 30px;
                        width: 31px;
                        border-radius: 50%;
                        position: absolute;
                        top: -9px;
                        transform: translateX(-50%);
                        background: rgba(36, 238, 137, 0.3);
                    }
                    .bar_point2 {
                        height: 15px;
                        width: 15px;
                        border-radius: 50%;
                        position: absolute;
                        top: -1.2px;
                        transform: translateX(-50%);
                        background: rgb(36 238 137);
                    }
                }
            }
            .action-buttons {
                display: flex;
                width: 220px;
                height: 70px;
                align-items: center;
                justify-content: center;

                .btn-box {
                    width: 120px;
                    height: 48px;
                    font-size: 20px;
                    font-weight: bold;
                    border-radius: 12px;
                    cursor: pointer;
                    color: #000;
                    font-weight: 800;
                    align-items: center;
                    display: flex;
                    justify-content: center;
                }

                .green {
                    background-image: linear-gradient(90deg, #24ee89, #9fe871);
                    box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
                }
            }
            .vip-badge {
                width: 220px;
                height: 220px;
                right: 20px;
                margin-top: -170px;
                position: absolute;
            }
        }
    }

    .vip-rewards {
        width: 100%;
        .reward-card {
            display: flex;
            border-radius: 16px;
            padding: 10px 40px 12px 10px;
            justify-content: space-between;

            .title {
                font-size: 28px;
                font-weight: bold;
                gap: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                .icon {
                    width: 96px;
                    height: 96px;
                    display: flex;
                    background-color: #79591b;
                    border-radius: 25px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    :deep(img) {
                        width: 85px;
                        height: 85px;
                    }
                }
            }
            .desc {
                font-size: 20px;
                color: #aaa;
                margin: 4px 0 8px;
            }
            .footer-card {
                display: flex;

                .claim-recieved {
                    display: flex;
                    width: 114px;
                    height: 90px;
                    align-items: center;
                    justify-self: center;
                    font-size: 20px;
                    .btn-revieved {
                        width: 100%;
                        height: 44px;
                        padding: 4px 12px;
                        font-weight: 400;
                        background-color: rgb(179, 190, 193);
                        color: #000;
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                .claim-btn {
                    display: flex;
                    width: 114px;
                    height: 90px;
                    background-color: rgba(255, 255, 255, 0.14);
                    border-radius: 9px;
                    align-items: center;
                    flex-direction: column;
                    font-size: 20px;
                    .amount {
                        display: flex;
                        font-size: 28px;
                        font-weight: bold;
                        line-height: 46px;
                        text-align: center;
                        color: #fff;
                        gap: 6px;
                        :deep(span) {
                            font-size: 28px;
                            font-weight: 100;
                            color: #24ee89;
                        }
                    }
                    .btn-claim {
                        width: 100%;
                        height: 44px;
                        padding: 4px 12px;
                        font-weight: 400;
                        background-image: linear-gradient(90deg, #24ee89, #9fe871);
                        color: #000;
                        border-radius: 8px;
                        font-size: clamp(12px, 3vw, 20px);
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .text-wrapper {
                            font-size: min(18px, 3.5vw); /* 基于视口的动态调整 */
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            max-width: 100%;
                            display: inline-block;
                            // transition: font-size 0.3s ease;
                        }
                        /* 自动调整大小的关键 */
                    }
                    @media (max-width: 768px) {
                        .text-wrapper {
                            font-size: min(14px, 3vw);
                        }
                    }
                }

                .disabled {
                    opacity: 0.4; // 变灰
                    cursor: not-allowed; // 鼠标样式变为禁止
                    pointer-events: none; // 确保不会响应点击（可选，双保险）
                    background-color: #aaa; // 可选：强制设置背景为灰色
                    color: #fff; // 根据需求设定文字颜色
                }
            }
        }
    }

    .vip-footer-text {
        margin-top: 40px;
        text-align: center;
        font-size: 20px;
        color: #fff;
    }
    .press-effect-btn {
        padding: 10px 20px;
        background: #42b983;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.1s ease;
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .press-effect-btn:active {
        transform: translateY(2px);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
}
</style>
