.level-content {
    position: absolute;
    top: 50%;
    left: 50%;
    padding: 0 28px;
    transform: translate3d(-50%, -50%, 0);
    font-family: MicrosoftYaHei;
    color: #fff;
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .level-title {
        padding-top: 83px;
        font-size: 38px;
        font-weight: bold;
        text-align: center;
    }
    .vip-level {
        @apply flex;
        // position: relative;
        // top: 108px;
        height: 98px;
        padding-left: 400px;
        margin-top: 108px;
        img {
            height: 100%;
        }
    }
    .level-btn {
        margin: 0 auto;
        width: 347px;
        height: 75px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb944 40%, #ffad38 100%);
        border-radius: 38px;
        font-size: 38px;
        color: #fff;
        text-align: center;
        line-height: 75px;
        font-weight: bold;
    }
}
