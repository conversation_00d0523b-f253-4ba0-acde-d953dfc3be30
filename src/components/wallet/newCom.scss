$headH: 450px;
$titleH: 80px;
$btnH: 118px;

.wallet-content {
    position: relative;
    margin: 0 auto;
    width: 100%;
    padding: 0 26px;
    
    // background: #606165 url('@/assets/img/wallets/scroll_bg.png') no-repeat left bottom / 700px 332px;
    background-color: #333738;
    border-radius: 15px;

    .wallet-title {
        height: $titleH;
        font-size: 30px;
        line-height: 80px;
        letter-spacing: -1px;
        color: #ffffff;
    }
    .wallet-btn {
        height: $btnH;
        padding-top: 10px;

        .btn {
            margin: 0 auto;
            width: 440px;
            height: 88px;
            line-height: 88px;
            background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
            background-blend-mode: normal, normal;
            border-radius: 44px;
            font-size: 36px;
            color: #ffffff;
            text-align: center;
        }
    }
    .wallet-scroll {
        padding: 0 26px;
        height: calc(100% - $titleH - $btnH);
        overflow-y: scroll;
    }
}

.select-list {
    @apply grid grid-cols-4 gap-[20px];

    .list-item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 150px;
        height: 80px;
        aspect-ratio: 2 / 1;
        // line-height: 100px;
        // background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#ffffff, #ffffff);
        background-color: #3B4142aa;
        background-blend-mode: normal, normal;
        border-radius: 10px;
        border: solid 4px #484E50;
        text-align: center;
        font-size: 32px;
        font-weight: 400;
        color: #fff;

        span {
            margin-left: 2px;
            color: #ffa72a;
        }

        &.active {
            border-color: #5ECA9C;
            background-color: #5ECA9C33;

            // &::after {
            //     position: absolute;
            //     right: -5px;
            //     // bottom: -4px;
            //     display: inline-block;
            //     content: '';
            //     width: 52px;
            //     height: 52px;
            //     // background: url('@/assets/img/wallets/checked.png') no-repeat;
            //     background-size: 100% 100%;
            // }
        }
    }
    
}

.select-list1 {
    @apply grid grid-cols-4 gap-[20px];
    &.select-list-icon {
        @apply grid grid-cols-3 gap-[30px];
        
    }
    
    .list-item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 150px;
        height: 60px;
        aspect-ratio: 2 / 1;
        // line-height: 100px;
        // background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#ffffff, #ffffff);
        background-color: #3B4142aa;
        background-blend-mode: normal, normal;
        border-radius: 10px;
        border: solid 4px #484E50;
        text-align: center;
        font-size: 26px;
        font-weight: 400;
        
        color: #fff;
        &.list-item-icon {
            width:202px;
            height: 62px;
            padding: 0px 20px;
        }
        span {
            margin-left: 2px;
            color: #ffa72a;
        }

        &.active {
            border-color: #5ECA9C;
            background-color: #5ECA9C33;
        }
        &.status {
            opacity: 0.7;
            background-color: #7d7d7d;
            &::after {
                position: absolute;
                right: 1px;
                top: 1px;
                display: inline-block;
                content: '';
                width: 30px;
                height: 30px;
                background: url('@/assets/img/wallets/maintenance_icon.png') no-repeat;
                background-size: 100% 100%;
                filter: brightness(0) invert(1);
            }
        }
    }
    
}

