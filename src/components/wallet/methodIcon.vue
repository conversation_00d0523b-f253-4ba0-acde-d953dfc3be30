<template>
    <div class="method-icon">
        <img v-if="currency === 'USD'" src="@/assets/img/wallets/method_icon/cashapp.png" />
        <img v-if="currency === 'TRY'" src="@/assets/img/wallets/method_icon/papara.png" />
        <img v-if="currency === 'BRL'" src="@/assets/img/wallets/method_icon/pix.png" />
        <img v-if="currency === 'NGN'" src="@/assets/img/wallets/method_icon/palmpay.png" />
        <img v-if="currency === 'INR'" src="@/assets/img/wallets/method_icon/imps.avif" />
        <img v-else src="@/assets/img/wallets/method_icon/gcash-1.png" />
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'

const store = useBaseStore()

const currency = computed(() => store.wallet?.currency)
</script>
<style lang="scss" scoped>
.method-icon {
    position: relative;
    margin: 0 auto;
    width: 250px;
    height: 88px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #202328;
    border: 3px solid #00ff7f;

    img {
        max-width: inherit !important;
        width: 80%;
        height: auto;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>
