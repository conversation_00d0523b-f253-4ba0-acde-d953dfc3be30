<template>
    <div class="record-wrap">
        <div class="record-title">
            <p v-for="(item, index) in tab" :key="index">{{ $t(`${item}`) }}</p>
        </div>
        <div class="record-content">
            <ScrollList ref="scroll" :api="getList" :disableRefresh="false" @onChange="getRecordList">
                <template #default>
                    <template v-if="!isEmpty">
                        <div class="record-item" v-for="item in recordList" :key="item">
                            <p>{{ dayjs(item._source.time).format('YYYY-MM-DD HH:mm:ss') }}</p>
                            <p>{{ item._source.context.purchase.item.worth?.gold }}</p>
                            <p>{{ $t('Recharge_successful') }}</p>
                        </div>
                    </template>
                    <van-empty
                        image="//betfugu.com/static/img/playtok/wallet/no_data.png"
                        v-else
                        :description="$t('No_recharge_record')"
                    ></van-empty>
                </template>
            </ScrollList>
        </div>
    </div>
</template>
<script lang="ts" setup name="deposit-record">
import ScrollList from '@/components/ScrollList.vue'
import { getOrders } from '@/api/wallet'
import dayjs from 'dayjs'

const tab = ['Time', 'Amount', 'Status']

const isEmpty = ref(false)
const scroll = ref(null)
const recordList = ref([])

const getList = async (params) => {
    const { page, pageSize } = params
    try {
        const res = await getOrders(
            {
                from: (page - 1) * pageSize,
                size: pageSize,
            },
            false
        )
        if (res.code === 200) {
            isEmpty.value = res.data.total.value === 0
            return res.data.hits
        } else {
            return []
        }
    } catch (e) {
        return []
    }
}

const getRecordList = (list, state) => {
    recordList.value = state.reset ? [...list] : [...recordList.value, ...list]
}
</script>
<style lang="scss" scoped>
.record-title,
.record-item {
    @apply grid-cols-3;
}
.record-item {
    line-height: 48px;
    p {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
