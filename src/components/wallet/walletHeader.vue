<template>
    <div class="wallet-header">
        <div class="wallet-money-wrap">
            <div class="wallet-money">
                <div class="money-count">{{ formatLocalMoney(wallet?.gold || 0) }}</div>
            </div>
            <div class="wallet-select" @click="handleChangeCurrency">
                <div class="select-pic"><img :src="currency.picture" /></div>
                <div class="select-cur">{{ currency.currency }}({{ currency.clientcurrency }})</div>
                <div class="select-icon"></div>
            </div>
        </div>

        <div class="wallet-other">
            <div class="other-left">
                <span class="other-help" @click="showTip = true"></span>
                <span class="other-title bonus">Bonus</span>
                <span class="other-count">{{ currency.clientcurrency }} {{ formatNormalNumber(wallet?.bonus || 0) }}</span>
            </div>
            <div class="other-right">
                <UnderlineButton @click="handleRecoder">Record</UnderlineButton>
            </div>
        </div>
        <Record v-if="recordVisible" v-model="recordVisible" :type="type" />
        <Currency v-model="showCurrency" @confirm="onSelect" @getData="getCurrencyConf" :default="wallet.currency" />
        <Tip
            class="wallet-tip"
            v-model="showTip"
            title="Bonus"
            :content="
                vipInfo.curLevel == 0
                    ? $t('bonus_explanation_novip')
                    : $t('bonus_explanation', {
                          betrebate: betRebate,
                      })
            "
        />
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import Currency from '@/components/wallet/currencyDialog.vue'
import Record from '@/components/wallet/record.vue'
import { formatLocalMoney, formatNormalNumber } from '@/utils/toolsValidate'
import { Log } from '@/api/log'
import Tip from './tip.vue'
import UnderlineButton from '../common/UnderlineButton.vue'

// import { onBeforeRouteLeave } from 'vue-router'

const props = defineProps({
    type: {
        type: Number,
        default: () => 1,
    },
    title: {
        type: String,
        default: '',
    },
    balance: {
        type: Number,
        default: () => 0,
    },
})
const emits = defineEmits(['getData'])
const store = useBaseStore()
const { wallet, currencyList, vipInfo, vipConfig } = storeToRefs(store)
const showCurrency = ref(false)
const recordVisible = ref(false)
const showTip = ref(false)

const currency = computed(() => {
    return currencyList.value.find((item) => item.currency === wallet.value?.currency) || currencyList.value[0]
})
const betRebate = computed(() => {
    return (vipConfig.value.find((item) => item.level === vipInfo.value.curLevel)?.betRebate || 0) * 1000 + '‰'
})
const onSelect = (item) => {
    wallet.value.currency = item.currency
}

const getCurrencyConf = (confs) => {
    emits('getData', confs)
}

const handleRecoder = () => {
    Log({ event: props.type === 1 ? 'wallet_deposit_record' : 'wallet_withdraw_record' })
    recordVisible.value = true
}
const handleChangeCurrency = () => {
    Log({ event: 'wallet_exch_currency' })
    showCurrency.value = true
}
</script>
<style lang="scss" scoped>
.wallet-header {
    padding: 26px 30px 0;
    height: 170px;
    margin-bottom: 20px;
    background-size: 100% 450px;
    border-radius: 15px;
    font-family: MicrosoftYaHei;
    background: #333738;

    .wallet-other {
        padding-top: 20px;
        display: flex;
        align-items: center;

        .other-left {
            display: flex;
            align-items: center;
            flex: 1;

            .other-help {
                display: inline-block;
                margin-right: 8px;
                width: 30px;
                height: 30px;
                background: url('@/assets/img/wallets/qestion_icon.png') no-repeat;
                background-size: 100% 100%;
            }
            .other-title {
                position: relative;
                font-size: 30px;
                color: #ffffff;
                // &.bonus {
                //     @apply flex items-center;
                //     &::before {
                //         display: inline-block;
                //         content: '';
                //         margin-right: 11px;
                //         width: 30px;
                //         height: 30px;
                //         background: url('@/assets/img/wallets/B.png') no-repeat;
                //         background-size: 100% 100%;
                //     }
                // }
            }
            .other-count {
                margin-left: 20px;
                font-size: 36px;
                line-height: 30px;
                color: #eff28b;
            }
        }

        .other-right {
            color: #fffa;
            font-size: 28px;
            line-height: 33px;
            height: 33px;
        }
    }

    .wallet-money-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .wallet-money {
            display: flex;
            align-items: center;
            position: relative;
            border: 1px solid #9e9e9a;
            background-color: #2a2d2e;
            border-radius: 15px;
            padding: 10px 15px;
            width: 64%;
            height: 65px;

            .money-count {
                flex: 1;
                font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
                font-size: 50px;
                align-items: center;
                color: #eff28b;
                line-height: 45px;
            }
        }

        .wallet-select {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 33%;
            height: 65px;
            padding: 10px 13px;
            border: 1px solid rgba(255, 255, 255, 0.35);
            border-radius: 15px;

            background-color: #2a2d2e;
            font-family: MicrosoftYaHei;
            font-size: 28px;
            line-height: 50px;
            color: #ffffff;

            .select-pic {
                width: 37px;
                height: 37px;
                border-radius: 50%;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .select-cur {
                flex: 1;
                margin: 0 8px;
            }
            .select-icon {
                width: 37px;
                height: 37px;
                background: url('@/assets/img/wallets/slect_icon.png') no-repeat left bottom;
                background-size: 100% 100%;
            }
        }
    }
}

.wallet-tip {
    :deep(span) {
        color: #24eb88;
    }
}
</style>
