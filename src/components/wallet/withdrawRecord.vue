<template>
    <div class="record-wrap">
        <div class="record-title">
            <p v-for="(item, index) in tab" :key="index">{{ $t(`${item}`) }}</p>
        </div>
        <div class="record-content">
            <ScrollList ref="scroll" :api="getList" :disableRefresh="false" @onChange="getRecordList">
                <template #default>
                    <template v-if="!isEmpty">
                        <div class="record-item" v-for="(item, index) in recordList" :key="item">
                            <p>{{ dayjs(item._source.creatat).format('YYYY-MM-DD HH:mm:ss') }}</p>
                            <p class="gold_num">{{ item._source.gold }}</p>
                            <p>{{ $t(GCASH_PAYStATE[item._source.state]) }}<div v-if="item._source.state === 0 && !item?.cancel" class="canccle-order" @click="doCancelOrder(item, index)">{{ $t('Cancel') }}</div></p>
                        </div>
                    </template>
                    <van-empty v-else image="//betfugu.com/static/img/playtok/wallet/no_data.png" :description="$t('No_withdrawal_record')"></van-empty>
                </template>
            </ScrollList>
        </div>
    </div>
</template>
<script lang="ts" setup name="withdraw-record">
import ScrollList from '@/components/ScrollList.vue'
import { getPayoutOrders } from '@/api/wallet'
import { GCASH_PAYStATE } from '@/enums'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'

const isEmpty = ref(false)
const scroll = ref(null)
const i18n = useI18n()
const store = useBaseStore()
// 

const getList = async (params) => {
    const { page, pageSize } = params
    try {
        const res = await getPayoutOrders(
            {
                from: (page - 1) * pageSize,
                size: pageSize,
            },
            false
        )
        if (res.code === 200) {
            isEmpty.value = res.data.total.value === 0
            return res.data.hits
        } else {
            return []
        }
    } catch (e) {
        return []
    }
}
const tab = ['Time', 'Amount', 'Status']
const recordList = ref([])
const getRecordList = (list, state) => {
    recordList.value = state.reset ? [...list] : [...recordList.value, ...list]
}
const doCancelOrder = (item, index) => {
    showDialog({
        message: i18n.t('Confirm_cancellation'),
        className: 'common-dialog',
        showCancelButton: true,
        confirmButtonText: i18n.t('Confirm'),
        cancelButtonText: i18n.t('Cancel'),
    }).then(async () => {
       const res =  await store.OrderCancel(item._source.orderid)
       if(res.code === 200) {
            recordList.value[index].cancel =  true
            recordList.value[index]._source.state = 6
       }
    }).catch((e) => {
        console.log(e, 'Cancel')
    })
}
</script>
<style lang="scss" scoped>
.record-title,
.record-item {
    @apply grid-cols-5;
}
.record-item, .record-title {
    line-height: 48px;
    p {
        display: flex;
        align-items: center;
        justify-content: center;
       @apply col-span-2; 


        &:nth-child(2) {
            @apply col-span-1; 
        }
    }

}

.canccle-order {
    height: 40px;
    padding: 0 10px;
    line-height: 40px;
    margin-left: 5px;
    font-size: 20px;
    background-image: linear-gradient(150deg, 
		#ffc44f 0%, 
		#ffb63d 40%, 
		#ffa72a 100%), 
	linear-gradient(
		#41aefe, 
		#41aefe);
        border-radius: 10px;
        color: #fff;
}
</style>
