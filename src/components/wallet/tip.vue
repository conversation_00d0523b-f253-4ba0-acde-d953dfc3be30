<template>
    <van-overlay :show="showTip" :lock-scroll="false" @click="showTip = false" z-index="3001">
        <div class="dialog" @click.stop>
            <div class="title">
                {{ title }}
                <div class="close" @click="showTip = false"></div>
            </div>
            <div class="content" v-html="content"></div>
            <div class="confirm" @click="showTip = false">{{ $t('Confirm') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: () => '',
    },
    content: {
        type: String,
        default: () => '',
    },
})

const showTip = defineModel()
</script>
<style lang="scss" scoped>
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 660px;
    height: 840px;
    padding: 0 28px 60px;
    border-radius: 40px;
    background-color: #2a2d3d;
    font-family: MicrosoftYaHei;
    color: #ffffff;

    transform: translate3d(-50%, -60%, 0);

    .title {
        position: relative;
        margin-bottom: 29px;
        line-height: 99px;
        font-size: 40px;
        text-align: center;

        &::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            width: 620px;
            height: 4px;
            background-color: #ffffff;
            border-radius: 2px;
            opacity: 0.2;

            transform: translateX(-50%);
        }
        .close {
            position: absolute;
            top: 30px;
            right: 20px;
            width: 33px;
            height: 34px;
            background: url('@/assets/img/new-home/close.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .content {
        flex: 1;
        overflow-y: scroll;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 60px;
        letter-spacing: -1px;

        // :deep(span) {
        //     display: block;
        //     margin-bottom: 60px;
        //     color:#24eb88
        // }
    }

    .confirm {
        margin: 20px auto 0;
        width: 440px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        text-align: center;
        font-size: 36px;
        color: #ffffff;
    }
}
</style>
