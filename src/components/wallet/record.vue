<template>
    <Dialog :show="showModal" title="Record" v-bind="$attrs" @close="showModal = false">
        <div class="record">
            <component :is="recordComponent" />
        </div>
    </Dialog>
</template>
<script lang="ts" setup name="record">
import Dialog from '@/components/dialog/Dialog.vue'
import DepositRecord from '@/components/wallet/depositRecord.vue'
import WithdrawRecord from '@/components/wallet/withdrawRecord.vue'
import GameRcord from '@/components/wallet/gameRcord.vue'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    title: {
        type: String,
        default: () => '',
    },
    tab: {
        type: Array,
        default: () => [],
    },
    keys: {
        type: Array,
        default: () => [],
    },
    default: {
        type: Number,
        default: () => 0,
    },
    type: {
        type: Number,
        default: () => 0,
    },
})
const showModal = defineModel()
const comCOnfig = {
    1: DepositRecord,
    2: WithdrawRecord,
    3: GameRcord,
}
const recordComponent = computed(() => {
    return comCOnfig[props.type]
})
</script>
<style lang="scss" scoped>
:deep(.dialog-head) {
    &::after {
        display: none;
    }
}
.record {
    @apply flex flex-col;
    height: 900px;
    padding-bottom: 20px;
    overflow: hidden;

    :deep(.record-wrap) {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    :deep(.record-title),
    :deep(.record-content) {
        @apply grid gap-[40px];
    }
    :deep(.record-title) {
        padding: 0 27px;
        height: 60px;
        line-height: 60px;
        background-color: #444b5e;
        text-align: center;

        p {
            font-size: 26px;
            color: #ffffff;
        }
    }
    :deep(.record-content) {
        flex: 1;
        overflow: auto;
        padding: 0 22px;

        .record-item {
            @apply grid  gap-[40px];

            p {
                padding: 10px 0;
                font-size: 24px;
                font-weight: bold;
                letter-spacing: -1px;
                text-align: center;
                color: #ffffff;
            }
        }
    }
}
:deep(.van-pull-refresh) {
    overflow: inherit;
}
:deep(.van-empty) {
    padding-top: 240px;
    .van-empty__image {
        width: 308px;
        height: 113px;
        opacity: 0.6;
    }
}
</style>
