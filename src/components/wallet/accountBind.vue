<template>
    <div>
        <Dialog v-model="visible" :showLine="false">
            <template #head>
                <div class="bind-title">{{ $t('Bind_Payment_Account') }}</div>
            </template>
            <div v-if="visible" class="bind-form">
                <MethodIcon />
                <van-form ref="bindForm" class="w-full custom-form usdt-form" @failed="handleFailed" @submit="handleSubmit">
                    <template v-for="item in curForm" :key="item.key">
                        <div class="van-tip">{{ $t(item.label) }}:</div>
                        <van-field
                            v-model="cashForm[item.key]"
                            :name="item.key"
                            label=""
                            colon
                            :placeholder="$t(getTip(item))"
                            :rules="getRules(item)"
                            v-bind="
                                item.inputType === 'select'
                                    ? {
                                          readonly: true,
                                          'is-link': true,
                                          'arrow-direction': 'down',
                                          onClick: () => {
                                              selectKey = item.key
                                              actions = item.options
                                              showSheet = true
                                          },
                                      }
                                    : {}
                            "
                        />
                    </template>
                    <div class="flex justify-center submit-wrap">
                        <Button class="submit-btn" type="submit" native-type="submit">{{ $t('Submit') }}</Button>
                    </div>
                </van-form>
            </div>
        </Dialog>
        <van-action-sheet
            v-model:show="showSheet"
            :actions="actions"
            :cancel-text="$t('Cancel')"
            close-on-click-action
            @select="onSelect"
            :z-index="3001"
        />
    </div>
</template>
<script setup lang="ts">
import * as api from '@/api/wallet'
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import MethodIcon from '@/components/wallet/methodIcon.vue'
import { parse } from 'libphonenumber-js'

const visible = defineModel()
const emits = defineEmits(['confirm'])
const store = useBaseStore()
const i18n = useI18n()
const showSheet = ref(false)
const bindForm = ref(null)
const selectKey = ref('')

const map = {
    PHP: 'gcash',
    // USD: 'usdt',
    USD: 'CashApp',
    TRY: 'Papara',
    BRL: 'Pix',
    NGN: 'Palmpay',
    INR: 'BANK',
}

const withType = computed(() => {
    return map[store.wallet?.currency] || 'gcash'
})
const formConfig = computed(() => {
    return {
        PHP: [
            {
                key: 'phone',
                label: 'Account_Name',
                tip: 'Please_enter_account',
                type: 'digit',
                inputType: 'input',
                rules: [{ required: true, pattern: /^0\d{10}$/, message: i18n.t('Please_enter_account') }],
            },
            {
                key: 'firstname',
                label: 'First_Name',
                tip: 'Please_enter_name',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('Please_enter_name') }],
            },
            {
                key: 'lastname',
                label: 'Surname',
                tip: 'Please_enter_lastname',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('Please_enter_lastname') }],
            },
        ],
        USD: [
            // {
            //     key: 'phone',
            //     label: 'USDT_address',
            //     tip: 'USDT_addr_iput_desc_text',
            //     inputType: 'input',
            //     rules: [{ required: true, pattern: /^[A-Za-z\d]{26,}$/, message: i18n.t('Please_enter_account') }],
            // },
            // {
            //     key: 'address',
            //     label: 'USDT_Blockchain',
            //     tip: '',
            //     inputType: 'select',
            //     rules: [{ required: true }],
            //     options: [
            //         {
            //             name: 'TRX',
            //             code: 'TRX',
            //         },
            //         {
            //             name: 'ETH',
            //             code: 'ETH',
            //         },
            //     ],
            //     default: 'TRX',
            // },
            {
                key: 'accName',
                label: 'name',
                tip: 'withdraw_bind_name_info',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('withdraw_bind_name_error') }],
            },
            {
                key: 'bankNo',
                label: 'Account_Name',
                tip: 'withdraw_bind_account_info3',
                inputType: 'input',
                rules: [
                    {
                        required: true,
                        // pattern: /^(?!.{31,})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|[a-zA-Z]+|\+?[0-9]{1,30})$/,
                        maxlength: 30,
                        message: i18n.t('withdraw_bind_account_error'),
                    },
                ],
            },
        ],
        TRY: [
            {
                key: 'accName',
                label: 'name',
                tip: 'withdraw_bind_name_info',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('withdraw_bind_name_error') }],
            },
            {
                key: 'bankNo',
                label: 'Account_Name',
                tip: 'withdraw_bind_account_info1',
                inputType: 'input',
                rules: [{ pattern: /^(?:\d{10}|\d{24})$/, message: i18n.t('withdraw_bind_account_error') }],
            },
        ],
        BRL: [
            {
                key: 'accName',
                label: 'name',
                tip: 'withdraw_bind_name_info',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('withdraw_bind_name_error') }],
            },

            {
                key: 'bankCode',
                label: 'withdraw_bind_way',
                tip: '',
                inputType: 'select',
                rules: [{ required: true }],
                options: [
                    {
                        name: i18n.t('Reg_Mob_phone_No'),
                        code: 'PHONE',
                    },
                    {
                        name: i18n.t('withdraw_bind_cpf'),
                        code: 'CPF',
                    },
                    {
                        name: i18n.t('email'),
                        code: 'EMAIL',
                    },
                ],
                default: 'PHONE',
            },
            {
                key: 'phone',
                label: 'Account_Name',
                // tip: "Please enter  receiver's name",
                tipOptions: {
                    PHONETIP: 'withdraw_bind_phone_info',
                    CPFTIP: 'withdraw_bind_cpf_info',
                    EMAILTIP: 'withdraw_bind_email_info',
                },
                inputType: 'input',
                dynamicType: 1,
                // rules: [{ required: true, message: i18n.t('Please enter the correct form of the name.') }],
                rulesOptions: {
                    PHONERULE: [{ pattern: /^\d{11}$/, message: i18n.t('withdraw_bind_phone_error') }],
                    CPFRULE: [{ required: true, message: i18n.t('withdraw_bind_cpf_error') }],
                    EMAILRULE: [{ required: true, message: i18n.t('withdraw_bind_email_error') }],
                },
                typeKey: 'bankCode',
                isPhone: true,
            },
        ],
        NGN: [
            {
                key: 'accName',
                label: 'name',
                tip: 'withdraw_bind_name_info',
                inputType: 'input',
                rules: [{ pattern: /^[A-Za-z]*$/, message: i18n.t('withdraw_bind_name_error') }],
            },
            {
                key: 'bankNo',
                label: 'Account_Name',
                tip: 'withdraw_bind_account_info2',
                inputType: 'input',
                type: 'digit',
                rules: [{ pattern: /^\d{10}$/, message: i18n.t('withdraw_bind_account_error') }],
            },
        ],
        INR: [
            {
                key: 'accName',
                label: 'Account Name',
                tip: 'Account Name',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('withdraw_bind_name_error') }],
            },
            {
                key: 'phone',
                label: 'Account Number',
                tip: 'Account Number',
                inputType: 'input',
                // type: 'digit',
                rules: [{ required: true, message: i18n.t('withdraw_bind_account_error') }],
            },
            {
                key: 'ifsc',
                label: 'IFSC CODE',
                tip: 'IFSC CODE',
                inputType: 'input',
                rules: [{ required: true, message: i18n.t('withdrawal.INR.withdraw_bind_ifsc_error') }],
            },
        ],
    }
})
const actions = ref([])

const cashForm = ref({})

const curForm = computed(() => {
    return formConfig.value[store.wallet?.currency] || formConfig.value.PHP
})
const initSelect = () => {
    curForm.value.forEach((item) => {
        if (item.inputType === 'select' && !cashForm.value[item.key]) {
            cashForm.value[item.key] = item.default
        }
    })
}
const getTip = (item) => {
    if (item.dynamicType === 1 && !cashForm.value[item.typeKey]) {
        return ''
    }
    return item.dynamicType !== 1 ? item.tip : item.tipOptions[`${cashForm.value[item.typeKey]}TIP`]
}
const getRules = (item) => {
    return item.dynamicType !== 1 ? item.rules : item.rulesOptions[`${cashForm.value[item.typeKey]}RULE`]
}

watch(visible, (val) => {
    val && initSelect()
})

const onSelect = (item) => {
    // @ts-ignore
    cashForm.value[selectKey.value] = item.code
}

const handleFailed = (values) => {
    console.log(values, 'values')
}
const handleSubmit = (values) => {
    // // 验证通过
    // if (store.wallet.currency === 'BRL' && values.bankCode === 'PHONE') {
    //     values.phone = `+55${values.phone}`
    // }
    api.setAccountInfo({
        paytype: withType.value,
        ...values,
    }).then((res) => {
        if (res.code === 200) {
            emits('confirm')
        }
    })
}

const resetForm = () => {
    Object.keys(cashForm.value).forEach((key) => {
        cashForm.value[key] = ''
    })
}
const editAssign = (account) => {
    const config = formConfig.value[store.wallet?.currency]
    config.forEach((item) => {
        const key = item.key
        cashForm.value[key] = account[key]
    })
}

defineExpose({
    editAssign,
    resetForm,
})
</script>
<style lang="scss" scoped>
.bind-title {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 430px;
    // height: 87px;
    // padding: 0 40px;
    // line-height: 70px;
    // background-size: 100% 100%;
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
}
:deep(.dialog) {
    background-image: linear-gradient(#323838, #323838), linear-gradient(#2a2d3b, #2a2d3b);
    background-blend-mode: normal, normal;
    border-radius: 20px;
    font-family: MicrosoftYaHei;
    padding: 10px;
    width: 80%;
    .close-icon {
        top: 50%;
        width: 64px;
        height: 64px;
        border-radius: 10px;
        background-color: rgb(70, 79, 80, 0.08);
        background-image: url('@/assets/img/icon/com_close.svg');
        filter: brightness(0) invert(1);
        transform: translate(0, -50%);
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 50%;
    }
}
.bind-form {
    @apply flex flex-col items-center;
    padding-bottom: 40px;
    padding-top: 30px;
}
.van-form {
    margin-top: 24px;
}
.custom-form {
    padding: 0 20px;

    :deep(.van-cell) {
        position: relative;
    }
    .van-tip {
        font-family: MicrosoftYaHei;
        font-size: 27px;
        color: #fff;
        margin-left: 5px;
    }
    :deep(.van-cell) {
        align-items: center;
        margin: 5px 0 30px;
        height: 78px;
        line-height: 78px;
        padding: 0 20px;
        border-radius: 10px;
        border: 2px solid #394143;
        background-color: #292d2e;
        overflow: visible;

        &::after {
            display: none;
        }

        .van-field__control {
            caret-color: white; /* 将光标颜色设置为白色 */
        }
        .van-field__body {
            position: relative;

            input {
                font-size: 24px;
                font-weight: bold;
                color: #fff;

                &::placeholder {
                    color: #fff5;
                }
            }
        }
        .van-field__error-message {
            display: flex;
            align-items: center;
            position: absolute;
            bottom: 0;
            left: -20px;
            width: 100%;
            line-height: 40px;
            font-size: 20px;
            font-weight: bold;
            color: #ff5688;
            transform: translate3d(0, 90%, 0);
            &::before {
                position: relative;
                top: -1px;
                content: '';
                display: inline-block;
                margin-right: 10px;
                width: 26px;
                height: 21px;
                background: url('@/assets/img/wallets/exclam_mark.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .submit-wrap {
        @apply flex flex-col items-center;
        margin-top: 60px;

        // &::before {
        //     content: '';
        //     width: 640px;
        //     height: 4px;
        //     background-color: #ffffff;
        //     border-radius: 2px;
        //     opacity: 0.2;
        // }
        .submit-btn {
            width: 400px;
            height: 98px;
            line-height: 88px;
            padding: 0;
            background-image: linear-gradient(90deg, #24ee89, #9fe871);
            box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
            background-blend-mode: normal, normal;
            border-radius: 20px;
            font-size: 38px;
            font-weight: 800;
            border: none;
            color: #000;
        }
    }
}
</style>
