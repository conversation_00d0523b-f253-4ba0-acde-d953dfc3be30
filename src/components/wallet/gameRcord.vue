<template>
    <div class="record-wrap">
        <div class="record-title">
            <p v-for="(item, index) in tab" :key="index">{{ $t(`${item}`) }}</p>
        </div>
        <div class="record-content">
            <ScrollList ref="scroll" :api="getList" :disableRefresh="false" @onChange="getRecordList">
                <template #default>
                    <template v-if="!isEmpty">
                        <div class="record-item" v-for="item in recordList" :key="item">
                            <p>{{ dayjs(item._source.SettlementTime).format('YYYY-MM-DD HH:mm') }}</p>
                            <p>{{ item._source.channel && item._source.channel.trim() !== '' ? item._source.channel : item._source.gamechannel }}</p>
                            <!-- <p>{{ item._source.PayoffAmount + item._source.BetAmount > 0 ? $t('Win') : $t('Lose') }}</p> -->
                            <p>{{ formatNumber(item._source.PayoffAmount + item._source.BetAmount) }}</p>
                        </div>
                    </template>
                    <van-empty
                        class="empty-tip"
                        v-else
                        image="//betfugu.com/static/img/playtok/wallet/no_data.png"
                        :description="$t('No_game_record')"
                    ></van-empty>
                </template>
            </ScrollList>
        </div>
    </div>
</template>
<script lang="ts" setup name="game-record">
import ScrollList from '@/components/ScrollList.vue'
import { getGameRecords } from '@/api/wallet'
import dayjs from 'dayjs'
import { formatNumber } from '@/utils/toolsValidate'

const isEmpty = ref(false)
const scroll = ref(null)

const getList = async (params) => {
    const { page, pageSize } = params
    try {
        const res = await getGameRecords(
            {
                from: (page - 1) * pageSize,
                size: pageSize,
            },
            false
        )
        if (res.code === 200) {
            isEmpty.value = res.data.total.value === 0
            return res.data.hits
        } else {
            return []
        }
    } catch (e) {
        return []
    }
}
// ,
const tab = ['Time', 'Game', 'Bet']
const recordList = ref([])
const getRecordList = (list, state) => {
    recordList.value = state.reset ? [...list] : [...recordList.value, ...list]
}
</script>
<style lang="scss" scoped>
.record-title,
.record-item {
    @apply grid-cols-3;
    gap: 5px !important;
}
.record-item {
    line-height: 48px;
    p {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.empty-tip {
    text-align: center;
}
</style>
