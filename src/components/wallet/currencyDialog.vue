<template>
    <Dialog v-model="showModal" :title="$t('Choose_currency')" v-bind="$attrs" @close="showModal = false" z-index="3001" lazy-render>
        <div class="currency-wrap">
            <div class="currency-list">
                <div
                    v-for="(item, index) in store.currencyList"
                    :key="item.currency"
                    :class="['currency-item', { active: idx === index }]"
                    @click="chooseItem(item, index)"
                >
                    <div class="icon"><img :src="item.picture" /></div>
                    <p class="category">{{ item.currency }}</p>
                    <p class="balance">{{ formatNormalNumber(currencyConfig[item.currency] || 0) }}</p>
                </div>
            </div>
            <div class="btn-wrap">
                <Button type="submit" @confirm="onConfirm">{{ $t('Ok') }}</Button>
            </div>
        </div>
    </Dialog>
</template>
<script lang="ts" setup name="record">
import Dialog from '@/components/dialog/Dialog.vue'
import Button from '@/components/Button.vue'
import { getCurrency, setCurrency } from '@/api'
import { useBaseStore } from '@/stores'
import { formatNormalNumber } from '@/utils/toolsValidate'

const store = useBaseStore()

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    type: {
        // 1 确定是 发送请求设置货币， 2 不发送请求返回货币
        type: Number,
        default: () => 1,
    },
    default: {
        type: String,
        default: () => '',
    },
})
const emits = defineEmits(['update:modelValue', 'confirm', 'getData'])
const showModal = defineModel()
const idx = ref(0)
const confList = ref([])
const currencyConfig = ref({})
const chooseItem = (item, index) => {
    idx.value = index
}
const onConfirm = () => {
    if (!~idx.value) {
        return
    }
    const currency = confList.value[idx.value].currency
    if (!currency) {
        return
    }
    try {
        if (props.type === 1) {
            if (props.default !== currency) {
                setCurrency(currency).then((res) => {
                    if (res.code === 200) {
                        store.setWallet(res.data.currency)
                        emits('confirm', res.data.currency)
                    }
                })
            }
        } else {
            emits('confirm', currency)
        }
    } finally {
        showModal.value = false
    }
}
function selectInit() {
    const currency = props.default || store.wallet?.currency || store.currencyList[0].currency
    const chekedIdx = store.currencyList.findIndex((item) => item.currency === currency)
    idx.value = chekedIdx
}
selectInit()
const getList = () => {
    getCurrency().then((res) => {
        if (res.code === 200) {
            const { confs = [], currencys = {} } = res.data
            currencyConfig.value = currencys
            emits('getData', confs)
        }
    })
}

watch(showModal, () => {
    if (showModal.value) {
        selectInit()
        getList()
    }
})
const handleInitial = () => {
    confList.value = store.currencyList
}

onMounted(() => {
    handleInitial()
})
</script>
<style lang="scss" scoped>
.currency-wrap {
    display: flex;
    flex-direction: column;
    height: 850px;
}
.btn-wrap {
    @apply flex align-top justify-center;
    position: relative;
    padding-top: 25px;
    height: 160px;
    text-align: center;

    &::before {
        content: '';
        position: absolute;
        top: 5px;
        left: 50%;
        width: 640px;
        height: 4px;
        background-color: #ffffff;
        border-radius: 2px;
        opacity: 0.2;

        transform: translateX(-50%);
    }

    :deep(.tiktoko.submit) {
        width: 440px;
        height: 88px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        border: none;
    }
}
.currency-list {
    flex: 1;
    padding: 20px 12px 0;
    .currency-item {
        display: flex;
        align-items: center;
        height: 138px;
        padding: 0 8px;
        margin: 10px 0;
        gap: 20px;
        border-radius: 30px;
        border: 8px solid #444b5e;
        font-size: 30px;
        font-weight: bold;
        letter-spacing: -1px;
        color: #fff;
        background-color: #444b5e;

        .icon {
            width: 108px;
        }
        .category {
            flex: 1;
        }
        .balance {
        }
        &.active {
            position: relative;

            border-color: #10c580;

            &::after {
                position: absolute;
                right: -5px;
                bottom: -5px;
                display: inline-block;
                content: '';
                width: 52px;
                height: 52px;
                background: url('@/assets/img/wallets/checked.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    overflow: auto;
}
</style>
