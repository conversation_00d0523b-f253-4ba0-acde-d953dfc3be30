<template>
    <div class="wallet-lamp">
        <Vue3Marquee :animateOnOverflowOnly="true" :delay="1" :duration="10">
            <span v-for="(item, idx) in channel" :key="idx">{{ $t(tip) }}</span>
        </Vue3Marquee>
    </div>
</template>
<script setup lang="ts">
import { Vue3Marquee } from 'vue3-marquee'

defineProps({
    channel: {
        type: Array,
        default: () => [],
    },
    tip: {
        type: String,
        default: () => '',
    },
})
</script>
<style lang="scss" scoped>
.wallet-lamp {
    position: absolute;
    width: 680px;
    padding: 0 20px;
    height: 100px;
    line-height: 100px;
    top: 50%;
    left: 50%;
    background: url('@/assets/img/wallets/lamp_bg.png');
    background-size: 100% 100%;

    transform: translate3d(-50%, -50%, 0);
    overflow: hidden;
    :deep(.vue3-marquee) {
        overflow: hidden;
    }
    span {
        display: inline-block;
        min-width: 640px;
        margin-right: 640px;
        white-space: nowrap;
        color: #fff !important;
        font-size: 25px;
    }
}
</style>
