<template>
    <van-popup class="btip_popup" v-model:show="showTip" :lock-scroll="false" @click="showTip = false" z-index="3001" position="bottom">
        <div class="wallet-bonus" @click.stop>
            <div class="title">
                <span>{{ title }}</span>
                <button class="close" @click="showTip = false">
                    <!-- <img src="@/assets/img/new-home/newsetting/back.svg" alt="x" /> -->
                </button>
            </div>
            <div class="content" v-html="content"></div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: () => '',
    },
    content: {
        type: String,
        default: () => '',
    },
})

const showTip = defineModel<boolean>()
</script>
<style lang="scss" scoped>
.btip_popup {
    background-color: rgb(32, 35, 40);
    border-radius: 20px 20px 0px 0px;

    .wallet-bonus {
        @apply flex flex-col;
        width: 100%;
        min-height: 640px;
        max-height: 1024px;
        padding: 0 28px 10px;
        font-family: MicrosoftYaHei;
        color: #ffffff;

        .title {
            @apply flex;
            position: relative;
            height: 110px;
            font-size: 42px;
            font-weight: 600;
            text-align: center;
            align-items: center;
            justify-content: center;

            :deep(span) {
                line-height: 50px;
                max-width: 530px;
            }

            .close {
                position: absolute;
                right: 10px;
                width: 64px;
                height: 64px;
                border-radius: 10px;
                background-color: rgb(70, 79, 80, 0.08);
                background-image: url('@/assets/img/icon/com_close.svg');
                filter: brightness(0) invert(1);
                background-repeat: no-repeat;
                background-position: center center;
                background-size: 50%;
            }
        }

        .content {
            flex: 1;
            overflow-y: scroll;
            font-size: 28px;
            font-weight: 500;
            font-stretch: normal;
            line-height: 44px;
            // letter-spacing: -1px;

            :deep(span) {
                color: #24eb88;
            }
        }
    }
}
</style>
