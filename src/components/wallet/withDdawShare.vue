<template>
    <div>
        <van-overlay :show="visible" lock-scroll :z-index="301">
            <div class="share-wrap">
                <div ref="sharePic" class="share-pic">
                    <img v-if="!sharePicUrl" src="@/assets/img/wallets/share/share_bg.png" />
                    <img v-else class="create-img" :src="sharePicUrl" />
                    <div v-if="!sharePicUrl" class="pic-overlay">
                        <div :class="['num', { scale: numList.length > 3 }]">
                            <img :src="getImg(store.cureencySymbol())" />
                            <img v-for="(item, index) in numList" :key="index" :src="getImg(item)" />
                        </div>
                        <div class="text-pic"><img :src="getImg(`text${randomInt}`)" /></div>
                        <div class="search-pic"><img src="@/assets/img/wallets/share/search.png" /></div>
                    </div>
                </div>
                <div class="pic-overlay-right">
                    <img class="share-btn" src="@/assets/img/wallets/share/share_btn.png" @click="handleShare" />
                    <img class="share-tip" src="@/assets/img/wallets/share/share_tip.png" />
                </div>
                <div class="share-code"><img src="@/assets/img/wallets/share/save_text.png" /></div>
                <div class="close" @click="visible = false"></div>
            </div>
        </van-overlay>
        <ShareDialog v-model="showShare" :social="['facebook', 'telegram', 'viber']" :config="{ url: sharePicUrl }" />
    </div>
</template>
<script lang="ts" setup>
import ShareDialog from '@/components/dialog/shareDialog.vue'
import html2canvas from 'html2canvas'
import { useBaseStore } from '@/stores'
import { verifyNumberComma } from '@/utils/toolsValidate'
import { uploadPictrue } from '@/api'

const path = '../../assets/img/wallets/share/'
const file = import.meta.glob('../../assets/img/wallets/share/*', { eager: true })
function getRandomIntInclusive(min, max) {
    min = Math.ceil(min)
    max = Math.floor(max)
    return Math.floor(Math.random() * (max - min + 1)) + min
}
const randomInt = ref(1)

const getImg = (name) => {
    return file[path + name + '.png']?.default
}

const props = defineProps({
    num: {
        type: Number,
        default: 0,
    },
})
const numList = computed(() => {
    return verifyNumberComma(props.num).split('')
})
const showShare = ref(false)
const visible = defineModel()
const store = useBaseStore()
const sharePic = ref(null)
const sharePicUrl = ref('')
let fileBlob = null

watch(visible, () => {
    if (visible.value) {
        randomInt.value = getRandomIntInclusive(1, 5)

        nextTick(() => {
            creatImg()
        })
    } else {
        sharePicUrl.value = ''
    }
})
const creatImg = () => {
    html2canvas(sharePic.value, { backgroundColor: null }).then(function (canvas) {
        sharePicUrl.value = canvas.toDataURL()
        canvas.toBlob(
            function (blob) {
                fileBlob = new File([blob], 'a.jpeg', {
                    type: 'image/jpeg',
                    lastModified: Date.now(),
                })
                console.log(fileBlob, 'fileBlob')
            },
            'image/jpeg',
            0.5
        )
    })
}
const handleShare = () => {
    uploadPictrue(fileBlob).then((res) => {
        if (res.code === 200) {
            sharePicUrl.value = res.url
            showShare.value = true
        }
        // else {
        //     showToast('')
        // }
    })
}
</script>
<style lang="scss" scoped>
.share-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 709px;
    // height: 96%;

    transform: translate3d(-50%, -50%, 0);

    // .create-img {
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    // }

    .share-pic {
        position: relative;

        .pic-overlay {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40%;
            .num {
                display: flex;
                img {
                    height: 184px;
                }
                &.scale {
                    transform: scale(0.9);
                }
            }

            .text-pic {
                width: 468px;
                height: 79px;
            }
            .search-pic {
                margin-top: 10px;
                width: 402px;
                height: 65px;
            }
        }
    }
    .share-code {
        position: absolute;
        bottom: 75px;
        left: 0;
        width: 100%;
        height: 63px;
    }
    .pic-overlay-right {
        position: absolute;
        top: 30px;
        right: 18px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .share-btn {
            width: 43px;
        }

        .share-tip {
            margin: 16px 15px 0 0;
            width: 240px;
        }
    }

    .close {
        margin: 17px auto 0;
        width: 57px;
        height: 57px;
        background: url('@/assets/img/wallets/share/shut.png') no-repeat;
        background-size: 100% 100%;
    }
}
</style>
