<template>
    <div class="layout">
        <Header />
        <div class="layout-content">
            <router-view> </router-view>
        </div>
    </div>
</template>
<script lang="ts" setup>
import Header from '@/components/Header.vue'
</script>
<style lang="scss" scoped>
.layout {
    .layout-content {
        height: calc(var(--vh, 1vh) * 100 - var(--header-height)) !important;
        overflow: auto;
    }
}
</style>
