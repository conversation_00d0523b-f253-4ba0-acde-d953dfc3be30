<template>
    <div class="foru-item">
        <img src="@/assets/img/foru/底图.jpg" />
        <div :class="['animate-zone', { active: zoneVisble }]">
            <img class="zone-title" src="@/assets/img/foru/conn.png" />
            <div class="zone-user">
                <img class="user-avatar" />
                <div class="user-name">2223</div>
            </div>
            <div class="zone-money">
                <img src="@/assets/img/foru/p.png" />
                <img src="@/assets/img/foru/1.png" />
                <img src="@/assets/img/foru/2.png" />
                <img src="@/assets/img/foru/3.png" />
                <img src="@/assets/img/foru/4.png" />
                <img src="@/assets/img/foru/5.png" />
                <img src="@/assets/img/foru/6.png" />
                <img src="@/assets/img/foru/7.png" />
                <img src="@/assets/img/foru/8.png" />
                <img src="@/assets/img/foru/9.png" />
                <img src="@/assets/img/foru/0.png" />
            </div>
        </div>

        <div class="foru-float">
            <div v-show="!isExpand" class="foru-people grid grid-cols-4">
                <div v-for="item in 8" :key="item" class="people-detail">
                    <div class="avatar">
                        <div class="coins">999k</div>
                    </div>
                    <p class="user-name">sakjklsj</p>
                </div>
            </div>
            <div :class="['foru-record', { expand: isExpand }]">
                <div class="record-wrap">
                    <div class="record-title grid grid-cols-3">
                        <p>Nickname</p>
                        <p>Won</p>
                        <p>Time</p>
                    </div>
                    <div v-for="item in 8" :key="item" class="record-list grid grid-cols-3">
                        <p>Nickname001</p>
                        <p class="coins">999.99K</p>
                        <p>Time</p>
                    </div>
                </div>
                <div class="expand-btn" @click="handleExpand">Click to view all records</div>
            </div>
            <div v-show="!isExpand" class="coupon">
                <img src="@/assets/img/foru/discount.png" />
            </div>
            <Tool v-show="!isExpand" />
            <div class="foru-foot">
                <div class="foot-img">
                    <img src="@/assets/img/foru/pig.png" />
                </div>
                <div class="foot-content">
                    <p class="foot-title">Fortune Pig</p>
                    <van-text-ellipsis class="foot-des" rows="2" content="This is a gameplay description that can display up to two lines" />
                    <div class="foot-btn">Play</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="foru-item">
import Tool from './tool.vue'
const isExpand = ref(false)
const zoneVisble = ref(false)
const handleExpand = () => {
    isExpand.value = !isExpand.value
}

setTimeout(() => {
    zoneVisble.value = true
}, 5000)
</script>
<style scoped lang="scss">
$footHeight: 270px;
.foru-item {
    @apply relative h-full;
    object-fit: cover;
    background-size: 100%;
    background-repeat: 100% 100%;

    .animate-zone {
        @apply absolute flex flex-col items-center justify-center;
        bottom: 240px;
        width: 100%;
        height: 528px;

        opacity: 0;
        transform: scale(0.1);
        transition: all 0.3s ease;

        .zone-title {
            width: 578px;
            height: 100px;
        }
        .zone-user {
            @apply flex flex-col gap-[9px];
            .user-avatar {
                width: 84px;
                height: 84px;
                border-radius: 50%;
            }
            .user-name {
                font-size: 32px;
                font-weight: bold;
                letter-spacing: -2px;
                color: #c8462d;
            }
        }

        .zone-money {
            @apply flex;
            img {
                height: 44px;
            }
        }

        &.active {
            opacity: 1;
            transform: scale(1);
        }
    }

    .foru-float {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
    .foru-people,
    .foru-record,
    .foru-foot {
        position: absolute;
    }
    .foru-people,
    .foru-record {
        left: 50%;
        width: 700px;
        transform: translateX(-50%);

        .record-title {
            height: 48px;
            border-radius: 30px 30px 0 0;
            background: rgba(50, 36, 38, 0.14);
        }
    }
    .foru-people {
        top: 20px;
        height: 320px;
        padding: 24px 14px;

        .people-detail {
            @apply flex flex-col items-center gap-[9px];

            .avatar {
                position: relative;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: #ccc;

                .coins {
                    position: absolute;
                    top: 0;
                    right: 0;
                    font-size: 24px;
                    color: #ffa72a;
                    transform: translateX(90%);
                }
            }
            .user-name {
                font-size: 18px;
                color: #848484;
            }
        }
    }
    .foru-record {
        @apply flex flex-col;
        top: 346px;
        height: 280px;
        padding: 2px;

        transition: all 0.1s ease;

        .record-wrap {
            flex: 1;
            font-size: 22px;
            color: #848484;
            text-align: center;
            overflow: hidden;

            .record-list {
                margin: 28px 0;
                .coins {
                    color: #ffa72a;
                }
            }
        }
        .expand-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            margin-top: 17px;
            font-size: 22px;
            color: #ffa72a;
            text-align: center;
            text-decoration: underline;
            height: 30px;

            &::after {
                display: inline-block;
                content: '';
                width: 19px;
                height: 18px;
                background: url('@/assets/img/foru/arrow.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        &.expand {
            top: 20px;
            height: 1000px;
            height: calc(var(--vh, 1vh) * 100 - var(--header-height) - $footHeight - var(--footer-height) - 10px) !important;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), linear-gradient(#000000, #000000);
            background-blend-mode: normal, normal;
            border-radius: 30px;
            border: solid 4px rgba(255, 255, 255, 0.2);

            .record-wrap {
                overflow: auto;
            }
        }
    }
    .coupon {
        position: absolute;
        width: 130px;
        height: 109px;
        bottom: calc($footHeight + 18px);
    }
    .foru-foot {
        @apply flex gap-[20px] box-border;
        width: 730px;
        padding: 40px 20px;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0 auto;
        height: $footHeight;
        border-radius: 40px 40px 0 0;
        background-color: rgba(9, 15, 30, 0.7);

        .foot-img {
            width: 220px;
            height: 200px;
        }
        .foot-content {
            flex: 1;
            color: #fff;

            .foot-title {
                font-weight: bold;
                font-size: 36px;
            }
            .foot-des {
                // margin: 16px 0 23px;
                font-size: 24px;
                // line-height: 40px;
            }
            .foot-btn {
                width: 440px;
                height: 76px;
                line-height: 76px;
                margin: 13px auto 0;
                background: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
                border-radius: 38px;
                font-size: 36px;
                font-weight: bold;
                text-align: center;
            }
        }
    }
}
</style>
