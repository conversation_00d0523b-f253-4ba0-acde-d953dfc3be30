<template>
    <van-popover class="contry-pop" v-model:show="visible" :actions="actions" @select="onSelect" :offset="[0, 30]">
        <template #reference>
            <div :class="['coutry', { active: visible }]" style="color: rgba(255, 255, 255, 0.4)">+ {{ visibleCode }}</div>
        </template>
    </van-popover>
</template>
<script setup lang="ts" name="county-code">
import { COUNTRY_PHONE_CONFIG, CURRENCY_LIST } from '@/enums'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
const store = useBaseStore()

const { userInfo } = storeToRefs(useBaseStore())

const emits = defineEmits(['countryCodeChange'])
const actions = ref([])

const code = ref('')
const visibleCode = ref('')

const visible = ref(false)

const onSelect = (record) => {
    code.value = record.value
    visibleCode.value = record.code
    emits('countryCodeChange', record.value)
}

const initactions = () => {
    let country = 'PH'
    if (store.wallet?.currency) {
        country = CURRENCY_LIST.find((item) => item.currency === store.wallet?.currency).country
    }

    const idx = COUNTRY_PHONE_CONFIG.findIndex((item) => item.value === country)
    return COUNTRY_PHONE_CONFIG.splice(idx, 1)
        .concat(COUNTRY_PHONE_CONFIG)
        .map((item) => ({ ...item, text: item.code }))
}

onMounted(() => {
    actions.value = initactions()
    const idx = actions.value.findIndex((item) => item.value === userInfo.value?.country)
    onSelect(actions.value[idx === -1 ? 0 : idx])
})
</script>
<style scoped lang="scss">
.coutry {
    @apply flex justify-center items-center gap-[10px];
    position: relative;
    width: 100%;
    height: 100%;

    // &::after {
    //     position: absolute;
    //     right: 28px;
    //     display: inline-block;
    //     content: '';
    //     width: 24px;
    //     height: 20px;
    //     background: url('@/assets/img/login/triangle.png') no-repeat;
    //     background-size: 100% 100%;
    // }

    .code-icon {
        width: 120px;
        text-align: center;
        text-indent: 30px;
    }
    &.active {
        &::after {
            transform: rotateX(180deg);
        }
    }

    .code-list {
        position: absolute;
        top: 110px;
        min-height: 200px;
        max-height: 300px;
        overflow: auto;
        width: 100%;
        background: #fff;
        font-family: MicrosoftYaHei-Bold;
        z-index: 10;

        .code-item {
            line-height: 50px;
            font-size: 26px;
            font-weight: bold;
            color: #000;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 1);

            &:active {
                background-color: #ccc;
            }
        }
    }
    .coutry-input {
        width: 100%;
        height: 100%;
        padding-right: 30px;
        background-color: rgba(0, 0, 0, 0);
    }
    &::after {
        pointer-events: none;
        display: inline-block;
        content: '';
        width: 20px;
        height: 12px;
        background: url('@/assets/img/login/arrow_down.png') no-repeat;
        background-size: 100% 100%;
    }
}
.contry-pop {
    :deep(.van-popover__content) {
        max-height: 400px;
        overflow: auto;
    }
}
</style>
<style lang="scss">
.login .van-popover__wrapper {
    display: inline-block;
    width: 100%;
    height: 100%;
}
</style>
