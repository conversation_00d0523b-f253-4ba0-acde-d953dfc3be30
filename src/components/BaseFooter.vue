<template>
    <div ref="footDom" class="footer" :class="{ isWhite }">
        <div
            :class="['l-button', { active: currentTab === index }]"
            v-for="(item, index) in computedTabMenu"
            :key="item.title"
            @click="tab(item, index)"
        >
            <div class="footer-tab">
                <img class="footer-icon" :src="getImg(item, currentTab === index)" />
                <span class="footer-name" :class="{ active: currentTab === 2 }">{{ $t(item.title) }}</span>
            </div>
            <transition name="grow">
                <div class="grow-line" v-show="currentTab === index"></div>
            </transition>
        </div>
    </div>
</template>
<script setup lang="ts" name="BaseFooter">
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'

const path = '../assets/img/footer-bar/'
const file = import.meta.glob('../assets/img/footer-bar/*', { eager: true })
import { Log } from '@/api/log'

const i18n = useI18n()
const store = useBaseStore()
const jackpotStore = useJackpotStore()
const { showJackpot } = storeToRefs(jackpotStore)

const props = defineProps({
    initTab: {
        type: Number,
        default: () => 0,
    },
    isWhite: {
        type: Boolean,
        default: () => false,
    },
})
const router = useRouter()
const isRefresh = reactive<{
    [key: string]: boolean
    isRefresh1: boolean
    isRefresh2: boolean
}>({
    isRefresh1: false,
    isRefresh2: false,
})
// 当前显示的tab记录
const currentTab = ref(props.initTab)
const footDom = ref(null)
// tab数据
const jackpotMenuItem = {
    title: 'Jackpot',
    path: '/jackpot',
    icon: 'jackpot',
}

const reelsMenuItem = {
    title: 'Home_Reels',
    path: '/video',
    icon: 'video',
}

const baseTabMenu = [
    {
        title: 'Home_page',
        path: '/',
        icon: 'home',
        isFresh: true,
    },
    // 第二项会根据 showJackpot 动态变化
    jackpotMenuItem,
    {
        title: 'earn_money',
        path: '/earnCash',
        icon: 'earncash',
    },
    {
        title: 'Home_Wallets',
        path: '/wallets',
        icon: 'wallets',
    },
    {
        title: 'Home_events',
        path: '/events',
        icon: 'events',
    },
]

// 计算属性：根据showJackpot值动态生成菜单
const computedTabMenu = computed(() => {
    const menu = [...baseTabMenu]
    // 如果showJackpot为false，则显示Reels菜单项
    // if (!showJackpot.value) {
    //     menu[1] = reelsMenuItem
    // }
    return menu
})

// 跳转tab
const tab = (item, index) => {
    Log({
        event: `tab_${i18n.t(item.title)}`,
    })
    // 如果有刷新功能，并且是点击的当前tab
    if (item.isFresh && currentTab.value === index) {
        refresh(index)
    } else {
        if (item.path === '/earnCash') {
            Log({
                event: 'tab_earn',
            })
        }
        router.push(item.path)
    }
}
// 刷新页面
const refresh = (index) => {
    isRefresh['isRefresh' + index] = !isRefresh['isRefresh' + index]
    setTimeout(() => {
        isRefresh['isRefresh' + index] = !isRefresh['isRefresh' + index]
    }, 2000)
}
const getImg = (item, active = false) => {
    const imgPath = path + `${active ? item.icon + '_active' : item.icon}.png`
    const imgModule = file[imgPath] as any
    return imgModule?.default || ''
}
const getFootReact = () => {
    return footDom.value.getBoundingClientRect()
}
defineExpose({
    getFootReact,
})
</script>
<style scopes lang="scss">
.footer {
    font-size: 25px;
    position: fixed;
    left: 0;
    width: 100%;
    height: var(--footer-height);
    border-top: 2px solid #3d3b38;
    z-index: 100;
    //不用bottom：0是因为，在进行页面切换的时候，vue的transition
    // 会使footer的bottom：0失效，不能准确定位
    // top: calc(var(--vh, 1vh) * 100 - var(--footer-height));
    // background: var(--footer-color);
    bottom: 0px;
    display: flex;
    background-image: linear-gradient(#242833, #242833), linear-gradient(#2a2c3c, #2a2c3c);
    background-blend-mode: normal, normal;
    border-radius: 20px 20px 0px 0px;
    border: solid 2px #303446;

    &.isWhite {
        background: white !important;
        color: #000 !important;
    }

    .l-button {
        flex: 1;
        padding-top: 30px;
        position: relative;
        .footer-tab {
            @apply flex flex-col justify-center items-center;
            position: relative;
        }
        .footer-icon {
            width: 50px;
            height: 50px;
        }
        .footer-name {
            font-size: 20px;
            margin-top: 11px;
            font-weight: 1000;
            color: #668292;
        }
        &:nth-child(4) {
            .footer-name {
                margin-top: 14px;
            }
        }

        &.active {
            .footer-name {
                color: #fff;
            }
        }
    }
}
</style>
