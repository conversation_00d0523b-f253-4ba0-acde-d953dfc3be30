<template>
    <div class="lang-choose" :data-theme="theme">
        <div class="lang-list">
            <div
                :class="['lang-item', { active: checked === item.value }]"
                v-for="item in store.languageList"
                :key="item.value"
                @click="changeLang(item)"
            >
                <div class="lang-text">{{ item.show }}</div>
                <div class="lang-checked"></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="langChoose">
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'

const i18n = useI18n()
const store = useBaseStore()

defineProps({
    theme: {
        type: String,
        default: () => 'light',
    },
})
const emits = defineEmits(['on-select'])
const checked = ref(i18n.locale.value)
const changeLang = (itd) => {
    const lang = itd.value
    checked.value = lang
    emits('on-select', lang)
}
</script>

<style scoped lang="scss">
.lang-choose {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
}

.lang-list {
    padding-top: 10px;
    .lang-item {
        position: relative;
        // box-sizing: border-box;
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        // height: 100px;
        // padding: 0 31px 0 56px;
        // line-height: 100px;
        // font-family: MicrosoftYaHei;
        // font-size: 30px;
        // font-weight: bold;
        // color: #6d6d6d;
        // background-color: #f4f4f2;
        // overflow: hidden;

        // &:nth-child(even) {
        //     background: #fff;
        // }
        .lang-checked {
            position: absolute;
            right: -4px;
            bottom: -4px;
            display: none;
            width: 51px;
            height: 51px;
            background: url('@/assets/img/wallets/checked.png') no-repeat;
            background-size: 100% 100%;
        }
        &.active {
            border-color: #10c580;

            .lang-checked {
                display: block;
            }
        }
        // width: 660px;
        margin: 10px 0;
        height: 120px;
        line-height: 120px;
        padding: 0 20px;
        color: #fff;
        font-size: 30px;
        font-weight: bold;
        letter-spacing: -1px;

        border-radius: 30px;
        border: solid 8px #444b5e;
        background-color: #444b5e;
    }
}
</style>
