<template>
    <div class="cooperation">
        <div class="latest">
            <p class="cooperation-title">{{ $t('lastest_bet_bigwins') }}</p>
            <Latest />
        </div>
        <!--div class="community">
            <p class="cooperation-title">{{ $t('Community') }}</p>
            <div class="community-list">
                <img class="community-img" v-lazy="FB" @click="openMedia('https://www.facebook.com/BetFugu1')" />
                <img class="community-img" v-lazy="TG" @click="openMedia('https://t.me/BetFugu1')" />
                <img class="community-img" v-lazy="TK" @click="openMedia('https://www.tiktok.com/@BetFugugame?lang=en')" />
            </div>
        </div-->
        <div class="partner">
            <p class="cooperation-title">{{ $t('partner_game_tech') }}</p>
            <div class="community-list flex-wrap">
                <img :class="['parter-img', item]" v-for="item in partner" :key="item" v-lazy="getImg(`${item}.png`)" />
            </div>
        </div>
        <div class="license">
            <p class="cooperation-title">{{ $t('licensing') }}</p>
            <div class="community-list">
                <img v-lazy="GC" />
                <img v-lazy="Mga" />
            </div>
        </div>
        <div class="pt">
            <div class="pt-icon"></div>
            <div class="pt-text">
                <p class="cooperation-title">{{ $t('BetFugu_info1') }}</p>
            </div>
        </div>
        <div class="year">
            <img src="@/assets/img/new-home/21years.png" />
        </div>
        <langugeSelect />
    </div>
</template>
<script setup lang="ts">
import { hydrateOnIdle } from 'vue'
import langugeSelect from '@/components/newHome/langugeSelect.vue'
import FB from '@/assets/img/home/<USER>'
import TG from '@/assets/img/home/<USER>'
import TK from '@/assets/img/home/<USER>'
import GC from '@/assets/img/new-home/GC.png'
import Mga from '@/assets/img/new-home/mga.png'
const Latest = defineAsyncComponent({
    loader: () => import('@/components/newHome/latest.vue'),
    hydrate: hydrateOnIdle(/* 传递可选的最大超时 */),
})

const path = '../../assets/img/new-home/partner/'
const file = import.meta.glob('../../assets/img/new-home/partner/*', { eager: true })

const getImg = (name) => {
    return file[path + name].default
}

const partner = ['jili', 'et', 'ne', 'jdb', 'pg', 'rtg', 'ta', 'fc', 'rt', 'pp']
const openMedia = (url) => {
    window.open(url)
}
</script>
<style lang="scss" scoped>
.cooperation {
    margin-top: 20px;
    padding: 0 16px 50px 0;

    .cooperation-title {
        @apply flex items-center gap-[10px];
        position: relative;
        line-height: 80px;
        font-size: 30px;
        font-family: MicrosoftYaHei;
        color: #ffffff;

        &::before {
            display: inline-block;
            content: '';
            width: 24px;
            height: 24px;
            background: url('@/assets/img/new-home/green-p.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .community-list {
        display: flex;
        padding-bottom: 50px;

        .community-img {
            width: 220px;
            height: 100px;
            border-radius: 20px;
            margin-right: 20px;
        }
        .parter-img {
            margin-bottom: 10px;
            width: 220px;

            &.fb,
            &.gg {
                width: 344px;
            }
        }
        .pay-img {
            height: 60px;
        }
    }
    .license {
        // margin-bottom: 30px;
    }
    .media {
        margin-bottom: 50px;

        img {
            width: 500px;
        }
    }

    .pay {
        .community-list {
            @apply gap-[40px];
        }
        .pay-text {
            padding-bottom: 40px;
        }
    }
    .pay-text {
        // word-wrap: break-word;
        // word-break: break-all;
        @apply flex flex-wrap;
        line-height: 60px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        font-size: 30px;
        color: #668292;
    }
    .cunrrent {
        img {
            width: 500px;
        }
    }
    .license {
        img {
            width: 344px;
            height: 80px;
        }
    }
    .year {
        margin-top: 20px;
        margin-bottom: 80px;
        img {
            margin: 0 auto;
            width: 454px;
            height: 54px;
        }
    }
    .pt {
        .pt-icon {
            margin-bottom: 20px;
            width: 283px;
            height: 73px;
            background: url('@/assets/img/home/<USER>') no-repeat;
            background-size: 100% 100%;
        }
        .pt-text {
            font-size: 24px;
            .cooperation-title::before {
                display: none;
            }
            .cooperation-title {
                margin-bottom: 50px;
                line-height: 40px;
                color: #668292;
            }
        }
    }
}
</style>
