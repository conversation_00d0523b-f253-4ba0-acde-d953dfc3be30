.title {
    padding-bottom: 12px;
    font-family: MicrosoftYaHei;
    font-size: 30px;
    color: #ffffff;
}
.menu-wrap {
    //margin-bottom: 20px;
    padding: 0 20px;
    border-radius: 20px;
   background-color: rgb(50, 55, 56, 1);

    .menu-item {
        @apply flex items-center;
        position: relative;
        height: 89px;

        &::after {
            content: '';
            display: none;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background-color: #2a2d3d;
            border-radius: 1px;
        }

        &:last-child {
            &::after {
                display: none;
            }
        }

        .menu-icon {
            margin-right: 17px;
            width: 48px;
            height: 48px;
        }
        .menu-name {
            @apply flex justify-between items-center;
            flex: 1;
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;

            &::after {
                display: inline-block;
                content: '';
                width: 16px;
                height: 26px;

                background: url('@/assets/img/new-home/menu-row.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
}
