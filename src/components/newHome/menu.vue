<template>
    <van-popup class="menu-popup" v-model:show="store.showMenu" position="right">
        <div class="menu">
            <div class="menu-title">
                <!-- <div class="menu-close" @click="store.showMenu = false"></div> -->
                <button class="menu-close" @click="store.showMenu = false">
                    <img src="@/assets/img/new-home/newsetting/back.svg" alt="x" />
                </button>
            </div>
            <div class="menu-content">
                <MenuMix />
                <!-- <MenuGame /> -->
                <MenuSet />
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import MenuMix from './menuMix.vue'
// import MenuGame from './menuGame.vue'
import MenuSet from './menuSet.vue'
import { Log } from '@/api/log'

const store = useBaseStore()

watch(
    () => store.showMenu,
    (val) => {
        if (val) {
            Log({
                event: 'menu_visit',
            })
        }
    }
)

const getNoticeNumber = () => {
    store.getMsgUnread()
}
const getVipInfo = async (loading = true, tip = true) => {
    try {
        const info = await store.getVipInfo(loading, tip)
        if (info.code === 200) {
            store.setvipInfo(info)
        }
    } catch (e) {
        console.log(e)
    }
}
onActivated(() => {
    getVipInfo(false, false)
    getNoticeNumber()
})
</script>
<style lang="scss" scoped>
.menu-popup {
    background-color: rgb(35 38 38);
    width: 100%;
    height: 100vh;
}

.menu {
    width: 100%;
    height: 100vh;
    background: url('@/assets/img/new-home/newsetting/menubg.avif') no-repeat center;
    background-size: 120% auto;
    background-position: -120px 0;
    background-color: rgb(35 38 38);

    .menu-title {
        @apply flex justify-center relative;
        height: var(--menu-header);
        align-items: flex-end;
        padding-bottom: 30px;
        .menu-close {
            position: absolute;
            left: 40px;
            width: 55px;
            height: 55px;
            border-radius: 10px;
            background-color: #464f50;
            color: #fff;
            background-size: 100% 100%;
            justify-content: center;
            align-items: center;
            display: flex;
            :deep(img) {
                width: 100%;
                height: 60%;
            }
        }
    }
    .menu-content {
        padding: 30px;
        height: calc(var(--vh, 1vh) * 100 - max(var(--menu-header), 110px));
        overflow-y: scroll;
    }
}
</style>
