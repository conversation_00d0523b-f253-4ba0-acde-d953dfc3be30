<template>
    <div class="latest-wrap">
        <div class="latest-tab">
            <div v-for="item in tab" :key="item.type" :class="['tab', { active: item.type === active }]" @click="changeTab(item)">
                {{ $t(item.name) }}
            </div>
        </div>
        <div class="content">
            <div class="content-title">
                <div v-for="name in title" :key="name">{{ $t(name) }}</div>
            </div>
            <div class="content-list">
                <template v-if="active === 1">
                    <swiper
                        v-if="tabData.length"
                        :direction="'vertical'"
                        :slidesPerView="10"
                        :modules="modules"
                        :autoplay="{ delay: 3000 }"
                        :loop="true"
                        class="mySwiper"
                    >
                        <swiper-slide v-for="(item, index) in tabData" :key="index">
                            <div class="item">
                                <div class="game">
                                    <van-text-ellipsis :content="item.gamename" />
                                </div>
                                <div class="name">{{ encryptUsername(decodeName(item.nickname)) }}</div>
                                <div :class="['gold', { active: item.payoff }]">
                                    {{ item.payoff ? '+' : '' }} {{ store.cureencySymbol() }} {{ item.payoff }}
                                </div>
                            </div>
                        </swiper-slide>
                    </swiper>
                </template>

                <template v-else>
                    <template v-if="tabData.length">
                        <div class="item" v-for="(item, index) in tabData" :key="index">
                            <div class="game">
                                <van-text-ellipsis :content="item.gamename" />
                            </div>
                            <div class="name">{{ encryptUsername(decodeName(item.nickname)) }}</div>
                            <div :class="['gold', { active: item.scores }]">
                                {{ item.scores && active === 2 ? '+' : '' }}{{ active === 2 ? store.cureencySymbol() : '' }} {{ item.scores
                                }}{{ item.scores && active === 3 ? 'x' : '' }}
                            </div>
                        </div>
                    </template>
                    <div v-else class="no-data"><img src="@/assets/img/new-home/oops.png" /></div>
                </template>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { encryptUsername, decodeName } from '@/utils'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { FreeMode, Autoplay } from 'swiper/modules'
import { useBaseStore } from '@/stores'
import { getLastestBet, getHighRoller, getHighMultiplier } from '@/api/home'

const map = {
    1: getLastestBet,
    2: getHighRoller,
    3: getHighMultiplier,
}
const modules = [FreeMode, Autoplay]
const active = ref(1)
const store = useBaseStore()

watch(
    () => store.wallet.currency,
    () => {
        data[2] = []
        data[3] = []
    }
)

const tab = ref([
    {
        type: 1,
        name: 'lastest_bet',
    },
    {
        type: 2,
        name: 'high_roller',
    },
    {
        type: 3,
        name: 'higmutiplier',
    },
])
const title = ['Game', 'Player', 'Profit']

const data = reactive({
    1: [],
    2: [],
    3: [],
})
const tabData = computed(() => {
    return data[active.value]
})
const changeTab = (item) => {
    active.value = item.type
    getList()
}
const getList = () => {
    const type = active.value
    if ([2, 3].includes(type) && data[type].length) {
        return
    }
    const getData = map[type]
    getData().then((res) => {
        if (res.code === 200) {
            data[type] = [2, 3].includes(type) ? res.rank : res.data
        }
    })
}
onActivated(() => {
    getList()
})
onMounted(() => {
    getList()
})
</script>
<style lang="scss" scoped>
.latest-wrap {
    margin-bottom: 30px;
    font-family: 'Microsoft YaHei';
    .latest-tab {
        @apply grid grid-cols-3;
        width: 700px;
        height: 105px;
        border-radius: 30px;
        background: #1a1e2e;

        .tab {
            @apply flex justify-center items-center;
            color: #788094;
            font-size: 28px;
            font-weight: 400;
            white-space: normal; // 允许换行
            word-break: break-word; // 在中文或长英文断词处自动换行
            text-align: center; // 多行居中对齐
            padding: 10px 20px; // 适当内边距让内容有呼吸感

            &.active {
                border-radius: 30px;
                background: #444b5e;
                color: #ffd145;
            }
        }
    }
    .content {
        margin-top: 26px;
        width: 700px;
        height: 982px;
        border-radius: 30px;
        background: #1a1e2e;

        .content-title {
            @apply grid grid-cols-3;
            font-size: 28px;
            font-weight: 400;
            color: #788094;
            border-radius: 28px 30px 0px 0px;
            background: #2a2d3d;

            div {
                @apply flex justify-center items-center;
                height: 81px;
            }
        }
        .content-list {
            height: calc(100% - 81px);
            padding-top: 46px;
            .item {
                @apply grid grid-cols-3;

                & > div {
                    @apply flex justify-center items-center;
                    margin-bottom: 20px;

                    height: 64px;
                    font-size: 24px;
                    font-weight: 400;
                    color: #fff;
                }
                .game {
                    justify-self: flex-start;
                    padding-left: 30px;
                    &::before {
                        display: inline-block;
                        content: '';
                        margin-right: 13px;
                        width: 25px;
                        height: 24px;
                        background: url('@/assets/img/new-home/card.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }
                .gold {
                    color: #788094;
                    &.active {
                        color: #00ff62;
                    }
                }
            }
            .no-data {
                @apply flex justify-center items-center;
                height: 500px;
                img {
                    width: 406px;
                    height: 186px;
                }
            }
        }
    }
}
.swiper {
    pointer-events: none;
}
.swiper,
.swiper-wrapper {
    width: 100%;
    height: 100%;
}
.swiper-slide {
    width: 100%;
    .item {
        width: 100%;
    }
}
</style>
