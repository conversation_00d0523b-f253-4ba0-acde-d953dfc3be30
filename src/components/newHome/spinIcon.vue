<template>
    <van-badge v-if="showicon" class="sign" :content="spinnums" :offset="[-8, -3]" :show-zero="false">
        <div class="sign-btn" @click="handleClick"></div>
    </van-badge>
</template>

<script lang="ts" setup>
import eventBus from '@/utils/bus'
import { useBaseStore } from '@/stores'

const router = useRouter()
const showicon = ref(false)
const store = useBaseStore()
const spinnums = ref(0)

watch(
    () => store.token,
    (val, oldVal) => {
        if (val != oldVal) {
            handlePoint()
        }
    }
)

const handleClick = () => {
    if (!store.token) {
        router.push('/login')
        return
    }
    console.log('handleClick')
    eventBus.emit('showLuckySpin', true)
}
const handleSign = async (param: { show: boolean; spintimes?: number }) => {
    console.log('handleSpinIcon-show', param)
    showicon.value = param.show
    spinnums.value = param.spintimes

    console.log('spinnums-------', spinnums.value)
}
const handlePoint = () => {
    store
        .getSignEvent(
            {
                id: 'totalrechargetable',
            },
            false,
            false
        )
        .then((res) => {
            if (res.code === 200 && res.acticity.status === 1) {
                let rotations = res.acticity.rotations
                let times = 0
                for (let i = 0; i < rotations.length; i++) {
                    times += rotations[i].times
                }
                console.log('times', times)

                handleSign({ show: true, spintimes: times })
            } else {
                handleSign({ show: false })
            }
        })
}

onMounted(() => {
    handlePoint()
})

onBeforeMount(() => {
    eventBus.on('totalrechargetable', handleSign)
    // eventBus.on('checkPoint', handlePoint)
})
onBeforeUnmount(() => {
    eventBus.off('totalrechargetable', handleSign)
    // eventBus.off('checkPoint', handlePoint)
})
</script>
<style lang="scss" scoped>
.sign {
    position: absolute;
    bottom: 150px;
    right: 20px;
    width: 93px;
    height: 93px;

    .sign-btn {
        &::before {
            position: absolute;
            left: 50%;
            top: 0px;
            content: '';
            width: 155px;
            height: 155px;
            background: url('@/assets/img/luckyspin/spinicon.avif') no-repeat;
            background-size: 100% 100%;
            transform: translate3d(-60%, -35%, 0);
            // transform-origin: left center;
            // animation: rotate 3s ease-in-out infinite alternate;
        }
    }
}
</style>
