<template>
    <van-popup class="channel-pop" v-model:show="show" position="bottom" close-on-popstate round :z-index="3001" @closed="handleClose">
        <div class="gift-centers">
            <div class="title">
                <div class="text">{{ $t('gift_center') }}</div>
                <div class="close" @click="handleClose"></div>
            </div>

            <template v-if="Object.keys(freeTimes).length">
                <div class="game">
                    <div
                        v-for="(item, key) in freeTimes"
                        :key="key + item"
                        :class="['game-item', { active: key === activeIdx }]"
                        @click="handleClick(configs[key], key)"
                    >
                        <div class="item-top">
                            <img v-lazy="configs[key].img" />
                            <div class="game-num">{{ item || 0 }}</div>
                        </div>
                        <div class="item-bot">{{ $t(configs[key].name) }}</div>
                    </div>
                </div>
                <div class="oprate">
                    <div class="tip-wrap" v-if="activeIdx">
                        <div
                            class="tip-title"
                            v-html="
                                $t(activeItem?.desc, {
                                    current_type: store.cureencySymbol(),
                                    number: activeItem?.worth,
                                })
                            "
                        ></div>
                        <div
                            class="tip"
                            v-html="
                                $t(activeItem?.subDesc, {
                                    current_type: store.cureencySymbol(),
                                    number: activeItem?.worth,
                                })
                            "
                        ></div>
                    </div>
                    <div class="btn" @click="goGame">Play</div>
                </div>
            </template>
            <div v-else class="no-data"><img src="@/assets/img/new-home/oops.png" /></div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { getfreepackage } from '@/api/home'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { Log } from '@/api/log'

const show = defineModel()
const router = useRouter()
const store = useBaseStore()

const activeIdx = ref('')
const configs = ref({})
const freeTimes = ref({})
const activeItem = ref({})

watch(show, () => {
    getPackge(false)
})

const handleClose = () => {
    show.value = false
}
const getPackge = (loading = true) => {
    getfreepackage(loading).then((res) => {
        if (res.code === 200) {
            configs.value = res.confs
            const freePackage = res.freePackage
            freeTimes.value = {}
            Object.keys(freePackage).forEach((key) => {
                const num = freePackage[key]
                if (num) {
                    freeTimes.value[key] = num
                }
            })
            if (!activeIdx.value) {
                const keys = Object.keys(freeTimes.value)
                if (keys.length) {
                    const key = keys[0]
                    handleClick(res.confs[key], key)
                }
            }
        }
    })
}
const handleClick = (item, index) => {
    if (activeIdx.value === index) {
        return
    }
    activeIdx.value = index
    activeItem.value = item
    Log({
        event: 'free_click',
        content: {
            id: item.id,
        },
    })
}
const goGame = () => {
    const url = activeItem.value.jump + `&sid=${store.token}&id=${activeItem.value.id}&currency=${store.wallet?.currency}`
    router.push({
        path: '/iframe',
        query: {
            u: encodeURIComponent(url),
            from: 'home',
            isFree: 'true',
        },
    })
    getPackge(false)
    handleClose()
    Log({
        event: 'free_Play',
        content: {
            id: activeItem.value.id,
        },
    })
}
// onActivated(() => {
//     getPackge()
// })
// onBeforeMount(() => {
//     getPackge()
// })
</script>
<style lang="scss" scoped>
.gift-centers {
    min-height: 416px;

    font-family: MicrosoftYaHei;
    color: #fff;
    background: linear-gradient(#1c1b28, #1c1b28), linear-gradient(#2a2d3d, #2a2d3d);

    .title {
        @apply flex justify-between items-center;
        position: relative;
        height: 146px;
        padding: 0 22px;
        background-color: #2a2d3e;

        .text {
            @apply flex items-center;
            font-weight: bold;
            font-size: 40px;

            &::before {
                position: relative;
                top: -8px;
                display: inline-block;
                content: '';
                margin-right: 12px;
                width: 48px;
                height: 49px;
                background: url('@/assets/img/new-home/gift-icon.png') no-repeat;
                background-size: 100% 100%;
            }
        }

        .close {
            width: 54px;
            height: 54px;
            background: url('@/assets/img/close.png') no-repeat left top;
            background-size: 27px 27px;
        }
    }
    .game {
        @apply grid grid-cols-4 gap-[19px];
        padding: 0 22px 32px;
        background-color: #2a2d3e;

        .game-item {
            .item-top {
                position: relative;
                width: 163px;
                height: 163px;
                background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#2a2d3d, #2a2d3d);
                background-blend-mode: normal, normal;
                border-radius: 40px;
                border: solid 4px #2a2d3e;

                .game-num {
                    position: absolute;
                    right: 24px;
                    bottom: 12px;
                    font-size: 24px;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
            .item-bot {
                margin-top: 30px;
                font-size: 24px;
                text-align: center;
            }

            &.active {
                .item-top {
                    background-image: linear-gradient(rgba(50, 236, 135, 0.27), rgba(50, 236, 135, 0.27)), linear-gradient(#2a2d3d, #2a2d3d);
                    border-color: #32ed87;
                }
            }
        }
    }

    .oprate {
        padding: 55px 22px;
        @apply flex items-center;
        .tip-wrap {
            flex: 1;

            .tip-title {
                margin-bottom: 16px;
                font-size: 30px;
            }

            .tip {
                font-size: 22px;
                color: #668292;
                :deep(span) {
                    color: #32ec87;
                }
            }
        }

        .btn {
            @apply flex items-center justify-center;
            margin-left: 94px;
            width: 175px;
            height: 88px;
            background-image: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#8b0500, #8b0500);
            background-blend-mode: normal, normal;
            border-radius: 44px;
            font-size: 36px;
            color: #000;
        }
    }
    .no-data {
        @apply flex justify-center items-center;
        height: 500px;
        background-color: #2a2d3e;
        img {
            width: 406px;
            height: 186px;
        }
    }
}
</style>
