<template>
    <Dialog v-model="showed" title="Language" v-bind="$attrs" @close="showed = false" z-index="3001">
        <div class="wrap">
            <div class="lang-wrap">
                <LangChoose @on-select="changeLanguage" theme="light" />
            </div>

            <div class="btn-wrap">
                <Button type="submit" @confirm="onConfirm">{{ $t('Ok') }}</Button>
            </div>
        </div>
    </Dialog>
</template>
<script setup lang="ts" name="initDialog">
import LangChoose from '@/components/language/langChoose.vue'
import Dialog from '@/components/dialog/Dialog.vue'
import Button from '@/components/Button.vue'
import { useI18n } from 'vue-i18n'
const i18n = useI18n()

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
})
const emits = defineEmits(['update:modelValue', 'confirm'])
const showed = defineModel()

const language = ref(i18n.locale.value)

const changeLanguage = (lang) => {
    language.value = lang
}

const onConfirm = () => {
    if (!language.value) {
        return
    }
    emits('confirm', language.value)
    showed.value = false
}
</script>
<style scoped lang="scss">
.wrap {
    height: 900px;
    padding: 0 12px 160px;
}
.lang-wrap {
    margin: 0 auto;
    width: 100%;
    height: 100%;
    overflow: auto;
    // background-color: #fff;
}
.btn-wrap {
    @apply flex justify-center items-center;
    position: relative;
    padding-top: 25px;
    height: 160px;
    text-align: center;

    &::before {
        content: '';
        position: absolute;
        top: 5px;
        left: 50%;
        width: 640px;
        height: 4px;
        background-color: #ffffff;
        border-radius: 2px;
        opacity: 0.2;

        transform: translateX(-50%);
    }

    :deep(.tiktoko.submit) {
        width: 440px;
        height: 88px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        border: none;
    }
}
.next-step {
    margin: 0 auto 20px;
    width: 520px;
    height: 120px;
    line-height: 120px;
    border-radius: 20px;
    font-size: 30px;
    color: #fff;
    background: #169bd5;
    text-align: center;
}
:deep(.van-index-bar__sidebar) {
    right: 20px;
}
</style>
