<template>
    <div v-if="!store.homeShow" class="tab-top">
        <div class="go-back" @click="store.homeShow = true"></div>
        <div class="tag-name">{{ $t(typename) }}</div>
    </div>

    <div class="tab-list">
        <div class="lists-item" v-for="(item, index) in gameList" :key="item.gametag" @click="handleClick(item, index)">
            <img v-lazy="item.picture" :key="item.picture" />
            <div class="item-online">{{ item.gameonline }}</div>
            <div class="item-bot">
                <div class="item-name">
                    <div>{{ store.getGameName(item.gamefirm) }}</div>
                </div>
                <div class="item-hot">{{ item.gamehot }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'

const props = defineProps({
    type: {
        type: String,
        default: '',
    },
})
const store = useBaseStore()
const { goGamePage } = useGame()

const gameList = computed(() => {
    const type = props.type
    if (!type) {
        return []
    }
    return store.getGameList(type)
})
const typename = computed(() => {
    return props.type === 'history' ? 'Game_type_history' : store.tabData.find((item) => item.type === props.type)?.name
})
const handleClick = (item, index) => {
    Log({
        event: `home_${props.type}_${index + 1}_${item.gametag}`,
    })
    goGamePage(item)
}
</script>
<style lang="scss" scoped>
.tab-top {
    @apply flex justify-between items-center;
    padding-left: 15px;
    padding-right: 55px;

    .tag-name {
        flex: 1;
        line-height: 50px;
        position: relative;
        padding: 20px 0;
        font-family: MicrosoftYaHei;
        font-size: 30px;
        color: #ffffff;
        text-align: center;
    }
    .go-back {
        width: 44px;
        height: 40px;
        background: url('@/assets/img/new-home/back.png') no-repeat right center;
        background-size: 100% 100%;
    }
}

.tab-list {
    @apply grid grid-cols-3 gap-[14px];
    padding: 0 15px;
    padding-bottom: 30px;
    font-family: MicrosoftYaHei;

    .lists-item {
        position: relative;
        width: 230px;
        height: 280px;
        border-radius: 20px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .item-online {
            @apply flex items-center gap-[6px];
            position: absolute;
            right: 8px;
            top: 8px;
            min-width: 80px;
            height: 32px;
            line-height: 32px;
            padding: 0 12px;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
            background-blend-mode: normal, normal;
            border-radius: 16px;
            background-size: 100% 100%;
            font-size: 18px;
            font-weight: bold;
            color: #fff;

            &::before {
                display: inline-block;
                content: '';
                width: 18px;
                height: 20px;
                background: url('@/assets/img/home/<USER>') no-repeat;
                background-size: 100% 100%;
            }
        }

        .item-bot {
            @apply flex items-center  gap-[6px];

            position: absolute;
            bottom: 10px;
            width: 100%;
            padding: 0 20px;
            font-size: 18px;
            line-height: 30px;
            color: #ffffff;

            .item-name {
                flex: 1;
                overflow: hidden;

                div {
                    white-space: nowrap; /* 保持文本在一行显示 */
                    overflow: hidden; /* 隐藏超出容器部分的文本 */
                    text-overflow: ellipsis;
                }
            }

            .item-hot {
                @apply flex items-center gap-[6px];
                position: relative;
                &::before {
                    display: inline-block;
                    content: '';
                    width: 20px;
                    height: 22px;
                    background: url('@/assets/img/new-home/hot.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
}
</style>
