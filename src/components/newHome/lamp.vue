<template>
    <div class="lamp">
        <van-swipe class="roll-swipe" :autoplay="autoPlay" :show-indicators="false" :vertical="true" :touchable="false" :loop="false">
            <van-swipe-item v-for="(item, index) in store.lampList" :key="index">
                <div v-if="item.title === 'SystemMaintain'">
                    {{
                        $t('scrolling_text_sys_update', {
                            time1: dayjs(item.context.start).format('YYYY-MM-DD HH:mm:ss'),
                            time2: dayjs(item.context.end).format('YYYY-MM-DD HH:mm:ss'),
                        })
                    }}
                </div>
                <div class="roll-item">
                    <p>{{ item.context.winner.nickname }}</p>
                    <div :class="['icon', { active: item.title === 'PayoutBigAmount' }]">
                        {{ item.title === 'ThirdGameBigWin' ? 'BIG WIN' : $t('Withdrawal') }}
                    </div>
                    <p>{{ dayjs(item.time).format('A hh:mm:ss') }}</p>
                    <p class="lamp-coin">{{ curSymbol[wallet?.currency] }}{{ formatNumber(item.context.gold) }}</p>
                </div>
            </van-swipe-item>
            <van-swipe-item v-if="!store.lampList.length">
                <div class="roll-item">
                    {{ $t('scrolling_text_default') }}
                </div>
            </van-swipe-item>
        </van-swipe>
    </div>
</template>
<script setup lang="ts">
// import { decodeName, encryptUsername } from '@/utils'
import dayjs from 'dayjs'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { formatNumber } from '@/utils/toolsValidate'

const store = useBaseStore()

const { curSymbol, wallet } = storeToRefs(store)

const autoPlay = computed(() => store.gameList.marqueeInterval * 1000 || 0)
</script>
<style lang="scss" scoped>
.lamp {
    margin: 0 auto;
    width: 720px;
    height: var(--lamp-height);
    background-color: #2a2d3d;
    border-radius: 30px;

    .roll-swipe {
        height: 100%;

        .roll-item {
            display: flex;
            align-items: center;
            padding-left: 72px;
            height: 100%;
            line-height: 60px;
            background: url('@/assets/img/new-home/notice.png') no-repeat 20px center;
            background-size: 36px 30px;
            font-family: MicrosoftYaHei;
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
        }
        .icon {
            margin: 0 20px 0;
            min-width: 100px;
            padding: 0 5px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 8px;
            border: solid 2px #de4070;
            font-family: MicrosoftYaHei;
            font-size: 22px;
            font-weight: bold;
            color: #de4070;

            &.active {
                border-color: #10c580;
                color: #10c580;
            }
        }
        .lamp-coin {
            margin-left: 18px;
            color: #ffea77;
        }
    }
}
</style>
