<template>
    <div v-if="showicon" class="sign" @click="handleClick">
        <img src="@/assets/img/invitewheel/icon_bg.svg" />
        <div class="text" v-html="$t('invite_wheel_icon_title')"></div>
    </div>
</template>

<script lang="ts" setup>
import eventBus from '@/utils/bus'
import { useBaseStore } from '@/stores'

const router = useRouter()
const showicon = ref(false)
const store = useBaseStore()

watch(
    () => store.token,
    (val, oldVal) => {
        if (val && val != oldVal) {
            handlePoint()
        }
    }
)

const handleClick = () => {
    console.log('handleClick')

    if (!store.token) {
        router.push('/login')
        return
    }
    eventBus.emit('activity', {
        param: 'invitepop',
    })
}
const handleSign = async (show: boolean = false) => {
    console.log('handleRedfissionIcon-show', show)
    showicon.value = show
}
const handlePoint = () => {
    store
        .getSignEvent(
            {
                id: 'redfission',
            },
            false,
            false
        )
        .then((res) => {
            if (res.code === 200 && res.acticity.status === 1) {
                handleSign(true)
            } else {
                handleSign(false)
            }
        })
}

onMounted(() => {
    handlePoint()
})

onBeforeMount(() => {
    eventBus.on('redfissionable', handleSign)
    eventBus.on('checkPoint', handlePoint)
})
onBeforeUnmount(() => {
    eventBus.off('redfissionable', handleSign)
    eventBus.off('checkPoint', handlePoint)
})
</script>
<style lang="scss" scoped>
.sign {
    position: absolute;
    bottom: 305px;
    right: 20px;
    width: 93px;
    height: 93px;

    &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        width: 120px;
        height: 120px;
        background: url('@/assets/img/home/<USER>') no-repeat;
        background-size: 100% 100%;
        animation: rotated linear 2s infinite;
    }

    .text {
        position: absolute;
        bottom: -20px;
        width: 100%;
        color: #fff;
        text-align: center;
        text-shadow: 2px 3px 2px #390015;
        // -webkit-text-stroke-width: 1px;
        -webkit-text-stroke-color: #390015;
        font-family: 'Helvet';
        // font-family: 'Microsoft YaHei';
        font-size: 30px;
        font-style: normal;
        font-weight: 900;
        line-height: 24px;
        z-index: 1;
    }
    // .sign-btn {
    &::before {
        position: absolute;
        left: 50%;
        top: 50%;
        content: '';
        width: 85px;
        height: 85px;
        background: url('@/assets/img/invitewheel/icon.png') no-repeat;
        background-size: 100% 100%;
        transform: translate3d(-50%, -50%, 0);
        z-index: 1;
        // transform-origin: left center;
        // animation: rotate 3s ease-in-out infinite alternate;
    }
    // }
}
</style>
