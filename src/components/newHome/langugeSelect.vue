<template>
    <div class="langguge" @click="handleClick">
        <div class="icon"><img v-lazy="Icon" /></div>
        <div class="text">{{ curLang.show }}</div>
        <div class="row"></div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import eventBus from '@/utils/bus'
import Icon from '@/assets/img/new-home/setting/language.png'

const store = useBaseStore()
const curLang = computed(() => {
    return store.languageList.find((item) => item.value === store.lang) || store.languageList[0]
})

const handleClick = () => {
    eventBus.emit('menuSet', 'showLang')
}
</script>
<style lang="scss" scoped>
.langguge {
    @apply flex items-center;
    width: 314px;
    height: 60px;
    padding-right: 30px;
    padding-left: 8px;
    // padding-left: 32px;
    // line-height: 60px;
    background-color: #2a2d3d;
    border-radius: 30px;

    .text {
        flex: 1;
        font-family: MicrosoftYaHei;
        font-size: 30px;
        font-weight: bold;
        font-stretch: normal;
        color: #fff;
    }
    .row {
        width: 16px;
        height: 26px;
        margin-left: 20px;
        background: url('@/assets/img/new-home/menu-row.png') no-repeat;
        background-size: 100% 100%;
        transform: rotate(90deg);
    }
    .icon {
        @apply flex items-center;
        margin-right: 20px;
        width: 52px;
        height: 52px;

        img {
            width: 52px;
        }
    }
}
</style>
