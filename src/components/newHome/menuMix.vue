<template>
    <div class="menu-mix">
        <div class="login-area">
            <div v-if="store.token" class="login-user">
                <div class="avatar" @click="goProfile"><img :src="store.userInfo?.avatar" /></div>
                <div class="user-info">
                    <div class="user-id">ID:{{ store.userInfo?.uid }}<span @click="handleCopy(store.userInfo?.uid)"></span></div>
                    <div class="user-name elipsis">{{ store.userInfo?.nickname && decode(`${store.userInfo?.nickname}`) }}</div>
                </div>
                <div class="edit" @click="goProfile"></div>
            </div>

            <div v-else class="login" @click="handleLogin">{{ $t('Not_logged_in_prompt') }}</div>
        </div>
        <!-- VIP 区域 -->
        <div class="vip-box" :class="{ 'vip-animate': vipInfo?.levelUpReward?.length || vipInfo?.monthReward }">
            <div class="vip-level" @click="navigateToVip">
                <span>VIP {{ vipInfo.curLevel }}</span>
                <div class="vip-club">
                    <span>VIP Club >> </span>
                </div>
            </div>
            <div class="vip-progress">
                <div class="bar">
                    <div class="fill" :style="{ width: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                    <div class="bar_point1" :style="{ left: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                    <div class="bar_point2" :style="{ left: (vipInfo.exp / vipInfo.max) * 100 + '%' }"></div>
                </div>
                <div class="xp-text">
                    <span>{{ vipInfo.max - vipInfo.exp }} XP to</span> VIP {{ vipInfo.curLevel + 1 }}
                </div>
            </div>
        </div>

        <!-- 余额 + 按钮 -->
        <div class="balance-box">
            <div class="balance-row">
                <span>Cash</span>
                <span class="text-right" style="color: rgb(36, 238, 137)"
                    >{{ store.curSymbol[store.wallet?.currency] }} {{ formatNormalNumber(store.wallet?.gold || 0) }}</span
                >
            </div>
            <div class="balance-row">
                <span>Withdrawable</span>
                <span class="text-right" style="color: rgb(255, 152, 32)"
                    >{{ store.curSymbol[store.wallet?.currency] }} {{ formatNormalNumber(accountInfo?.balance || 0) }}</span
                >
            </div>
            <div class="balance-row">
                <span>Turnover Requirement</span>
                <span class="text-right">{{ store.curSymbol[store.wallet?.currency] }} {{ formatNormalNumber(accountInfo?.remainBet || 0) }}</span>
            </div>
            <div class="balance-row">
                <span>Bonus </span>
                <span class="text-right">{{ store.curSymbol[store.wallet?.currency] }} {{ formatNormalNumber(store.wallet?.bonus || 0) }}</span>
            </div>
            <div class="action-buttons">
                <button class="btn-box green" @click="goWallet"><img src="@/assets/img/icon/icon-svg-deposit.svg" />Deposit</button>
                <button class="btn-box yellow" @click="goWithdraw"><img src="@/assets/img/icon/icon-svg-withdraw.svg" />Withdraw</button>
            </div>
        </div>

        <!-- 主菜单 -->
        <div class="menu-wrap">
            <div class="menu-item" v-for="item in mainMenu" :key="item.name" @click="item.action">
                <van-badge :dot="item.noticeNum" :offset="[-15, 5]">
                    <img class="menu-icon" v-lazy="item.icon" />
                </van-badge>
                <div class="menu-name">{{ $t(item.name) }}</div>
            </div>

            <!-- 展开后的游戏子菜单 -->
            <transition name="fade">
                <div v-if="showGames" class="menu-wrap menu-sub">
                    <div class="menu-item" v-for="item in store.menuData" :key="item.type" @click="hanldeClick(item)">
                        <img class="menu-icon" v-lazy="item.menupicture" />
                        <div class="menu-name">{{ $t(item.name) }}</div>
                    </div>
                </div>
            </transition>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { decode } from 'js-base64'
import { useClipboard1 } from '@/hooks/useCopy'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { formatNumber, formatPercent, formatNormalNumber } from '@/utils/toolsValidate'
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'
import { storeToRefs } from 'pinia'
import { checkLogin } from '@/utils'
import { AccountInfo } from '@/api/wallet/type'
import * as api from '@/api/wallet'
import { CustomerUrl } from '@/enums'

const store = useBaseStore()
const accountInfo = ref<AccountInfo>()
const router = useRouter()
const i18n = useI18n()
const { toClipboard } = useClipboard1()

const { vipInfo, vipConfig } = storeToRefs(store)

const levelConfig = computed(() => {
    return vipConfig.value.find((item) => item.level === vipInfo.value.curLevel + (vipInfo.value.levelUpReward ? 0 : 1))
})
const hanldeClick = async (item) => {
    Log({
        event: `menu_${item.type}`,
    })
    try {
        const logined = await checkLogin()
        if (!logined) {
            return
        }
    } catch (e) {
        return
    }
    store.showMenu = false
    eventBus.emit('changeTab', item)
}
const showGames = ref(false)
const mainMenu = [
    {
        name: 'Home_Messages',
        icon: new URL('@/assets/img/new-home/notify.png', import.meta.url).href,
        action: () => {
            goMessage()
        },
        noticeNum: () => {
            return !!store.noticeNum
        },
        highlight: false,
    },
    {
        name: 'menu_game_record',
        icon: new URL('@/assets/img/new-home/record.png', import.meta.url).href,
        action: () => {
            handleRecord()
        },
        highlight: false,
    },
    {
        name: 'menu_bind_phone',
        icon: new URL('@/assets/img/new-home/phone.png', import.meta.url).href,
        action: () => {
            handleBind()
        },
        highlight: false,
    },
    {
        name: 'Game',
        icon: new URL('@/assets/img/new-home/game.png', import.meta.url).href,
        action: () => {
            showGames.value = !showGames.value
        },
        highlight: false,
    },
]
const handleCopy = (text) => {
    Log({
        event: 'menu_ID',
    })
    toClipboard(`${text}`)
    showToast(i18n.t('Copy_successfully'))
}
const handleLogin = () => {
    Log({
        event: 'menu_sign',
    })
    router.push('/login')
}
const goWallet = () => {
    Log({
        event: 'menu_wallet',
    })
    router.push({
        path: '/wallets',
        query: {
            back: 1,
        },
    })
}

const goWithdraw = () => {
    Log({ event: 'wallet_withdraw' })
    router.push({
        path: '/cashout',
    })
}
const goServer = () => {
    Log({
        event: 'menu_CS',
    })
    var url = CustomerUrl
    if (store.partner == 10002000) {
        url = 'https://www.facebook.com/profile.php?id=61552549876486'
    }
    window.open(url)
}
const handleRecord = () => {
    Log({
        event: 'menu_game_record',
    })
    eventBus.emit('menuSet', 'showRecord')
}
const handleBind = () => {
    Log({
        event: 'menu_bindphone',
    })
    eventBus.emit('menuSet', '/bindPhone')
}
const goMessage = () => {
    Log({
        event: 'menu_message',
    })
    router.push({
        path: '/message',
    })
}
const goProfile = () => {
    Log({
        event: 'menu_profile',
    })
    router.push({
        path: '/profile',
    })
}

function navigateToVip() {
    router.push('/vip')
}

onActivated(async () => {
    try {
        const res = await api.getAccountInfo(false)
        if (res.code === 200) {
            accountInfo.value = res.data
        }
    } catch (e) {
        console.log(e)
    }
})
</script>
<style lang="scss" scoped>
@use './menu.scss' as *;
.menu-mix {
    //padding: 12px;
    font-family: 'Microsoft YaHei', sans-serif;
    color: #fff;

    .login-area {
        display: flex;
        align-items: center;
        //margin-bottom: 16px;

        .login-user {
            display: flex;
            align-items: center;
            width: 100%;
            height: 120px;

            .avatar {
                width: 110px;
                height: 110px;
                border-radius: 50%;
                overflow: hidden;
                //margin-right: 12px;
                border: 6px solid #555;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .user-info {
                margin-left: 20px;
                flex: 1;

                .user-id {
                    display: flex;
                    align-items: center;
                    font-size: 30px;
                    font-weight: 600;
                    color: #fff;

                    span {
                        margin-left: 8px;
                        width: 30px;
                        height: 32px;
                        background: url('@/assets/img/new-home/copy.png') no-repeat center;
                        background-size: 100% 100%;
                        cursor: pointer;
                    }
                }

                .user-name {
                    font-size: 26px;
                    color: #8aa0b0;
                    margin-top: 2px;
                }
            }

            .edit {
                width: 124px;
                height: 154px;
                background: url('@/assets/img/new-home/menu-row.png') no-repeat center;
                background-size: 14px 24px;
                background-position: 100px;
                margin-right: 0px;
            }
        }
    }

    .vip-box {
        margin-top: 10px;
        margin-bottom: 20px;
        border-radius: 12px;
        background: linear-gradient(91deg, rgba(149, 255, 236, 0) 0.53%, rgba(115, 175, 168, 0.2) 98.76%), rgb(50 55 56);
        height: 130px;
        padding: 2px;

        .vip-level {
            margin-top: 10px;
            margin-left: 20px;
            display: flex;
            justify-content: space-between;
            font-size: 26px;
            //margin-bottom: 6px;
            font-weight: 1000;

            .vip-club {
                margin-top: 5px;
                margin-left: 20px;
                height: 35px; // ✅ 更高
                padding: 5px 11px 1px 15px; // ✅ 更宽
                font-size: 18px;
                margin-left: auto; // ✅ 让它靠右
                color: #24ee89;
                font-weight: 1000;
                background-color: rgba(255, 255, 255, 0.1);
                display: flex;
                align-items: center;
                border-radius: 30px 0 0 30px;
            }
        }

        .vip-progress {
            margin-left: 20px;
            margin-top: 22px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

            .bar {
                flex: 1;
                position: relative;
                height: 11px;
                background: rgba(0, 0, 0, 0.2);
                border-radius: 6px;
                // overflow: hidden;

                .fill {
                    height: 100%;
                    border-radius: 6px;
                    background: rgb(36 238 137);
                }
                .bar_point1 {
                    height: 30px;
                    width: 31px;
                    border-radius: 50%;
                    position: absolute;
                    top: -9.2px;
                    transform: translateX(-50%);
                    background: rgba(36, 238, 137, 0.3);
                }
                .bar_point2 {
                    height: 15px;
                    width: 15px;
                    border-radius: 50%;
                    position: absolute;
                    top: -1.6px;
                    transform: translateX(-50%);

                    background: rgb(36 238 137);
                }
            }

            .xp-text {
                margin-left: auto;
                margin-right: 10px;
                font-size: 20px;
                color: #aaa;
                white-space: nowrap;
                margin-left: 15px;
                margin-right: 15px;
                font-weight: bold;
                :deep(span) {
                    color: #fff;
                    font-weight: 1000;
                }
            }
        }
    }

    .balance-box {
        margin: 10px 0px 15px 0;
        padding: 20px;
        background: rgb(50 55 56);
        border-radius: 20px;

        .balance-row {
            height: 50px;
            // background-color: #000;
            display: flex;
            justify-content: space-between; // ✅ 左右分开
            font-size: 24px;
            color: #fff;
            font-weight: 600;
            .text-right {
                font-size: 26px;
            }
        }

        .action-buttons {
            display: flex;

            align-items: center; // ✅ 垂直居中
            height: 90px; // ✅ 更高
            display: flex;
            gap: 18px;

            .btn-box {
                flex: 1;
                height: 65px;
                font-size: 20px;
                font-weight: bold;
                border-radius: 12px;
                cursor: pointer;
                color: #000;
                font-weight: 800;
                align-items: center;
                display: flex;
                justify-content: center;
                :deep(img) {
                    width: 36px;
                    height: 36px;
                    margin-right: 10px;
                }
            }

            .green {
                background-image: linear-gradient(90deg, #24ee89, #9fe871);
                box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
            }

            .yellow {
                background-image: linear-gradient(90deg, rgb(255, 253, 128), rgb(255, 219, 108));
                box-shadow: rgba(255, 187, 0, 0.376) 0px 0px 0.6rem, rgb(255, 219, 108) 0px -4px inset;
            }
        }
    }
}

.vip-animate {
    border-radius: 20px;
    animation: glow 1.5s infinite alternate;

    @keyframes glow {
        from {
            box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }
        to {
            box-shadow: 0 0 20px rgba(255, 215, 0, 1);
        }
    }
}
.menu-wrap {
    padding-bottom: 1px;
}
.menu-wrap .menu-sub {
    background-color: rgba(100, 101, 102, 0.5);

    margin-bottom: 16px;
}
</style>
