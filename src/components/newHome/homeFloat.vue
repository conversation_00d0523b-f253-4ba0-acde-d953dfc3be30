<template>
    <div v-if="show" class="dowload-float" @click="goPage"></div>
</template>
<script lang="ts" setup>
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'

const show = ref(false)

const goPage = () => {
    Log({
        event: 'ladderloserebate',
        content: {
            entrance: 'ladderloserebate',
        },
    })
    // eventBus.emit('ladderloserebate')
    eventBus.emit('activity', {
        param: 'ladderloserebate',
    })
}
const handleShow = (flag) => {
    show.value = flag
}

onBeforeMount(() => {
    eventBus.on('loseReturn', handleShow)
})
onBeforeUnmount(() => {
    eventBus.off('loseReturn', handleShow)
})
</script>
<style lang="scss" scoped>
$w: 102px;
.dowload-float {
    position: absolute;
    width: $w;
    height: $w;
    bottom: 280px;
    right: 20px;
    background: url('@/assets/img/home/<USER>') no-repeat;
    background-size: 100% 100%;
    z-index: 400;

    transform-origin: right bottom;
    transform: scale(0.9);

    &::before,
    &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        transform: translate3d(-50%, -55%, 0);
        background: url('@/assets/img/home/<USER>') no-repeat;
        background-size: 100% 100%;
    }
    &::before {
        width: 122px;
        height: 129px;
    }
    &::after {
        width: 182px;
        height: 182px;
        background-image: url('@/assets/img/home/<USER>');
        z-index: -1;
        animation: rotated linear 2s infinite;
    }
}

@keyframes rotated {
    0% {
        transform: translate3d(-50%, -50%, 0) rotateZ(360deg);
    }
    100% {
        transform: translate3d(-50%, -50%, 0) rotateZ(0deg);
    }
}
</style>
