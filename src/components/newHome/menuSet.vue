<template>
    <div class="menu-set">
        <div class="title"></div>
        <div class="menu-wrap">
            <div class="menu-item" v-for="(item, index) in setConfig" :key="index" @click="hanldeClick(item)">
                <img class="menu-icon" v-lazy="item.icon" />
                <div class="menu-name">{{ $t(item.name) }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'
import dayjs from 'dayjs'
import { CustomerUrl } from '@/enums'

const store = useBaseStore()
const router = useRouter()
const i18n = useI18n()

const path = '../../assets/img/new-home/setting'
const file = import.meta.glob('../../assets/img/new-home/setting/*', { eager: true })

console.log(file, 'filefile')

const getImg = (name) => {
    return file[path + name].default
}
const over24 = computed(() => {
    const createT = dayjs(store.userInfo.createat)
    const nowT = dayjs()
    const diffHours = nowT.diff(createT, 'hour')
    return diffHours > 24
})
const setConfig = computed(() => {
    const list = [
        {
            name: 'Language_Selection',
            icon: getImg('/language.png'),
            type: 'diaglog',
            param: 'showLang',
            log: 'menu_language',
        },
        {
            name: 'Bind_inviter',
            icon: getImg('/bind.png'),
            type: 'diaglog',
            param: '/bindInviter',
            log: 'menu_bindinvite',
        },
        {
            name: 'Customer_service',
            icon: getImg('/customer.png'),
            type: 'event',
            log: 'menu_customer',
            param: 'customer',
        },
        {
            name: 'Security_Center',
            icon: getImg('/security.png'),
            type: 'page',
            param: '/security',
            log: 'menu_security',
        },
        {
            name: 'Redemption_Code',
            icon: getImg('/redem.png'),
            type: 'diaglog',
            param: 'showRedeem',
            log: 'menu_CDK',
        },
        {
            name: 'Logout',
            icon: getImg('/logout.png'),
            type: 'event',
            param: 'logout',
            log: 'menu_logout',
        },
    ]
    if (store.userInfo?.isReseller) {
        list.splice(list.length - 1, 0, {
            name: 'Auto_reply',
            icon: getImg('/robit.png'),
            type: 'diaglog',
            param: 'robit',
            log: 'menu_reply',
        })
    }
    if (store.userInfo?.shareid || over24.value) {
        list.splice(1, 1)
    }
    return list
})
const hanldeClick = (item) => {
    Log({
        event: item.log,
    })
    switch (item.type) {
        case 'diaglog':
            eventBus.emit('menuSet', item.param)
            break
        case 'page':
            router.push({
                path: item.param,
            })
            break
        case 'event':
            if (item.param === 'customer') {
                var url = CustomerUrl
                if (store.partner == 10002000) {
                    url = 'https://www.facebook.com/profile.php?id=61552549876486'
                }
                return window.open(url)
            }
            if (item.param === 'logout') {
                showDialog({
                    message: i18n.t('Are_you_sure_exit'),
                    className: 'common-dialog',
                    showCancelButton: true,
                    confirmButtonText: i18n.t('Confirm'),
                    cancelButtonText: i18n.t('Cancel'),
                })
                    .then(() => {
                        store.logOut()
                        store.showMenu = false
                    })
                    .catch((e) => {
                        console.log(e, 'Cancel')
                    })
            }
            break
        default:
            break
    }
}
</script>
<style lang="scss" scoped>
@use './menu.scss' as *;

.title {
    height: 15px;
}
.menu-name {
    &::after {
        display: none !important;
    }
}

.menu-icon {
    width: 40px !important;
    height: auto !important;
    filter: brightness(0) invert(0.6);
}
</style>
