<template>
    <van-overlay :show="visible" :z-index="300" @click="handleClose">
        <section class="robit" @click.stop>
            <div class="robit-head">
                <div class="head-title">{{ $t('Auto_reply_title') }}</div>
                <div class="head-close" @click="handleClose"></div>
            </div>
            <div class="robit-content">
                <div class="robit-tip">{{ $t('Auto_message') }}</div>
                <div class="robit-input">
                    <van-field v-model="message" class="robit-area" type="textarea" :maxlength="150" :placeholder="$t('Auto_reply_info')"></van-field>
                </div>
                <div
                    class="robit-count"
                    v-html="
                        $t('max_characters', {
                            number_1: message.length,
                        })
                    "
                ></div>
                <div :class="['robit-btn', { active: message.length }]" @click="submit">{{ $t('Confirm') }}</div>
            </div>
        </section>
    </van-overlay>
</template>
<script setup lang="ts">
import { getResellerAuto, setResellerAuto } from '@/api/home'
import { useI18n } from 'vue-i18n'
const visible = defineModel()
const i18n = useI18n()

const message = ref('')
const handleClose = () => {
    visible.value = false
}
const submit = async () => {
    try {
        const res = await setResellerAuto(message.value)
        if (res.code === 200) {
            visible.value = false
            showSuccessToast(i18n.t('Auto_set_success'))
        } else {
            showFailToast(i18n.t('Auto_set_fails'))
        }
    } catch (e) {
        console.log(e)
    }
}
const getResellerText = async () => {
    try {
        const res = await getResellerAuto()
        if (res.code === 200) {
            message.value = res.reply
        }
    } catch (e) {
        console.log(e)
    }
}
onMounted(() => {
    getResellerText()
})
</script>
<style lang="scss" scoped>
.robit {
    position: absolute;
    width: 684px;
    height: 620px;
    padding: 0 35px;
    top: 50%;
    left: 50%;
    background-color: #2a2d3b;
    border-radius: 27px;

    transform: translate3d(-50%, -50%, 0);

    font-family: MicrosoftYaHei;
    color: #ffffff;

    .robit-head {
        position: relative;
        height: 114px;

        .head-title {
            @apply flex justify-center items-center;
            position: relative;
            top: -39px;
            margin: 0 auto;
            width: 490px;
            height: 87px;
            padding-bottom: 11px;
            font-size: 36px;
            background: url('@/assets/img/wallets/bind_title.png') no-repeat;
            background-size: 100% 100%;
        }
        .head-close {
            position: absolute;
            right: 5px;
            top: 22px;
            width: 33px;
            height: 34px;
            background: url('@/assets/img/wallets/account_close.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .robit-tip {
        margin-bottom: 15px;
        font-size: 28px;
    }
    .robit-input {
        height: 268px;
        overflow: hidden;
        border-radius: 22px;
        background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#0579fe, #0579fe);
        .robit-area {
            width: 100%;
            height: 100%;
            padding: 37px 26px 37px;
            background: none;
            :deep(.van-field__body) {
                height: 100%;
            }
            :deep(textarea) {
                height: 100%;
                color: #fff;
                font-size: 26px;
                line-height: 40px;

                &::placeholder {
                    color: #747a8d;
                }
            }
        }
    }
    .robit-count {
        margin-top: 11px;
        font-size: 22px;
        text-align: right;

        :deep(span) {
            color: #ffa72a;
            margin-right: 10px;
        }
    }
    .robit-btn {
        @apply flex justify-center items-center;
        margin: 030px auto 0;
        width: 320px;
        height: 80px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 40px;
        font-size: 36px;
        opacity: 0.6;
        pointer-events: none;

        &.active {
            opacity: 1;
            pointer-events: auto;
        }
    }
}
</style>
