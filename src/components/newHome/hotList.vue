<template>
    <div class="tab-list">
        <div class="lists-item" v-for="(item, index) in gameList" :key="item.gametag" @click="handleClick(item, index)">
            <img v-lazy="item.picture" :key="item.picture" />
            <div class="item-online">{{ item.gameonline }}</div>
            <div class="item-bot">
                <div class="item-name">
                    <div>{{ store.getGameName(item.gamefirm) }}</div>
                </div>
                <div class="item-hot">{{ item.gamehot }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'

const props = defineProps({
    type: {
        type: String,
        default: '',
    },
})
const store = useBaseStore()
const { goGamePage } = useGame()

const gameList = computed(() => {
    const type = props.type
    if (!type) {
        return []
    }
    return store.getGameList(type).slice(0, 6)
})
const typename = computed(() => {
    return props.type === 'history' ? 'Game_type_history' : store.tabData.find((item) => item.type === props.type)?.name
})
const handleClick = (item, index) => {
    Log({
        event: `home_${props.type}_${index + 1}_${item.gametag}`,
    })
    goGamePage(item)
}
</script>
<style lang="scss" scoped>
.tab-list {
    @apply grid grid-cols-3 gap-y-[15px] gap-x-[0px];
    // padding: 0 15px;
    width: 98%;
    padding-bottom: 10px;
    font-family: MicrosoftYaHei;

    .lists-item {
        position: relative;
        width: 220px;
        height: 280px;
        border-radius: 20px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .item-online {
            @apply flex items-center gap-[6px];
            position: absolute;
            right: 8px;
            top: 8px;
            min-width: 80px;
            height: 32px;
            line-height: 32px;
            padding: 0 12px;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
            background-blend-mode: normal, normal;
            border-radius: 16px;
            background-size: 100% 100%;
            font-size: 18px;
            font-weight: bold;
            color: #fff;

            &::before {
                display: inline-block;
                content: '';
                width: 18px;
                height: 20px;
                background: url('@/assets/img/home/<USER>') no-repeat;
                background-size: 100% 100%;
            }
        }

        .item-bot {
            @apply flex items-center  gap-[6px];

            position: absolute;
            bottom: 10px;
            width: 100%;
            padding: 0 20px;
            font-size: 18px;
            line-height: 30px;
            color: #ffffff;

            .item-name {
                flex: 1;
                overflow: hidden;

                div {
                    white-space: nowrap; /* 保持文本在一行显示 */
                    overflow: hidden; /* 隐藏超出容器部分的文本 */
                    text-overflow: ellipsis;
                }
            }

            .item-hot {
                @apply flex items-center gap-[6px];
                position: relative;
                &::before {
                    display: inline-block;
                    content: '';
                    width: 20px;
                    height: 22px;
                    background: url('@/assets/img/new-home/hot.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
}
</style>
