<template>
    <van-popup class="channel-pop" v-model:show="show" position="bottom" close-on-popstate round :z-index="3001" @closed="handleClose">
        <div class="wallet-bonus">
            <div class="title">
                {{ $t('Home_Wallets') }}
                <div class="close" @click="handleClose"></div>
            </div>
            <div class="num-area">
                <div class="tip">
                    <div class="tip-title">Cash Balance</div>
                </div>
                <div class="num-wrap glod-num">
                    <div class="content-num">
                        <p class="cury">{{ store.curSymbol[store.wallet?.currency] }}</p>
                        {{ formatNormalNumber(store.wallet?.gold || 0) }}
                    </div>
                </div>
                <div class="tip">
                    <div class="tip-title">Bonus Balance</div>
                    <div class="tip-oher">
                        <div class="icon" @click="showTip = true"></div>
                        <span>{{
                            $t('bonus_explanation_tip', {
                                betrebate: betRebate,
                            })
                        }}</span>
                    </div>
                </div>
                <div class="num-wrap">
                    <div class="content-num">
                        <p class="cury">{{ store.curSymbol[store.wallet?.currency] }}</p>
                        {{ formatNormalNumber(store.wallet?.bonus || 0) }}
                    </div>
                </div>
                <div class="bonus-area">
                    <div>
                        <p class="bonus-num">{{ store.curSymbol[store.wallet?.currency] }} {{ formatNormalNumber(inbonus || 0) }}</p>
                        <p class="bonus-num">{{ $t('Vault') }}</p>
                    </div>

                    <!-- <div class="bonus-btn" type="button" :disabled="store.wallet?.gold > 0" @click="goPage(2)">{{ $t('claim') }}</div> -->
                    <van-button class="bonus-btn" :disabled="canClaim" @click="claimBouns">
                        {{ $t('claim') }}
                    </van-button>
                </div>
            </div>
            <div class="btn-wrap">
                <div class="btn left-btn" @click="goPage(1)">{{ $t('Deposit') }}</div>
                <div class="btn right-btn" @click="goPage(2)">{{ $t('Withdraw') }}</div>
            </div>
        </div>
    </van-popup>
    <van-overlay :show="showTip" :lock-scroll="false" @click="showTip = false" z-index="3001">
        <div class="dialog" @click.stop>
            <div class="title">
                Bonus
                <div class="close" @click="showTip = false"></div>
            </div>
            <div
                class="content"
                v-html="
                    vipInfo.curLevel == 0
                        ? $t('bonus_explanation_novip')
                        : $t('bonus_explanation', {
                              betrebate: betRebate,
                          })
                "
            ></div>
            <div class="confirm" @click="showTip = false">{{ $t('Confirm') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { formatLocalMoney, formatNumber, formatNormalNumber } from '@/utils/toolsValidate'
import { storeToRefs } from 'pinia'

const show = defineModel()
const router = useRouter()
const store = useBaseStore()
const { vipInfo, vipConfig } = storeToRefs(store)
const showTip = ref(false)
const inbonus = ref(0)
const canClaim = ref(false)

// const depositB = computed(() => {
//     return (vipConfig.value[vipConfig.value.length - 1].betRebate || 0) * 1000 + '‰'
// })

const betRebate = computed(() => {
    return (vipConfig.value.find((item) => item.level === vipInfo.value.curLevel)?.betRebate || 0) * 1000 + '‰'
})

const handleClose = () => {
    show.value = false
}
const goPage = (type = 1) => {
    router.push({
        path: type === 1 ? '/wallets' : '/cashout',
        query: {
            back: 'true',
        },
    })
    handleClose()
}

const checkBonusClaim = () => {
    store.getBonus(0, false).then((res) => {
        if (res.code === 200) {
            inbonus.value = res.gold
            canClaim.value = res.gold <= 0
        }
    })
}

const claimBouns = () => {
    store.getBonus(1, false).then((res) => {
        if (res.code === 200 && res.gold > 0) {
            inbonus.value = 0
            canClaim.value = false
        }
    })
}

watch(show, () => {
    console.log('show.value', show.value)
    if (show.value) checkBonusClaim()
})

onMounted(() => {
    checkBonusClaim()
})
</script>
<style lang="scss" scoped>
.channel-pop {
    background-color: #2a2d3d;
}
.wallet-bonus {
    width: 750px;
    min-height: 692px;
    padding: 0 22px 58px;
    background-color: #232626;
    font-family: MicrosoftYaHei;
    color: #fff;

    .title {
        position: relative;
        @apply flex;
        padding-top: 33px;
        padding-bottom: 44px;
        position: relative;
        font-size: 52px;
        font-weight: bolder;

        &::before {
            display: inline-block;
            content: '';
            margin-right: 20px;

            width: 52px;
            height: 52px;
            background: url('@/assets/img/new-home/wallet-icon.png') no-repeat;
            background-size: 100% 100%;
            position: relative;
            top: 10px;
        }
        .close {
            position: absolute;
            right: 0px;
            top: 50px;
            width: 54px;
            height: 54px;
            background: url('@/assets/img/close.png') no-repeat center center;
            background-size: 27px 27px;
        }
    }

    .num-area {
        margin-bottom: 42px;
        .num-wrap {
            @apply flex;
            justify-content: flex-start;
            width: 707px;
            height: 167px;
            padding: 0 38px;
            background-image: linear-gradient(#323738, #323738), linear-gradient(#000000, #000000);
            background-blend-mode: normal, normal;
            border-radius: 14px;
            font-size: 88px;
            font-weight: bolder;

            .cury {
                margin-right: 14px;
                margin-top: 12px;
                font-size: 55px;
                font-weight: normal;
            }
            .content-num {
                @apply flex items-center;
                width: 100%;
                height: 100%;
                line-height: 167px;
                margin-top: 24px;
                margin-left: -20px;
            }
        }

        .tip {
            @apply flex;
            margin-left: 20px;
            margin-bottom: -55px;
            font-size: 24px;
            color: #64697a;

            .tip-title {
                color: #908f8f;
                font-weight: bolder;
            }
            .tip-oher {
                @apply flex items-center;
                color: #fff;

                :deep(span) {
                    color: #fff;
                    font-size: 20px;
                }
                .icon {
                    margin: 0 15px;
                    width: 22px;
                    height: 22px;
                    background: url('@/assets/img/new-home/bonus-tip.png') no-repeat;
                    background-size: 100% 100%;
                    scale: 1.5;
                }
            }
        }
        .glod-num {
            margin-bottom: 50px;
            color: #24eb88;
        }
        .bonus-area {
            @apply flex;
            margin-top: -127px;
            justify-content: flex-end;
            align-items: center;
            width: 707px;
            height: 127px;
            padding: 0 38px;

            .bonus-num {
                right: 20px;
                font-size: 32px;
                font-weight: bolder;
                color: #ff9820;
                margin-right: 10px;
                margin-bottom: -35px;
                line-height: 74px;
                text-align: right;
            }
            .bonus-btn {
                @apply flex justify-center items-center;
                border: none;
                width: 122px;
                height: 68px;
                background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#8b0500, #8b0500);
                border-radius: 18px;
                font-size: 26px;
                margin-right: -20px;
                margin-bottom: -30px;
                color: #000000;
                font-weight: bolder;
            }
        }
    }

    .btn-wrap {
        @apply flex justify-center;

        .btn {
            @apply flex justify-center items-center;
            width: 332px;
            height: 88px;
            background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#8b0500, #8b0500);
            border-radius: 14px;
            font-size: 34px;
            font-weight: bolder;
            color: #000000;
        }
        .right-btn {
            margin-left: 30px;
            background-image: linear-gradient(90deg, #fffc7f 0%, #ffdc6d 100%), linear-gradient(#8b0500, #8b0500);
        }
    }
}
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 660px;
    height: 840px;
    padding: 0 28px 60px;
    border-radius: 30px;
    background-color: #2a2d3d;
    font-family: MicrosoftYaHei;
    color: #ffffff;

    transform: translate3d(-50%, -60%, 0);

    .title {
        position: relative;
        margin-bottom: 29px;
        line-height: 99px;
        font-size: 40px;
        text-align: center;

        &::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            width: 620px;
            height: 4px;
            background-color: #ffffff;
            border-radius: 2px;
            opacity: 0.2;

            transform: translateX(-50%);
        }
        .close {
            position: absolute;
            top: 30px;
            right: 20px;
            width: 33px;
            height: 34px;
            background: url('@/assets/img/new-home/close.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .content {
        flex: 1;
        overflow-y: scroll;
        font-size: 28px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 60px;
        letter-spacing: -1px;

        :deep(span) {
            color: #24eb88;
        }
    }
    .confirm {
        margin: 20px auto 0;
        width: 440px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        text-align: center;
        font-size: 36px;
        color: #ffffff;
    }
}
</style>
