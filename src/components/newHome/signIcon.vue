<template>
    <div v-if="show" class="sign" @click="handleClick">
        <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none">
            <circle cx="45" cy="45" r="43.25" fill="#024E00" stroke="url(#paint0_linear_247_183)" stroke-width="2.5" />
            <defs>
                <linearGradient id="paint0_linear_247_183" x1="45" y1="3" x2="45" y2="87" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#3CB45C" />
                    <stop offset="1" stop-color="#008E49" />
                </linearGradient>
            </defs>
        </svg>
        <div class="text" v-html="$t('online_signin_title')"></div>
    </div>
</template>
<script lang="ts" setup>
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'
import { useBaseStore } from '@/stores'

const show = ref(false)
const store = useBaseStore()

const handleClick = () => {
    Log({
        event: 'onlinesign_entry',
    })
    eventBus.emit('activity', {
        param: 'onlinesign',
    })
}
const handleSign = async (flag) => {
    console.log('handleSign-1', flag)

    show.value = flag
}
const handlePoint = () => {
    store
        .getSignEvent(
            {
                id: 'onlinesign',
            },
            false,
            false
        )
        .then((res) => {
            if (res.code === 200 && res.acticity.status === 1) {
                handleSign(true)
            } else {
                handleSign(false)
            }
        })
}
onBeforeMount(() => {
    eventBus.on('sign', handleSign)
    eventBus.on('checkPoint', handlePoint)
})
onBeforeUnmount(() => {
    eventBus.off('sign', handleSign)
    eventBus.off('checkPoint', handlePoint)
})
</script>
<style lang="scss" scoped>
.sign {
    position: absolute;
    bottom: 440px;
    right: 20px;
    width: 93px;
    height: 93px;
    &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        width: 120px;
        height: 120px;
        background: url('@/assets/img/home/<USER>') no-repeat;
        background-size: 100% 100%;
        animation: rotated linear 2s infinite;
    }

    svg {
        width: 100%;
        height: 100%;
    }
    .text {
        position: absolute;
        bottom: -15px;
        width: 100%;
        text-align: center;
        color: #fff;
        text-shadow: 1px 2px 2px #390015;
        font-family: 'Helvet';
        font-size: 28px;
        font-weight: bolder;
        line-height: 24px;
        z-index: 2;
    }
    &::before {
        position: absolute;
        left: 50%;
        top: -15px;
        content: '';
        width: 90px;
        height: 104px;
        background: url('@/assets/img/sign.png') no-repeat;
        z-index: 1;
        background-size: 100% 100%;
        transform: translatex(-42%);
        transform-origin: left center;
        // animation: rotate 3s ease-in-out infinite alternate;
    }
}
// @keyframes rotate {
//     0% {
//         transform: rotateZ(0) translatex(-42%);
//     }
//     35% {
//         transform: rotateZ(-5deg) translatex(-42%);
//     }
//     75% {
//         transform-origin: left center;
//         transform: rotateZ(5deg) translatex(-42%);
//     }
//     100% {
//         transform: rotateZ(0) translatex(-42%);
//     }
// }
</style>
