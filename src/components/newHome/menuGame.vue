<template>
    <div class="menu-game">
        <div class="title">{{ $t('Game') }}</div>
        <div class="menu-wrap">
            <div class="menu-item" v-for="item in store.menuData" :key="item.type" @click="hanldeClick(item)">
                <img class="menu-icon" v-lazy="item.menupicture" />
                <div class="menu-name">{{ $t(item.name) }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { checkLogin } from '@/utils'
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'

const store = useBaseStore()

const hanldeClick = async (item) => {
    Log({
        event: `menu_${item.type}`,
    })
    try {
        const logined = await checkLogin()
        if (!logined) {
            return
        }
    } catch (e) {
        return
    }
    store.showMenu = false
    eventBus.emit('changeTab', item)
}
</script>
<style lang="scss" scoped>
@use './menu.scss' as *;

.menu-game {
}
</style>
