<template>
    <div class="foru-list">
        <template v-for="item in store.tabMenu" :key="item.type">
            <div :class="['list-wrap', { 'list-even': item.type === 'history' }]">
                <div class="list-title">
                    <div class="list-tag">{{ $t(item.name) }}</div>
                    <div class="list-all" @click="showAll(item)">
                        {{ $t('home_more') }}
                        <div class="all-animate">
                            <div v-for="item in 3" :key="item"></div>
                        </div>
                    </div>
                </div>
                <div v-if="item.type === 'Hot'" class="list-scroll">
                    <HotList :type="item.type" />
                </div>
                <div v-if="item.type !== 'Hot'" class="list-scroll">
                    <swiper :direction="'horizontal'" :slidesPerView="'auto'" :freeMode="true" :mousewheel="true" :modules="modules" class="mySwiper">
                        <swiper-slide>
                            <div class="game-list">
                                <div
                                    class="game-item"
                                    v-for="(itd, idx) in store.getGameList(item.type)"
                                    :key="itd.gametag"
                                    @click="goGame(item, itd, idx)"
                                >
                                    <img v-lazy="itd.picture" />
                                    <div class="item-online">{{ itd.gameonline }}</div>
                                    <div class="item-bot">
                                        <div class="item-name">
                                            <div>{{ store.getGameName(itd.gamefirm) }}</div>
                                        </div>
                                        <div class="item-hot">{{ itd.gamehot }}</div>
                                    </div>
                                </div>
                            </div>
                        </swiper-slide>
                    </swiper>
                </div>
            </div>
        </template>
        <Media />
    </div>
</template>
<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css/free-mode'
import { FreeMode, Mousewheel } from 'swiper/modules'
import { useBaseStore } from '@/stores'
import eventBus from '@/utils/bus'
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'
import Media from './media.vue'
import HotList from './hotList.vue'

const modules = [FreeMode, Mousewheel]

const store = useBaseStore()
const { goGamePage } = useGame()

const showAll = (item) => {
    console.log('item', item)

    // return
    Log({
        event: `home_all_${item.name}`,
    })
    if (['HOT', 'NEW', 'HISTORY'].includes(item.type.toUpperCase())) {
        eventBus.emit('handleTabView', {
            flag: false,
            type: item.type,
        })
    } else {
        eventBus.emit('tabmenu-change', item)
        document.querySelector('.home-scroll').scrollTop = 0
    }
}

const groupedItems = (items) => {
    const groups = []
    for (let i = 0; i < items.length; i += 6) {
        groups.push(items.slice(i, i + 6))
    }
    return groups
}

const goGame = (item, itd, idx) => {
    Log({
        event: `home_${item.type}_${idx + 1}_${itd.gametag}`,
    })
    goGamePage(itd)
}
</script>
<style lang="scss" scoped>
.foru-list {
    padding-left: 29px;
}
.list-wrap {
    margin-bottom: 20px;
    font-family: MicrosoftYaHei;
    .list-title {
        @apply flex items-center justify-between;
        padding-bottom: 12px;
        padding-right: 19px;

        .list-tag {
            @apply flex items-center gap-[10px];
            position: relative;
            font-size: 30px;
            color: #ffffff;

            &::before {
                display: inline-block;
                content: '';
                width: 24px;
                height: 24px;
                background: url('@/assets/img/new-home/green-p.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        .list-all {
            @apply flex items-center gap-[4px];
            font-size: 24px;
            color: #668292;

            // &::after {
            //     display: inline-block;
            //     content: '';
            //     width: 19px;
            //     height: 16px;
            //     background: url('@/assets/img/new-home/all.png') no-repeat;
            //     background-size: 100% 100%;
            // }
            @keyframes opacityCycle {
                0% {
                    opacity: 1;
                }
                33.33% {
                    opacity: 0.2;
                }
                66.66% {
                    opacity: 0.6;
                }
                100% {
                    opacity: 1;
                }
            }
            .all-animate {
                @apply flex;
                margin-left: 3px;
                div {
                    // margin-left: -2px;
                    width: 12px;
                    height: 20px;
                    background: url('@/assets/img/home/<USER>') no-repeat;
                    background-size: 100% 100%;

                    animation: opacityCycle 3s infinite;

                    &:nth-child(1) {
                        animation-delay: 0s;
                    }
                    &:nth-child(2) {
                        animation-delay: 1s;
                    }
                    &:nth-child(3) {
                        animation-delay: 2s;
                    }
                }
            }
        }
    }
    .list-scroll {
        position: relative;

        .game-list {
            display: flex;
            flex-wrap: nowrap;

            height: 280px;

            .game-item {
                position: relative;
                margin-right: 20px;
                width: 230px;
                height: 279px;
                flex-shrink: 0;
                border-radius: 20px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;

                    object-fit: cover;
                }
                .item-online {
                    @apply flex items-center gap-[6px];
                    position: absolute;
                    right: 8px;
                    top: 8px;
                    min-width: 80px;
                    height: 32px;
                    line-height: 32px;
                    padding: 0 12px;
                    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
                    background-blend-mode: normal, normal;
                    border-radius: 16px;
                    background-size: 100% 100%;
                    font-size: 18px;
                    font-weight: bold;
                    color: #fff;

                    &::before {
                        display: inline-block;
                        content: '';
                        width: 18px;
                        height: 20px;
                        background: url('@/assets/img/home/<USER>') no-repeat;
                        background-size: 100% 100%;
                    }
                }

                .item-bot {
                    @apply flex items-center  gap-[6px];

                    position: absolute;
                    bottom: 6px;
                    width: 100%;
                    padding: 0 20px;
                    font-size: 20px;
                    line-height: 30px;
                    color: #ffffff;

                    .item-name {
                        flex: 1;
                        overflow: hidden;

                        div {
                            white-space: nowrap; /* 保持文本在一行显示 */
                            overflow: hidden; /* 隐藏超出容器部分的文本 */
                            text-overflow: ellipsis;
                        }
                    }

                    .item-hot {
                        @apply flex items-center gap-[6px];
                        position: relative;
                        &::before {
                            display: inline-block;
                            content: '';
                            width: 20px;
                            height: 22px;
                            background: url('@/assets/img/new-home/hot.png') no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                }
            }
        }
    }
    .swiper,
    .swiper-wrapper {
        width: 100%;
        height: 100%;
    }
    .swiper-slide {
        width: auto;
    }
    &.list-even {
        .game-list {
            height: 158px;

            .game-item {
                width: 130px;
                height: 158px;

                .item-online {
                    display: none;
                }
                .item-bot {
                    display: none;
                }
            }
        }
    }

    &.list-hot {
        .game-list {
            height: 258px;

            .game-item {
                width: 130px;
                height: 158px;

                .item-online {
                    display: none;
                }
                .item-bot {
                    display: none;
                }
            }
        }
    }
}
</style>
