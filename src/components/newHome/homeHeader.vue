<template>
    <div :class="['home-header', { active: store.showDown }]">
        <div v-if="store.showDown" class="down-wrap">
            <!-- <img class="down-icon" src="@/assets/img/home/<USER>" /> -->
            <div class="down-tip">
                {{
                    $t('Download_prompt_text1', {
                        text: $t(`${store.wallet?.currency || store.defaultCurrency.currency}_d_text_num`),
                    })
                }}
            </div>
            <div class="down-btn" @click="download">{{ $t('Download') }}</div>
            <div class="down-close" @click="store.showDownload = false"><img src="@/assets/img/home/<USER>" /></div>
        </div>

        <div v-if="token" class="login-wrap">
            <div class="home-logo"></div>
            <div :class="['user-info', { 'vip-animate': vipInfo?.levelUpReward?.length || vipInfo?.monthReward }]" @click="goVip">
                <div :class="['user-pic', `element-${getVipBadge(vipInfo.curLevel)}`]"></div>
                <div class="info">
                    <div class="user-id">{{ userInfo.uid }}</div>
                    <div class="user-name elipsis">
                        {{ userInfo?.nickname && decode(`${userInfo?.nickname}`) }}
                    </div>
                </div>
            </div>
            <div
                :class="[
                    'wallet-info',
                    {
                        'vip-animate': (!vipInfo.recharge || store.vipInfo.rechargeMax != 0) && wallet?.bonus > 0,
                    },
                ]"
            >
                <div class="wallet-left" @click="showBonusPop = true">
                    <template v-if="vipInfo.recharge || store.vipInfo.rechargeMax === 0 || wallet?.gold > 0">
                        <div class="wallet-coin">
                            <span> {{ curSymbol[wallet?.currency] }}{{ formatNumber(wallet?.gold || 0) }}</span>
                        </div>
                        <div class="wallet-bonus"><span>Bonus:</span> {{ curSymbol[wallet?.currency] }} {{ formatNumber(wallet?.bonus || 0) }}</div>
                    </template>

                    <div class="bonus" v-else>{{ curSymbol[wallet?.currency] }}{{ formatNumber(wallet?.bonus || 0) }}</div>
                    <transition
                        name="fly-b"
                        @after-enter="
                            () => {
                                showBonus = false
                            }
                        "
                    >
                        <div v-if="showBonus" class="bonus-box"></div>
                    </transition>
                </div>
                <!-- <div class="wallet-right" @click="goWallet"></div> -->
                <div class="wallet-right" @click="showBonusPop = true"></div>
            </div>
            <van-badge :content="freetimes" :offset="[-8, 3]" :show-zero="false" :style="{ zIndex: 300 }">
                <div class="gift" @click="handleGiftClick">
                    <div :class="['gift-tip', { active: giftTip && (!vipInfo.recharge || store.vipInfo.rechargeMax != 0) }]"></div>
                    <transition name="fly" @after-enter="onAnimationEnd">
                        <div v-if="showgift" class="bloom-box">
                            <img :src="giftimg" />
                        </div>
                    </transition>
                </div>
            </van-badge>
            <van-badge :dot="!!store.noticeNum" :offset="[-1, 3]">
                <div class="menu" @click="handleMenuClick"></div>
            </van-badge>
        </div>
        <div v-else class="login-wrap justify-between">
            <div class="nologin-logo"></div>
            <div class="header-tip">{{ $t('Not_logged_in_text') }}</div>
            <!-- <div class="header-login1" @click="handleLogin">{{ $t('Sign In') }}</div> -->
            <div class="header-login" @click="handleLogin">{{ $t('Not_logged_in_prompt') }}</div>
        </div>
        <Currency v-model="showCurrency" @confirm="onSelect" :default="wallet?.currency" />
        <IosInstruction v-if="showDownload" v-model="showDownload" />
        <GiftCenter v-model="showGiftCenter" />
        <BonusPop v-model="showBonusPop" />
    </div>
</template>
<script setup lang="ts">
import Currency from '@/components/wallet/currencyDialog.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { decode } from 'js-base64'
import { useRouter, useRoute } from 'vue-router'
import { getDeviceInfo, getUrlParams, getVipBadge } from '@/utils'
import { formatNumber } from '@/utils/toolsValidate'
import { Log } from '@/api/log'
import eventBus from '@/utils/bus'
import { getfreepackage } from '@/api/home'
// import popupManager from '@/utils/PopupManager'
// import invitepop from '../invitewheel/invitepop.vue'

const IosInstruction = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/views/iosInstruction/index.vue'),
    delay: 2000,
})
const GiftCenter = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/components/newHome/giftCenter.vue'),
    delay: 2000,
})
const BonusPop = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/components/newHome/bonusPop.vue'),
    delay: 2000,
})

const store = useBaseStore()
const router = useRouter()
const route = useRoute()

const { userInfo, wallet, token, curSymbol, vipInfo } = storeToRefs(store)

const showCurrency = ref(false)
const showDownload = ref(false)
const showGiftCenter = ref(false)
const showBonusPop = ref(false)
const showgift = ref(false)
const showBonus = ref(false)
const freetimes = ref(0)

const giftTip = ref(false)

const giftimg = ref('')

const handleGiftClick = () => {
    showGiftCenter.value = true
    giftTip.value = false
}

const onSelect = (item) => {
    wallet.value.currency = item.currency
}
// const goWallet = () => {
//     Log({
//         event: 'home_wallet',
//     })
//     router.push({
//         path: '/wallets',
//         query: {
//             back: 1,
//         },
//     })
// }
const handleMenuClick = () => {
    Log({
        event: 'home_menu',
    })
    store.setMenu(true)
}
// const handleSelect = () => {
//     Log({
//         event: 'home_exch_currency',
//     })
//     showCurrency.value = true
// }
const download = () => {
    if (getDeviceInfo().os === 'iOS') {
        // router.push('/iosInstruction')
        showDownload.value = true
    } else {
        const id = getUrlParams('userId') || route.query.userId
        const partner = getUrlParams('partner') || route.query.partner || 66666666
        window.open(`https://betfugu.com/app_ph.html?partner=${partner}${id ? '&userId=' + id : ''}`)
    }
}
const handleLogin = () => {
    // Log({
    //     event: 'home_sign',
    // })
    router.push({
        path: '/login',
        query: {
            back: 1,
            path: route.path,
        },
    })
}
const handleClose = () => {
    giftTip.value = true
    showGiftCenter.value = true
}
const handleAnimate = ({ type = 1, data }) => {
    if (type === 1) {
        giftimg.value = data
        showgift.value = true
    } else {
        showBonus.value = true
    }
}

const onAnimationEnd = () => {
    showgift.value = false
}

const handleFreeGameTimes = async () => {
    let fts = 0
    try {
        getfreepackage(false).then((res) => {
            if (res.code === 200) {
                const freePackage = res.freePackage

                Object.keys(freePackage).forEach((key) => {
                    const num = freePackage[key]
                    if (num) {
                        fts += num
                    }
                })
                freetimes.value = fts
            }
        })
    } catch (e) {
        console.log(e)
    }
    freetimes.value = fts
}

onBeforeMount(() => {
    eventBus.on('chargeGiftClose', handleClose)
    eventBus.on('bloomAnimate', handleAnimate)
    eventBus.on('freeGametimes', handleFreeGameTimes)
})
onBeforeUnmount(() => {
    eventBus.off('chargeGiftClose', handleClose)
    eventBus.off('bloomAnimate', handleAnimate)
    eventBus.off('freeGametimes', handleFreeGameTimes)
})
onMounted(() => {
    if (localStorage.getItem('pwaIos')) {
        localStorage.removeItem('pwaIos')
        showDownload.value = true
    }
})
const goVip = () => {
    router.push('/vip')
}
</script>
<style lang="scss" scoped>
.home-header {
    padding: 20px 20px;
    height: calc(var(--home-header));

    font-family: --font-family;
    padding-top: calc(var(--safe-height) + constant(safe-area-inset-top));
    padding-top: calc(var(--safe-height) + env(safe-area-inset-top));

    &.active {
        height: calc(var(--home-header));
    }
    .login-wrap {
        display: flex;
        align-items: center;
        height: 82px;

        .home-logo {
            margin-right: 13px;
            width: 116px;
            height: 76px;
            background: url('@/assets/img/new-home/logo.png') no-repeat left center;
            background-size: 100% 100%;
        }
        .user-info {
            display: flex;
            align-items: center;
            width: 202px;
            height: 72px;
            padding: 0 4px;
            background-color: #2a2d3d;
            border-radius: 14px;

            .user-pic {
                margin-right: 10px;
                width: 72px;
                height: 68px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                background-position: left center;
                @for $i from 0 through 5 {
                    &.element-#{$i} {
                        background-image: url('@/assets/img/vip/vip#{$i}.png');
                    }
                }
            }
            .info {
                flex: 1;
                font-weight: bold;
                overflow: hidden;
                .user-id {
                    font-size: 24px;
                    color: #ffffff;
                }
                .user-name {
                    font-size: 18px;
                    color: #668292;
                }
            }
        }
        .wallet-info {
            display: flex;
            align-items: center;
            margin: 0 10px;
            width: 230px;
            height: 72px;
            padding: 0 4px;
            background-color: #2a2d3d;
            color: #ffea76;
            border-radius: 14px;
            .wallet-left {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                height: 100%;

                .wallet-coin {
                    @apply flex items-center;
                    font-size: 24px;
                    font-weight: bold;
                    .coin-icon {
                        margin-right: 8px;
                        width: 24px;
                        height: 24px;
                        background: url('@/assets/img/new-home/switch.png') no-repeat;
                        background-size: 100% 100%;
                    }
                    span {
                        position: relative;
                        top: 2px;
                        letter-spacing: -0.5px;
                    }
                }
                .wallet-bonus {
                    font-size: 18px;
                    font-weight: bold;
                    letter-spacing: -0.5px;

                    span {
                        color: #fff;
                    }
                }
                .bonus {
                    @apply flex items-center justify-start;
                    // padding-left: 39px;
                    font-size: 24px;
                    line-height: 10px;

                    &::before {
                        display: inline-block;
                        content: '';
                        margin-right: 10px;
                        width: 39px;
                        height: 38px;
                        background: url('@/assets/img/new-home/B.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }
            }
            .wallet-right {
                @apply flex items-center justify-center;
                margin-left: 10px;
                width: 64px;
                height: 64px;
                background-image: linear-gradient(0deg, #0e8e66 0%, #10c57f 100%), linear-gradient(#887afb, #887afb);
                border-radius: 12px;

                &::before {
                    display: inline-block;
                    content: '';
                    width: 34px;
                    height: 32px;

                    background: url('@/assets/img/new-home/shop.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }

        .gift,
        .menu {
            @apply flex items-center justify-center;
            width: 64px;
            height: 64px;
            background-image: linear-gradient(0deg, #0e8e66 0%, #10c57f 100%), linear-gradient(#887afb, #887afb);
            background-blend-mode: normal, normal;
            border-radius: 12px;

            &::before {
                display: inline-block;
                content: '';
                width: 32px;
                height: 30px;

                background: url('@/assets/img/new-home/menu.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        .gift {
            position: relative;
            margin-right: 10px;
            &::before {
                width: 64px;
                height: 64px;
                background-image: url('@/assets/img/new-home/gift.png');
            }
            .gift-tip {
                position: absolute;
                right: -55px;
                bottom: 0;
                width: 437px;
                height: 173px;
                background: url('@/assets/img/new-home/text.png') no-repeat;
                background-size: 100% 100%;
                z-index: 300;
                transform: translateY(90%);
                pointer-events: none;
                opacity: 0;

                &.active {
                    opacity: 1;
                    animation: boundce 1s ease infinite alternate;
                }
            }
        }

        .header-tip {
            font-size: 24px;
            font-weight: bold;
            color: #848d93;
            width: 360px;
        }
        .nologin-logo {
            margin-right: 13px;
            width: 142px;
            height: 37px;
            background: url('@/assets/img/new-home/logo2.png') no-repeat left center;
            background-size: 100% 100%;
        }

        .header-login {
            width: 165px;
            height: 64px;
            line-height: 64px;
            background-image: linear-gradient(90deg, #10c57f 0%, #7ce520 90%), linear-gradient(#e59e20, #e59e20);
            background-blend-mode: normal, normal;
            border-radius: 30px;
            font-size: 28px;
            font-weight: bold;
            color: #000;
            text-align: center;
        }

        .header-login1 {
            width: 165px;
            height: 64px;
            line-height: 64px;
            // margin-left: 60px;
            background-blend-mode: normal, normal;
            border-radius: 30px;
            font-size: 30px;
            font-weight: bold;
            color: #fff;
            text-align: center;
        }
    }
}
.down-wrap {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 7px;
    height: 100px;
    border-bottom: 1px solid #3d3b38;
    .down-icon {
        width: 71px;
        height: 67px;
    }
    .down-tip {
        flex: 1;
        margin: 0 10px;
        font-size: 30px;
        color: #fff;
        line-height: 34px;
    }
    .down-btn {
        width: 193px;
        height: 60px;
        margin-top: 10px;
        margin-right: 80px;
        line-height: 60px;
        font-size: 34px;
        // background-image: linear-gradient(0deg, #0e8e66 0%, #10c57f 100%), linear-gradient(#887afb, #887afb);
        background-image: linear-gradient(90deg, #fffc7f 0%, #dfff6d 100%), linear-gradient(#8b0500, #8b0500);
        font-size: 28px;
        text-align: center;
        border-radius: 30px;
        font-weight: bold;
        color: #000;
    }
    .down-close {
        position: absolute;
        width: 40px;
        height: 40px;
        right: 10px;
        margin-top: 12px;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
:deep(.van-badge--dot) {
    width: 20px;
    height: 20px;
    background: #e24757;
}
.bloom-box {
    position: fixed;
    top: 50%;
    right: 50%;
    width: 355px;
    height: 310px;
    border-radius: 8px;
    transform: translate(-50%, -50%); /* 确保最终居中 */
    // background: url('@/assets/img/dialog/welcome-gift/bomb.png') no-repeat;
    background-size: 100% 100%;
    z-index: 4000;
}

/* 入场动画 */
.fly-enter-active,
.fly-b-enter-active {
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.fly-enter-active {
    transition-duration: 1s;
    transition-timing-function: cubic-bezier(0.69, 0.32, 0.3, 1.25);
}

.fly-enter-from,
.fly-b-from {
    transform: translate(50%, -50%) /* 初始居中位置 */ scale(1.2); /* 放大2倍 */
    right: 50%;
    top: 50%;
}

.fly-enter-to {
    transform: translate(50%, -50%) /* 移动到顶部中央 */ scale(0); /* 缩小回正常尺寸 */
    left: auto;
    right: 130px;
    top: 180px; /* 顶部留20px间距 */
}
.fly-leave-to,
.fly-b-leave-to {
    opacity: 0;
}
.fly-leave-active,
.fly-b-leave-active {
    opacity: 0;
}
.bonus-box {
    position: fixed;
    top: 50%;
    right: 50%;
    width: 231px;
    height: 250px;
    transform: translate(50%, -50%); /* 确保最终居中 */
    background: url('@/assets/img/dialog/bonus/bouns.png') no-repeat;
    background-size: 100% 100%;
    z-index: 4000;
}
.fly-b-enter-to {
    transform: translate(50%, -50%) /* 移动到顶部中央 */ scale(0); /* 缩小回正常尺寸 */
    left: auto;
    right: 50%;
    top: 180px; /* 顶部留20px间距 */
}
@keyframes boundce {
    0% {
        transform: scale(1) translateY(90%);
    }
    100% {
        transform: scale(1.05) translateY(90%);
    }
}
</style>
