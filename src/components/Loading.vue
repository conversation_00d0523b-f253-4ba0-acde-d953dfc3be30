<template>
    <div class="Loading inline normal">
        <div class="circle blue"></div>
        <div class="circle red"></div>
    </div>
</template>
<style scoped lang="scss">
.Loading {
    &.inline {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        z-index: 3000;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &.small {
        .circle {
            width: 20px;
            height: 20px;
        }
    }

    &.full {
        z-index: 999;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: space-between;
        width: 20px;
    }

    .circle {
        width: 15px;
        height: 15px;
        border-radius: 50%;
    }

    .blue {
        background: cadetblue;
        animation: anim-blue 0.4s ease-in-out 0s infinite alternate;
    }

    .red {
        background: var(--primary-btn-color);
        animation: anim-red 0.4s ease-in-out 0s infinite alternate;
    }

    @keyframes anim-blue {
        from {
            transform: translate3d(0, 0, 0) scale(1);
        }
        to {
            transform: translate3d(10px, 0, 0) scale(1.2);
        }
    }
    @keyframes anim-red {
        from {
            transform: translate3d(0, 0, 0) scale(1);
        }
        to {
            transform: translate3d(-10px, 0, 0) scale(1.2);
        }
    }
}
</style>
