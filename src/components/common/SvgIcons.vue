<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-25 15:56:21
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-25 19:59:35
 * @FilePath     : /src/components/common/SvgIcons.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-25 15:56:21
-->
<template>
    <!-- SVG 图标定义 - 公共图标库 -->
    <svg style="display: none">
        <!-- 左箭头 -->
        <symbol viewBox="0 0 24 24" id="icon-svg-JiantouL">
            <path d="M15 18L9 12L15 6" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </symbol>

        <!-- 关闭图标 -->
        <symbol viewBox="0 0 24 24" id="icon-svg-Close">
            <path d="M18 6L6 18M6 6L18 18" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </symbol>

        <!-- Jackpot 元宝图标 -->
        <symbol viewBox="0 0 17 16" id="icon-svg-JackpotYuan">
            <path
                d="M8.5 10.88c.56 0 1.018.431 1.063.98l.003.087v1.987a1.067 1.067 0 0 1-2.13.087l-.003-.088v-1.986c0-.589.477-1.067 1.066-1.067H8.5Zm-2.037-.844c.396.396.416 1.026.06 1.444l-.06.065L5.06 12.95a1.067 1.067 0 0 1-1.568-1.444l.06-.065 1.404-1.405a1.067 1.067 0 0 1 1.508 0Zm5.518-.059.065.06 1.404 1.404a1.067 1.067 0 0 1-1.444 1.568l-.065-.06-1.404-1.404a1.067 1.067 0 0 1 1.444-1.568Zm2.452-3.044a1.067 1.067 0 0 1 .088 2.13l-.088.003h-1.986a1.067 1.067 0 0 1-.088-2.13l.088-.003h1.986Zm-9.88 0a1.067 1.067 0 0 1 .087 2.13l-.088.003H2.566a1.067 1.067 0 0 1-.088-2.13l.088-.003H4.553Zm8.896-3.882c.396.395.416 1.025.06 1.444l-.06.064-1.404 1.405a1.067 1.067 0 0 1-1.568-1.444l.06-.065 1.404-1.404a1.067 1.067 0 0 1 1.508 0Zm-8.455-.06.065.06 1.404 1.404A1.067 1.067 0 0 1 5.02 6.023l-.064-.06L3.55 4.56a1.067 1.067 0 0 1 1.444-1.568ZM8.5 1c.56 0 1.018.431 1.063.98l.003.087v1.987a1.067 1.067 0 0 1-2.13.087l-.003-.088V2.067C7.433 1.477 7.91 1 8.499 1H8.5Z"
            />
        </symbol>

        <!-- Jackpot 角标图标 -->
        <symbol viewBox="0 0 77 28" id="icon-svg-JackpotJiao">
            <path
                d="M.5 2a2 2 0 0 1 2-2h71.769c1.689 0 2.617 1.964 1.545 3.27l-7.771 9.46a2 2 0 0 0 0 2.54l7.771 9.46c1.073 1.306.144 3.27-1.545 3.27H2.5a2 2 0 0 1-2-2V2Z"
            />
        </symbol>

        <symbol viewBox="0 0 32 32" id="icon-svg-RuleIcon">
            <path
                d="M16 3.826c6.724 0 12.174 5.45 12.174 12.174 0 6.724-5.45 12.174-12.174 12.174-6.724 0-12.174-5.45-12.174-12.174C3.826 9.277 9.276 3.826 16 3.826Zm0 2.609a9.565 9.565 0 1 0 0 19.13 9.565 9.565 0 0 0 0-19.13Zm-.04 14.71a1.305 1.305 0 1 1-.001 2.61 1.305 1.305 0 0 1 .001-2.61Zm.23-12.252c1.356 0 2.462.355 3.297 1.106.835.73 1.252 1.732 1.252 3.005 0 1.044-.271 1.9-.772 2.567-.188.21-.793.773-1.795 1.65a3.235 3.235 0 0 0-.835 1.043c-.179.357-.28.73-.307 1.145l-.006.211v.292h-2.4v-.292c0-.793.126-1.482.418-2.045.256-.532.996-1.344 2.22-2.452l.221-.198.25-.293c.377-.459.564-.96.564-1.481 0-.69-.208-1.232-.584-1.628-.396-.397-.96-.585-1.67-.585-.918 0-1.565.272-1.961.856-.323.437-.49 1.045-.518 1.81l-.004.235h-2.38c0-1.544.44-2.755 1.357-3.631.898-.877 2.108-1.315 3.653-1.315Z"
            ></path>
        </symbol>
    </svg>
</template>

<script setup>
// 这个组件只是用来定义SVG图标，不需要任何逻辑
</script>
