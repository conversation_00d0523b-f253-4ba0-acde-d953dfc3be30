interface SABridgeInterface {
    getAFID(): string
    signInWithGoogle(): void
    signInWithFacebook(): void
    getADAttribution(): void
    changeRotation(orientation: string): void
    reqWakeUrl(): void
    shareContent(url: string, txt: string, title: string): void
    getZGVisitorInfo(): string
}

declare const SABrige: SABridgeInterface // 声明 SABridge 变量

export function SAJSGoogleLogin() {
    try {
        SABrige.signInWithGoogle()
    } catch (error) {
        console.log(error)
    }
}

export function SAJSFacebookLogin() {
    try {
        SABrige.signInWithFacebook()
    } catch (error) {
        console.log(error)
    }
}

export function SAJSChangeRotation(orientation: string) {
    try {
        SABrige.changeRotation(orientation)
    } catch (error) {
        console.log(error)
    }
}

export function SAJSGetADAttribution() {
    try {
        SABrige.getADAttribution()
    } catch (error) {
        console.log(error)
    }
}

export function SAJSReqWakeUrl() {
    try {
        SABrige.reqWakeUrl()
    } catch (error) {
        console.log(error)
    }
}

export function SAJSShareContent(url: string, txt: string, title: string) {
    try {
        SABrige.shareContent(url, txt, title)
    } catch (error) {
        console.log(error)
    }
}

export function SAJSGetZGVisitorInfo(): string {
    try {
        return SABrige.getZGVisitorInfo()
    } catch (error) {
        return ''
    }
}

export function SAJSGetAFID(): string {
    try {
        return SABrige.getAFID()
    } catch (error) {
        return ''
    }
}
