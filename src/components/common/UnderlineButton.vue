<template>
    <button class="underline-animation" @click="$emit('click')">
        <slot></slot>
    </button>
</template>

<style scoped>
.underline-animation {
    background: none;
    border: none;
    padding: 0 0 4px 0;
    position: relative;
    cursor: pointer;
    font-size: inherit;
}

.underline-animation::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background: currentColor;
    transform: scaleX(1);
    transition: transform 0.25s ease;
}

.underline-animation:hover:after {
    transform: scaleX(0);
}
</style>
