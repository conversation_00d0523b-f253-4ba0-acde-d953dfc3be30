<template>
    <div class="pop-title">
        <button class="nav-back-button" @click="$emit('close')"></button>
        <span class="title">{{ $t(title) }}</span>
    </div>
</template>

<script setup lang="ts">
defineEmits<{
    close: []
}>()

defineProps({
    title: {
        type: String,
        default: '',
    },
})
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$bg-secondary: #383838;
$bg-hover: #4a4a4a;
$bg-button: #464f50;
$text-primary: #ffffff;
$accent-color: #4caf50;

.pop-title {
    position: sticky;
    top: 0;
    display: flex;
    align-items: flex-end;
    padding-bottom: 20px;
    // justify-content: center;
    font-size: 27.9122px;
    font-weight: 800;
    line-height: 55.8244px;
    width: 100%;
    height: var(--home-header);
    background-color: rgb(50, 55, 56, 1);
    z-index: 100;
    button {
        width: 55px;
        height: 55px;
        position: absolute;
    }

    button::after {
        content: '';
        position: absolute;
        top: -15px;
        left: -15px;
        right: -15px;
        bottom: -15px;
    }
    .nav-back-button {
        left: 25px;
        border-radius: 10px;
        background: url('@/assets/img/icon/rounded-left.svg') no-repeat center center;
        background-size: 70% 70%;
        background-color: rgb(70, 79, 80, 0.5);
        color: #fff;
    }

    .title {
        font-size: 27.9122px;
        color: $text-primary;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
    }
}
</style>
