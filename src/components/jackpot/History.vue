<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-25 15:02:56
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-14 14:55:55
 * @FilePath     : /src/components/jackpot/History.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-25 15:02:56
-->

<template>
    <div class="history-page">
        <div class="main-container">
            <div class="pop-title">
                <div class="date-navigation-container">
                    <div class="date-navigation">
                        <button type="button" class="date-nav-button date-nav-prev" @click="previousDate" :disabled="isPrevDisabled">
                            <svg class="nav-arrow-icon" aria-hidden="true">
                                <use href="#icon-svg-JiantouL" fill=""></use>
                            </svg>
                        </button>
                        <p class="current-date-text">{{ currentDate }}</p>

                        <button type="button" class="date-nav-button date-nav-next" @click="nextDate" :disabled="isNextDisabled">
                            <svg class="nav-arrow-icon nav-arrow-right" aria-hidden="true">
                                <use href="#icon-svg-JiantouL" fill=""></use>
                            </svg>
                        </button>
                    </div>
                </div>
                <button type="button" class="close-button" @click="$emit('close')">
                    <svg class="close-icon" aria-hidden="true">
                        <use href="#icon-svg-Close" fill=""></use>
                    </svg>
                </button>
            </div>

            <!-- 历史记录列表 -->
            <div class="history-content">
                <RankingList
                    :data-list="transformedHistoryData"
                    :headers="tableHeaders"
                    :show-avatars="true"
                    :avatar-count="3"
                    :zebra-stripe-pattern="false"
                    :loading="isLoading"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import RankingList from './RankingList.vue'
import { processPlayerList } from './calculator'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { formatDateOnly, subtractTime } from '@/utils/date'
import { ERROR_CODES } from '@/enums'
import { useI18n } from 'vue-i18n'

// 定义事件
const emit = defineEmits(['close', 'dateChange'])

// 使用 jackpot store 获取数据
const jackpotStore = useJackpotStore()
const { jackpotData } = storeToRefs(jackpotStore)

const store = useBaseStore()
const { wallet, curSymbol } = storeToRefs(store)
const { t } = useI18n()

const isLoading = ref(true)

// 历史数据存储
const historyDataCache = ref({})
const currentHistoryData = ref(null)

// 接收父组件传入的初始日期索引，如果没有则默认为1
const props = defineProps({
    initialDateIndex: {
        type: Number,
        default: 1,
    },
})

const dateIndex = ref(props.initialDateIndex)

// 定义常量
const MAX_HISTORY_DAYS = 100 // 最大历史数据天数
const DAYS_PER_CLICK = 1 // 每次点击跳转的天数

const isPrevDisabled = computed(() => {
    return dateIndex.value >= MAX_HISTORY_DAYS
})

const isNextDisabled = computed(() => {
    return dateIndex.value <= 1
})

// 根据当前索引实时计算日期
const currentDate = computed(() => {
    const jackpotDataValue = jackpotData.value
    const daysToSubtract = dateIndex.value // 索引1表示昨天(减1天)，索引2表示前天(减2天)

    // 有服务器时间就用服务器时间，没有就用本地时间
    const baseTime = jackpotDataValue?.time ? jackpotDataValue.time : new Date()

    // 计算目标日期
    const targetDate = subtractTime(baseTime, daysToSubtract, 'day')
    return formatDateOnly(targetDate, '/')
})

// 表头文案配置
const tableHeaders = {
    col1: t('jackpot.table.rank'),
    col2: t('jackpot.table.player'),
    col3: t('jackpot.table.turnover'),
    col4: t('jackpot.table.prize'),
}

// 获取历史数据
const fetchHistoryData = async (ago, forceRefresh = false) => {
    // 检查缓存（如果不强制刷新）
    if (!forceRefresh && historyDataCache.value[ago]) {
        currentHistoryData.value = historyDataCache.value[ago]
        isLoading.value = false
        return
    }

    try {
        isLoading.value = true

        const response = await store.getJackpotAction(
            {
                id: 'daylyJackpot',
                method: 'history',
                params: { ago },
            },
            false,
            false
        )

        if (response.code === ERROR_CODES.OK) {
            historyDataCache.value[ago] = response.data
            currentHistoryData.value = response.data
        } else {
            currentHistoryData.value = null
        }
    } catch (error) {
        console.error(`获取历史数据出错，ago: ${ago}`, error)
        currentHistoryData.value = null
    } finally {
        isLoading.value = false
    }
}

// 计算处理后的历史数据
const transformedHistoryData = computed(() => {
    // 解包 computed 和 readonly 包装的数据以获取奖励规则配置
    const jackpotDataValue = jackpotData.value
    const historyData = currentHistoryData.value

    if (!jackpotDataValue || !jackpotDataValue.rankReward) {
        return []
    }

    if (!historyData || !historyData.rank || !historyData.pool) {
        return []
    }

    // 奖励规则使用当前配置，但奖池金额使用历史数据
    return processPlayerList(historyData.rank, jackpotDataValue.rankReward, historyData.pool)
})

/**
 * 向前导航到更早的日期（Previous按钮）
 * 导航规则： 例如: DAYS_PER_CLICK = 2
 * 1. 正常情况下前进DAYS_PER_CLICK天（如1天前 → 3天前 → 5天前）
 * 2. 如果剩余天数不足DAYS_PER_CLICK，则只前进剩余的天数（如5天前 → 6天前，只前进1天）
 * 3. 确保不会超过MAX_HISTORY_DAYS（最大历史天数）
 */
const previousDate = () => {
    if (dateIndex.value < MAX_HISTORY_DAYS) {
        // 计算剩余可前进天数
        const remainingDays = MAX_HISTORY_DAYS - dateIndex.value

        // 如果剩余天数小于步长，则只前进剩余的天数
        // 例如：当前在5天前，MAX_HISTORY_DAYS=6，则只能再前进1天到6天前
        const actualStep = Math.min(DAYS_PER_CLICK, remainingDays)

        dateIndex.value += actualStep
        const ago = dateIndex.value

        emit('dateChange', dateIndex.value)

        fetchHistoryData(ago)
    }
}

/**
 * 向后导航到更近的日期（Next按钮）
 * 导航规则：
 * 1. 如果当前在非标准位置（如6天前），先回到上一个标准位置（5天前）
 * 2. 如果当前在标准位置（如5天前、3天前、1天前），则后退标准步长
 */
const nextDate = () => {
    if (dateIndex.value > 1) {
        // 检查是否在标准位置（1, 3, 5...）
        // 标准位置的特点：(日期索引-1) 能被步长整除
        const isAtStandardPosition = (dateIndex.value - 1) % DAYS_PER_CLICK === 0

        if (!isAtStandardPosition) {
            // 例如：当前在6天前，需要先回到5天前（上一个标准位置）
            // 计算当前位置属于哪个区间，然后回到该区间的起始位置
            // 例如：当DAYS_PER_CLICK=2时，区间是[1,2], [3,4], [5,6]...
            // 6在第3个区间[5,6]，所以回到5
            const currentGroup = Math.ceil(dateIndex.value / DAYS_PER_CLICK)
            const previousStandardPosition = (currentGroup - 1) * DAYS_PER_CLICK + 1
            dateIndex.value = previousStandardPosition
        } else {
            // 当前已在标准位置，正常后退DAYS_PER_CLICK天
            // 例如：从5天前 → 3天前 → 1天前
            dateIndex.value -= DAYS_PER_CLICK

            // 确保不会小于1（不会超过昨天）
            if (dateIndex.value < 1) {
                dateIndex.value = 1
            }
        }

        const ago = dateIndex.value

        emit('dateChange', dateIndex.value)

        fetchHistoryData(ago)
    }
}

// 组件挂载时初始化
onMounted(() => {
    fetchHistoryData(dateIndex.value, true)
})
</script>

<style lang="scss" scoped>
// 变量定义
$bg-primary: #2a3132;
$bg-secondary: #3a4142;
$bg-hover: #4a5152;
$text-primary: #ffffff;
$text-secondary: #b3bec1;

.history-page {
    height: 100%;
    background: $bg-primary;
    display: flex;
    flex-direction: column;
    color: $text-primary;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #292d2e;
}

.pop-title {
    // background: $bg-secondary;

    color: #fff;
    display: flex;
    align-items: center;
    font-size: 27.9122px;
    font-weight: 800;
    height: 97.6927px;
    justify-content: center;
    position: relative;

    .date-navigation-container {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .date-navigation {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .date-nav-button {
        width: 55.8244px;
        height: 55.8244px;
        border-radius: 10.4671px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 65, 66, 1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: none;

        &:hover:not(:disabled) {
            background-color: rgba(58, 65, 66, 0.8);
        }

        &:disabled {
            background-color: rgba(58, 65, 66, 0.4);
            cursor: not-allowed;
            opacity: 0.5;

            .nav-arrow-icon {
                opacity: 0.5;
            }
        }

        .nav-arrow-icon {
            width: 34.8902px;
            height: 34.8902px;
            color: #fff;
            fill: currentColor;

            &.nav-arrow-right {
                transform: rotate(180deg);
            }
        }
    }

    .current-date-text {
        font-size: 27.9122px;
        font-weight: 600;
        color: #fff;
        margin: 0;
        padding: 0 16px;
        white-space: nowrap;
        min-width: 100px;
        text-align: center;
    }

    .close-button {
        width: 55.8244px;
        height: 55.8244px;
        border-radius: 10.4671px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 65, 66, 1);
        transition: all 0.3s ease;
        margin-left: auto;
        margin-right: 27.9122px;

        &:hover {
            background-color: rgba(58, 65, 66, 0.8);
        }

        .close-icon {
            color: #fff;
            fill: currentColor;
            width: 25.121px;
            height: 25.121px;
        }
    }
}

.history-content {
    flex: 1;
    overflow-y: auto;
}
</style>
