<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-23 19:45:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 10:45:21
 * @FilePath     : /src/components/jackpot/JackpotPrize.vue
 * @Description  : Popup jackpot prize component (弹窗专用)
 * Copyright 2025 Bruce, All Rights Reserved.
-->
<template>
    <div class="trophy-container">
        <img src="@/assets/img/jackpot/trophy.png" class="trophy-image" />
        <div class="jackpot-info">
            <div class="jackpot-title">
                <img
                    class="leaf-icon"
                    src="data:image/png;base64,UklGRuYBAABXRUJQVlA4TNoBAAAvLwAOEF+BoG3beDt/rGew0FDQto3kPUCOP8ddGbdtI4nO9I9pdl6zhxFEso37Cf6rKiFRfgMngQD/7bT3QgKGQhyAAWUgrSoGI39BKsQ7QNAM0uJH1VVVZIGW5HhV9esDNaoqX6zpZt9f6905kG3btto22ytXurZDUsyYVnhu7v9/oUM6xceI/k+A/qz5pfNlicZXgs5Pllpg8FMO2kCS2xl3NhLNj0FqenkOEO5TrMzNE+9u5XZ2TBA28hobaWlDltvEDz1Klt3cw3IZA1s3G95N2Y0KAiST425xXGe5HQdp2ytfzcuGstl0FWQvM6AEark9chvMz6yAEEd5Hq2W+r0jaakXoptDGX9WSzi6scC7vRud39kqmxddihA7WTq6kTS7V6L2pNMOR2OABFzUOtEzt0dZeHOiE4Rlp4rai/KYtRwgSuZDUu7DTROdNJHbUZHGxxkglI0gyvYOZE9F2ui0h6SWvTRM9X7/ALUa6PWUbDqLkMrYNFDryN0w1VAAHHUpbhqIUylfjtVa6u9TsbjfQjLL07y/Asi5m5f3VVXFh6e46/IUrwBb5QSF3s2XSGXfZ936OkgVsH1P0plk3/a+QdrosxfKqQbTFyNv03zjSO1MMXr7Hwo="
                />
                <span class="title-text">{{ displayData.title }}</span>
                <img
                    class="leaf-icon mirror"
                    src="data:image/png;base64,UklGRuYBAABXRUJQVlA4TNoBAAAvLwAOEF+BoG3beDt/rGew0FDQto3kPUCOP8ddGbdtI4nO9I9pdl6zhxFEso37Cf6rKiFRfgMngQD/7bT3QgKGQhyAAWUgrSoGI39BKsQ7QNAM0uJH1VVVZIGW5HhV9esDNaoqX6zpZt9f6905kG3btto22ytXurZDUsyYVnhu7v9/oUM6xceI/k+A/qz5pfNlicZXgs5Pllpg8FMO2kCS2xl3NhLNj0FqenkOEO5TrMzNE+9u5XZ2TBA28hobaWlDltvEDz1Klt3cw3IZA1s3G95N2Y0KAiST425xXGe5HQdp2ytfzcuGstl0FWQvM6AEark9chvMz6yAEEd5Hq2W+r0jaakXoptDGX9WSzi6scC7vRud39kqmxddihA7WTq6kTS7V6L2pNMOR2OABFzUOtEzt0dZeHOiE4Rlp4rai/KYtRwgSuZDUu7DTROdNJHbUZHGxxkglI0gyvYOZE9F2ui0h6SWvTRM9X7/ALUa6PWUbDqLkMrYNFDryN0w1VAAHHUpbhqIUylfjtVa6u9TsbjfQjLL07y/Asi5m5f3VVXFh6e46/IUrwBb5QSF3s2XSGXfZ936OkgVsH1P0plk3/a+QdrosxfKqQbTFyNv03zjSO1MMXr7Hwo="
                />
            </div>
            <div class="prize-label">{{ displayData.prizeLabel }}</div>
            <div class="prize-amount">{{ curSymbol[wallet?.currency] }}{{ displayData.prizePool }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { computed, watch, onBeforeMount, onBeforeUnmount, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import eventBus, { EVENT_KEY } from '@/utils/bus'
import { useJackpotAnimation } from '@/hooks/useJackpotAnimation'

const props = defineProps<{}>()

const store = useBaseStore()
const { curSymbol, wallet } = storeToRefs(store)
const { t } = useI18n()

const jackpotStore = useJackpotStore()
const { jackpotData } = storeToRefs(jackpotStore)

// 使用动画组合式函数 - 弹窗配置：从0开始
const { currentPoolValue, startGrowth, stopGrowth, resetAnimation } = useJackpotAnimation({
    startFromZero: true, // 弹窗每次都从0开始
})

// 监听 jackpot 数据变化
watch(
    () => jackpotData.value?.pool,
    (newPool) => {
        if (newPool) {
            // 使用 resetAnimation 重置状态（从0开始）
            resetAnimation(newPool)

            // 延迟一点开始动画，确保界面已经渲染
            setTimeout(() => {
                startGrowth()
            }, 100)
        }
    },
    { immediate: true }
)

// 计算显示的数据
const displayData = computed(() => {
    return {
        title: t('jackpot.daily_jackpot'),
        prizeLabel: t('jackpot.prize_pool'),
        prizePool: currentPoolValue.value.toFixed(2),
    }
})

// 处理弹窗打开事件 - 主动重置状态
const handleViewEnter = () => {
    // 如果已经有数据，重置并开始动画
    if (jackpotData.value?.pool && jackpotData.value.pool > 0) {
        resetAnimation(jackpotData.value.pool)
        nextTick(() => {
            startGrowth()
        })
    } else {
        // 否则只是重置状态
        resetAnimation(0)
    }
}

const handleViewExit = () => {
    // 停止所有动画，确保值稳定
    stopGrowth()

    const finalValue = currentPoolValue.value

    if (finalValue > 0) {
        jackpotStore.setHomePagePool(finalValue)
    }
}

onBeforeMount(() => {
    eventBus.on(EVENT_KEY.JACKPOT_VIEW_ENTER, handleViewEnter)
    eventBus.on(EVENT_KEY.JACKPOT_VIEW_EXIT, handleViewExit)
})

onBeforeUnmount(() => {
    eventBus.off(EVENT_KEY.JACKPOT_VIEW_ENTER, handleViewEnter)
    eventBus.off(EVENT_KEY.JACKPOT_VIEW_EXIT, handleViewExit)
})
</script>

<style lang="scss" scoped>
.trophy-container {
    display: flex;
    align-items: center;
    height: 240px;
    justify-content: center;
    background-image: linear-gradient(to right, rgb(36 238 137 / 0.25), rgb(50 55 56 / 1));
    border-radius: 12px;
    padding: 15px;

    .trophy-image {
        width: 195.3854px;
        height: auto;
        margin-right: 10px;
    }

    .jackpot-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        margin-left: 60px;
        height: 100%;

        .jackpot-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .leaf-icon {
                width: 32px;
                height: auto;

                &:first-child {
                    margin-left: 30px;
                }

                &:last-child {
                    margin-right: 30px;
                }
                &.mirror {
                    transform: scaleX(-1);
                }
            }

            .title-text {
                font-size: 32px;
                font-weight: bold;
                color: #10c580; // 品牌绿色
                margin: 0 8px;
            }
        }

        .prize-label {
            font-size: 30px;
            color: #d3d0d0;
            margin-bottom: 8px;
        }

        .prize-amount {
            background-color: #232626;
            padding: 8px 20px;
            border-radius: 12px;
            font-size: 40px;
            font-weight: bolder;
            color: #29ef8a;
            min-width: 180px;
            text-align: center;
            width: 100%;
        }
    }
}
</style>
