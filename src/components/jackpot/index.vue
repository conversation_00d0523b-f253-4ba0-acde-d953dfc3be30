<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-19 14:17:07
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-21 14:03:07
 * @FilePath     : /src/components/jackpot/index.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-19 14:17:07
-->
<template>
    <div class="jackport-container">
        <div class="top-container">
            <div class="jackpot-title">
                <div class="indicator-wrapper">
                    <div class="indicator-dot"></div>
                    <div class="indicator-pulse"></div>
                </div>
                <div>{{ $t('jackpot.title') }}</div>
            </div>
            <span v-if="!showClaimCard" class="more-btn" @click="openJackpotView">
                {{ $t('jackpot.more') }}
                <svg class="more-icon" aria-hidden="true">
                    <use href="#icon-svg-JiantouL"></use>
                </svg>
            </span>
            <div v-if="!showClaimCard" class="nav-controls">
                <button class="nav-button" type="button" :disabled="currentSlide === 0" @click="prevSlide">
                    <svg class="arrow-icon" aria-hidden="true">
                        <use href="#icon-svg-JiantouL"></use>
                    </svg>
                </button>
                <button class="nav-button" type="button" :disabled="currentSlide === maxSlides - 1" @click="nextSlide">
                    <svg class="arrow-icon right" aria-hidden="true">
                        <use href="#icon-svg-JiantouL"></use>
                    </svg>
                </button>
            </div>
        </div>
        <RewardClaimCard v-if="showClaimCard" @claim="handleClaimSuccess"></RewardClaimCard>
        <van-swipe
            v-if="!showClaimCard"
            ref="swipeRef"
            class="jackport-swipe"
            :show-indicators="false"
            :loop="false"
            @change="onSwipeChange"
            v-model:current="currentSlide"
        >
            <van-swipe-item @click="openJackpotView">
                <HomeJackpotPrize />
            </van-swipe-item>
            <van-swipe-item @click="openJackpotView">
                <TimeChampion />
            </van-swipe-item>
            <van-swipe-item v-if="loggedIn" @click="openJackpotView">
                <VipCard />
            </van-swipe-item>
        </van-swipe>
    </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { isLoggedIn } from '@/utils'
import type { SwipeInstance } from 'vant'
import RewardClaimCard from './RewardClaimCard.vue'
import TimeChampion from './TimeChampion.vue'
import VipCard from './VipCard.vue'
import HomeJackpotPrize from './HomeJackpotPrize.vue'
import type { DaylyJackpotData } from './types'

const jackpotStore = useJackpotStore()
const { isRewardClaimed, jackpotData: storeJackpotData } = storeToRefs(jackpotStore)
const router = useRouter()

const loggedIn = computed(() => isLoggedIn())

const daylyJackpotData = computed<DaylyJackpotData | null>(() => {
    return storeJackpotData.value || null
})

const showClaimCard = computed(() => {
    return !!daylyJackpotData.value?.last && !isRewardClaimed.value
})

const handleClaimSuccess = () => {
    jackpotStore.setClaimDaylyJackpot()
}

// 轮播控制
const swipeRef = ref<SwipeInstance>()
const currentSlide = ref(0)
const maxSlides = computed(() => (loggedIn.value ? 3 : 2))

const prevSlide = () => {
    swipeRef.value?.prev()
}

const nextSlide = () => {
    swipeRef.value?.next()
}

const onSwipeChange = (index: number) => {
    currentSlide.value = index
}

const openJackpotView = () => {
    router.push('/jackpot')
}
</script>
<style lang="scss" scoped>
.jackport-container {
    margin: 0 auto;
    margin-bottom: 20px;
    // width: 720px;
    // padding: 0 15px;
    height: 300px;
    box-sizing: border-box;

    .top-container {
        margin-top: 27.9122px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        height: 55.8244px;
        padding: 10px 15px;
        background-color: transparent;
        border-radius: 8px;

        .jackpot-title {
            display: flex;
            align-items: center;
            font-size: 27.9122px;
            font-weight: 400;
            color: white;

            .indicator-wrapper {
                position: relative;
                margin: 0 12px 0 16px;
                height: 13.95px;
                width: 13.95px;
                margin-right: 13.95px;

                .indicator-dot {
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    border-radius: 50%;
                    background-color: #00cc66;
                }

                .indicator-pulse {
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    border-radius: 50%;
                    background-color: #00cc66;
                    animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
                }
            }
        }

        .more-btn {
            margin-left: auto;
            display: flex;
            align-items: center;
            background-color: rgba(58, 65, 66, 1);
            border-radius: 6px;
            padding: 0 8px;
            height: 55.8244px;
            font-size: 24.4232px;
            font-weight: 500;
            line-height: 55.8244px;
            border-radius: 13.9561px;
            padding-left: 13.9561px;
            padding-right: 13.9561px;
            color: #fff;

            .more-icon {
                margin-left: 6.978px;
                transform: rotate(180deg);
                width: 27.9122px;
                height: 27.9122px;
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .nav-controls {
            margin-left: 8px;
            display: flex;
            gap: 8px;

            .nav-button {
                width: 55.8244px;
                height: 55.8244px;
                border-radius: 10.4671px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(58, 65, 66, 1);
                border: none;
                cursor: pointer;
                transition: all 0.3s ease;

                &:disabled {
                    background-color: rgba(58, 65, 66, 0.4);
                    cursor: not-allowed;
                }

                .arrow-icon {
                    width: 27.9122px;
                    height: 27.9122px;
                    color: #fff;
                    transition: color 0.3s ease;

                    &.right {
                        transform: rotate(180deg);
                    }
                }
            }
        }

        @keyframes ping {
            75%,
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }
    }

    .jackport-swipe {
        :deep(.van-swipe__track) {
            gap: 15px;
        }
        :deep(.van-swipe-item) {
            width: calc(100vw - 30px) !important;
            margin-left: 15px;
        }
    }
}
</style>
