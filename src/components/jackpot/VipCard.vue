<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-23 19:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-22 10:59:29
 * @FilePath     : /src/components/jackpot/VipCard.vue
 * @Description  : Jackpot VIP card component
 * Copyright 2025 Bruce, All Rights Reserved.
-->
<template>
    <div class="vip-card">
        <div class="user-section">
            <img class="avatar" :src="vipData.avatar" />
            <div class="details">
                <div class="username-line">
                    {{ vipData.username }}
                    <div class="vip-badge">{{ vipData.vipLevel }}</div>
                </div>
                <div class="bet-info">
                    <template v-if="displayMode === 'first_place'">
                        <span class="congrats">
                            {{ $t('jackpot.vip.congrats_rank_one') }}
                        </span>
                    </template>

                    <template v-else-if="displayMode === 'in_ranking'">
                        {{ $t('jackpot.vip.bet') }}
                        <span class="amount">{{ curSymbol[wallet?.currency] }}{{ vipData.betDifference }}</span>
                        {{ $t('jackpot.vip.and_get') }}
                        <span class="amount">{{ curSymbol[wallet?.currency] }}{{ vipData.targetBet }}</span>
                    </template>

                    <template v-else>
                        {{ $t('jackpot.vip.bet') }}
                        <span class="amount">{{ curSymbol[wallet?.currency] }}{{ vipData.remainBet }}</span>
                        {{ $t('jackpot.vip.to_share_jackpot') }}
                    </template>
                </div>
            </div>
            <div v-if="showGoButton" class="button-container">
                <button type="button" class="go-bet-button" @click="emit('close')">
                    <span class="text">{{ $t('jackpot.vip.go_bet') }}</span>
                </button>
            </div>
        </div>
        <div class="stats-section">
            <div class="item">
                <div class="label">{{ $t('jackpot.vip.my_rank') }}</div>
                <div class="value">{{ vipData.myRank }}</div>
            </div>
            <div class="item">
                <div class="label">{{ $t('jackpot.vip.turnover') }}</div>
                <div class="value">{{ vipData.turnover }}</div>
            </div>
            <div class="item">
                <div class="label">{{ $t('jackpot.vip.prize') }}</div>
                <div class="value">{{ curSymbol[wallet?.currency] }} {{ vipData.prize }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { findRewardRate, calculatePrize, findHigherRewardTier, formatRate, floorToTwoDecimalsString } from './calculator'

const props = defineProps<{
    showGoButton?: boolean
}>()

const showGoButton = computed(() => props.showGoButton ?? false)

const emit = defineEmits<{
    close: []
}>()

const store = useBaseStore()
const jackpotStore = useJackpotStore()
const { jackpotData } = storeToRefs(jackpotStore)

const { curSymbol, userInfo, vipInfo, wallet } = storeToRefs(store)

// 是否是第一名
const isFirstPlace = computed(() => jackpotData.value?.myRank === 1)

// 是否进入了排行榜
const isInRanking = computed(() => {
    const myRank = jackpotData.value?.myRank || 0
    const rankLength = jackpotData.value?.rank?.length || 0
    return myRank > 0 && myRank <= rankLength
})

// 确定当前显示模式
const displayMode = computed(() => {
    if (isFirstPlace.value) {
        return 'first_place'
    } else if (isInRanking.value) {
        return 'in_ranking'
    } else {
        return 'not_in_ranking'
    }
})

// 计算奖金
const calculatePlayerPrize = (rank: number, pool: number, rankReward: any[]) => {
    if (rank === 0 || !rankReward || rankReward.length === 0) return '0'

    const rate = findRewardRate(rank, rankReward)
    if (rate === 0) return '0'

    const prize = calculatePrize(pool, rate)
    return prize.toFixed(2)
}

// 计算排名显示
const calculateRankDisplay = (myRank: number | undefined, rank: any[] | undefined): string | number => {
    // 1. 没有排行榜数据时显示 "-"
    if (!rank || rank.length === 0) {
        return '-'
    }

    // 2. 在排行榜范围内时显示实际排名
    if (myRank && myRank <= rank.length) {
        return myRank
    }

    // 3. 超出排行榜范围时显示 "长度+"
    if (myRank && myRank > rank.length) {
        return `${rank.length}+`
    }

    // 4. 其他情况（如 myRank 无效）显示 "-"
    return '-'
}

// 计算下一个奖励等级的差额和比例
const calculateNextTierInfo = () => {
    const jackpot = jackpotData.value
    if (!jackpot || !jackpot.myRank || !jackpot.rank || !jackpot.rankReward) {
        return { betDifference: 0, targetBet: 0 }
    }

    // 查找更高奖励比例的区间
    const nextTier = findHigherRewardTier(jackpot.myRank, jackpot.rank, jackpot.rankReward)
    if (!nextTier) {
        return { betDifference: 0, targetBet: 0 }
    }

    // 查找当前玩家数据
    const currentPlayer = jackpot.rank.find((p) => p.rank === jackpot.myRank)
    if (!currentPlayer) {
        return { betDifference: 0, targetBet: 0 }
    }

    // 计算投注额差值 (加1)
    const betDifference = Math.max(0, nextTier.player.bet - currentPlayer.bet) + 1

    // 计算奖金值 (使用 nextTier 的奖励比例计算奖金)
    const prize = calculatePrize(jackpot.pool, nextTier.rate)

    return {
        betDifference: floorToTwoDecimalsString(betDifference),
        targetBet: floorToTwoDecimalsString(prize),
    }
}

// 从 store 获取 VIP 卡片数据
const vipData = computed(() => {
    const jackpot = jackpotData.value

    const calculatedPrize = calculatePlayerPrize(jackpot?.myRank, jackpot?.pool, jackpot?.rankReward)

    const rankDisplay = calculateRankDisplay(jackpot?.myRank, jackpot?.rank)

    // 计算下一个奖励等级的信息
    const { betDifference, targetBet } = calculateNextTierInfo()

    return {
        avatar: userInfo.value?.avatar,
        username: userInfo.value?.uid,
        vipLevel: `VIP ${vipInfo.value?.curLevel || 0}`,
        remainBet: jackpot?.remainBet || 0,
        myRank: rankDisplay,
        turnover: jackpot?.myBet || 0,
        prize: calculatedPrize,
        betDifference,
        targetBet,
    }
})
</script>

<style lang="scss" scoped>
.vip-card {
    background-image: linear-gradient(to right, rgb(36 238 137 / 0.25), rgb(50 55 56 / 1));
    border-radius: 20.9341px;
    padding: 27.9122px;
    font-family: sans-serif;
    color: #e2e8f0;
    height: 237.2537px;

    .user-section {
        display: flex;
        position: relative;

        .avatar {
            width: 69.7805px;
            height: 69.7805px;
            border-radius: 50%;
            margin-right: 13.9561px;
            flex-shrink: 0;
            object-fit: contain;
        }

        .details {
            .username-line {
                font-weight: 800;
                color: #ffffff;
                font-size: 24.4232px;
            }

            .vip-badge {
                background: linear-gradient(79deg, #924813 1.67%, #c28255, #924813 96.05%);
                font-size: 20.9341px;
                line-height: 27.9122px;
                padding: 0 6.978px;
                display: inline-block;
                border-radius: 6.978px;
                height: 27.9122px;
            }

            .bet-info {
                font-size: 20.9341px;
                line-height: 27.9122px;

                .amount {
                    color: rgba(36, 238, 137, 1);
                    font-weight: bold;
                    margin: 0 6.978px;
                }

                .rate {
                    color: rgba(36, 238, 137, 1);
                    font-weight: bold;
                }

                .congrats {
                    color: rgba(36, 238, 137, 1);
                    font-weight: bold;
                }
            }
        }

        .button-container {
            position: absolute;
            right: 0;
            top: -15px;
            height: 100%;

            .go-bet-button {
                color: #000;
                border-radius: 10.4671px;
                padding: 6.978px 20.9341px;
                font-weight: 600;
                font-size: 20.9341px;
                line-height: 27.9122px;
                min-height: 41.8683px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 48.8464px;
                background-image: linear-gradient(90deg, #24ee89, #9fe871);
                background-color: #464f50;
                box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -2px #1dca6a;

                .text {
                    color: #000;
                    font-weight: 600;
                    font-size: 20.9341px;
                    line-height: 27.9122px;
                    margin: 0 6.978px;
                }
            }
        }
    }

    .stats-section {
        display: flex;
        padding-top: 13.9561px;
        margin-top: 27.9122px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        .item {
            flex: 1 1 0%;
            text-align: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            &:not(:first-child)::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 70%;
                border-left: 1px solid rgba(255, 255, 255, 0.1);
            }

            .label {
                color: #a0aec0;
                font-size: 24.4231px;
            }

            .value {
                color: #10c580;
                font-size: 27.9122px;
                line-height: 41.8683px;
                font-weight: 800;
            }
        }
    }
}
</style>
