/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-01-16 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-14 10:14:16
 * @FilePath     : /src/components/jackpot/types.ts
 * @Description  : Jackpot 组件统一类型定义
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// ========== 基础数据类型 ==========

/**
 * 奖励配置类型定义
 */
export interface RankReward {
  min: number
  max: number
  rate: number
}

/**
 * 玩家排行数据类型定义
 */
export interface PlayerRankData {
  rank: number
  uid: string
  nickname?: string
  bet: number
}

/**
 * 上次获奖者信息
 */
export interface LastWinner {
  uid: string
  nickname: string
  bet: number
  avatar: string
  prize: number
}

// ========== 主要数据类型 ==========

/**
 * Jackpot 主数据类型定义
 */
export interface DaylyJackpotData {
  /** 当前服务器时间 */
  time: string | Date
  /** 下次结算时间 */
  settleTime: string | Date
  /** 流水倍数 */
  flowmult: number
  ignoreGames: string[]
  /** 排名奖励配置 */
  rankReward: RankReward[]
  /** 当前奖池金额 */
  pool: number
  /** 当前用户投注额 */
  myBet: number
  myRank: number
  /** 可参与派奖的Bet差额 */
  remainBet: number
  rankSize: number
  /** 实时排行榜 */
  rank?: PlayerRankData[]
  /** 上次获奖者信息 */
  lastWinner?: LastWinner
  /** 上次结算当前用户信息 */
  last?: {
    rank: number
    day: string
    bet: number
    remainRank: number
    reward: number
    remainBet: number
  }
  /** 其他可能的字段 */
  [key: string]: any
}

// ========== 处理后的数据类型 ==========

/**
 * 处理后的玩家列表数据类型 (用于 RankingList 组件)
 */
export interface ProcessedPlayerData {
  /** 排名 */
  col1: number
  /** 玩家信息 (ID/昵称) */
  col2: string
  /** 投注额 */
  col3: number
  /** 奖金 */
  col4: number
  /** 奖励比例 (格式化后的百分比字符串) */
  rate: string
}

/**
 * 表头配置类型
 */
export interface TableHeaders {
  col1: string
  col2: string
  col3: string
  col4: string
}
