<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-25 19:29:28
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 19:18:32
 * @FilePath     : /src/components/jackpot/MyRewards.vue
 * @Description  : My Rewards 组件，显示用户的奖励记录
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-25 19:29:28
-->

<template>
    <div class="my-rewards-page">
        <div class="main-container">
            <div class="pop-title">
                <div class="title-container">
                    <h2 class="title-text">{{ $t('jackpot.navigation.my_rewards') }}</h2>
                </div>
                <button type="button" class="close-button" @click="$emit('close')">
                    <svg class="close-icon" aria-hidden="true">
                        <use href="#icon-svg-Close" fill=""></use>
                    </svg>
                </button>
            </div>

            <div class="rewards-content">
                <RankingList
                    :data-list="transformedRewardsData"
                    :headers="tableHeaders"
                    :show-avatars="true"
                    :avatar-count="1"
                    :show-rate="false"
                    :zebra-stripe-pattern="false"
                    display-mode="rewards"
                    :loading="isLoading"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import RankingList from './RankingList.vue'
import { useBaseStore } from '@/stores'
import { ERROR_CODES } from '@/enums'
import { formatDateOnly } from '@/utils/date'
import { useI18n } from 'vue-i18n'
import { floorToTwoDecimalsString } from './calculator'

const emit = defineEmits(['close'])

const store = useBaseStore()
const { t } = useI18n()

const isLoading = ref(true)

const tableHeaders = {
    col1: t('jackpot.table.rank'),
    col2: t('jackpot.table.status'),
    col3: t('jackpot.table.prize'),
    col4: t('jackpot.table.time'),
}

// 我的奖励数据
const rewardsData = ref([])

// 获取个人奖励记录
const fetchPersonalRewards = async () => {
    try {
        isLoading.value = true

        const response = await store.getJackpotAction(
            {
                id: 'daylyJackpot',
                method: 'record',
                params: { from: 0, size: 100 },
            },
            false,
            false
        )

        if (response.code === ERROR_CODES.OK && response.data) {
            rewardsData.value = response.data
        } else {
            console.error('获取个人奖励记录失败:', response)
            rewardsData.value = []
        }
    } catch (error) {
        rewardsData.value = []
    } finally {
        isLoading.value = false
    }
}

// 转换数据格式以匹配 RankingList 组件
const transformedRewardsData = computed(() => {
    return rewardsData.value.map((item) => {
        // 将 "20250708" 格式转换为 "2025/07/08" 格式
        const formattedDate = formatDateOnly(`${item.day.substring(0, 4)}-${item.day.substring(4, 6)}-${item.day.substring(6, 8)}`, '/')

        return {
            col1: item.rank,
            col2: item?.status === 1 ? t('jackpot.status_text.received') : t('jackpot.status_text.not_received'),
            col3: floorToTwoDecimalsString(item.reward),
            col4: formattedDate,
        }
    })
})

onMounted(() => {
    fetchPersonalRewards()
})
</script>

<style lang="scss" scoped>
$bg-primary: #2a3132;
$bg-secondary: #3a4142;
$bg-hover: #4a5152;
$text-primary: #ffffff;
$text-secondary: #b3bec1;

.my-rewards-page {
    height: 100%;
    background: $bg-primary;
    display: flex;
    flex-direction: column;
    color: $text-primary;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #292d2e;
}

.pop-title {
    color: #fff;
    display: flex;
    align-items: center;
    font-size: 27.9122px;
    font-weight: 800;
    height: 97.6927px;
    justify-content: center;
    position: relative;

    .title-container {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .title-text {
        font-size: 27.9122px;
        font-weight: 600;
        color: #fff;
        margin: 0;
        padding: 0;
        white-space: nowrap;
        text-align: center;
    }

    .close-button {
        width: 55.8244px;
        height: 55.8244px;
        border-radius: 10.4671px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 65, 66, 1);
        transition: all 0.3s ease;
        margin-left: auto;
        margin-right: 27.9122px;

        &:hover {
            background-color: rgba(58, 65, 66, 0.8);
        }

        .close-icon {
            color: #fff;
            fill: currentColor;
            width: 25.121px;
            height: 25.121px;
        }
    }
}

.rewards-content {
    flex: 1;
    overflow-y: auto;
}
</style>
