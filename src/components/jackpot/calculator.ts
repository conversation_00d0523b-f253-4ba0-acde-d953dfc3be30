/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-08 14:05:29
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 17:11:41
 * @FilePath     : /src/components/jackpot/calculator.ts
 * @Description  : 奖池计算相关工具函数
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-08 14:05:29
 */
import { decodeName } from '@/utils'
import type {
    RankReward,
    PlayerRankData as PlayerData
} from './types'

// 临时定义ProcessedPlayerData接口，修改col3和col4为string类型
interface ProcessedPlayerData {
    col1: number;
    col2: string;
    col3: string; // 修改为string类型
    col4: string; // 修改为string类型
    rate: string;
}

/**
 * 格式化数字，保留两位小数并向下取整
 * @param value 需要格式化的数值
 * @returns 格式化后的数值，保留两位小数
 */
export function floorToTwoDecimals(value: number): number {
    // 将数值乘以100，向下取整，然后除以100，实现保留两位小数并向下取整
    return Math.floor(value * 100) / 100;
}

/**
 * 格式化数字为字符串，保留两位小数并向下取整
 * @param value 需要格式化的数值
 * @returns 格式化后的字符串，始终显示两位小数
 */
export function floorToTwoDecimalsString(value: number): string {
    const formattedValue = floorToTwoDecimals(value);
    // 然后使用toFixed(2)确保显示两位小数
    return formattedValue.toFixed(2);
}

/**
 * 根据排名查找对应的奖励比例
 * @param rank 排名
 * @param rankReward 奖励配置数组
 * @returns 奖励比例，未找到则返回0
 */
export function findRewardRate(rank: number, rankReward: RankReward[]): number {
    const reward = rankReward.find(item => rank >= item.min && rank <= item.max)
    return reward ? reward.rate : 0
}

/**
 * 查找更高奖励比例的等级区间
 * @param myRank 当前排名
 * @param playerList 玩家列表
 * @param rankReward 奖励配置数组
 * @returns 更高奖励比例区间的目标排名和比例，如果没找到则返回null
 */
export function findHigherRewardTier(
    myRank: number,
    playerList: PlayerData[],
    rankReward: RankReward[]
): { player: PlayerData; rate: number } | null {
    // 如果是第一名，无需查找
    if (myRank <= 1) {
        return null
    }

    // 查找当前排名对应的奖励区间
    const currentRewardIndex = rankReward.findIndex(
        item => myRank >= item.min && myRank <= item.max
    )

    if (currentRewardIndex <= 0) {
        return null // 已经是最高级别或没找到当前排名的区间
    }

	const lastReward = rankReward[currentRewardIndex-1]
	const targetPlayer = playerList.find(p => p.rank === lastReward.max)

    return {
        player: targetPlayer,
        rate: lastReward.rate
    }
}

/**
 * 计算具体奖金
 * @param pool 奖池总金额
 * @param rate 奖励比例
 * @returns 计算后的奖金，保留两位小数并向下取整
 */
export function calculatePrize(pool: number, rate: number): number {
    // 计算原始值
    const rawValue = pool * rate;
    // 使用formatNumber函数格式化
    return floorToTwoDecimals(rawValue);
}

/**
 * 格式化显示比例
 * @param rate 比例（0-1之间的小数）
 * @returns 格式化后的比例字符串，保留两位小数
 */
export function formatRate(rate: number): string {
    return (rate * 100).toFixed(2)
}

/**
 * 处理玩家列表数据，计算奖金和百分比
 * @param playerList 原始玩家数据列表
 * @param rankReward 奖励规则配置
 * @param pool 奖池金额
 * @returns 处理后的玩家数据列表
 */
export function processPlayerList(
    playerList: PlayerData[],
    rankReward: RankReward[],
    pool: number
): ProcessedPlayerData[] {
    if (!playerList || !Array.isArray(playerList)) {
        console.warn('Invalid player list provided')
        return []
    }

    if (!rankReward || !Array.isArray(rankReward) || !pool) {
        console.warn('Invalid rank reward or pool provided')
        return []
    }

    return playerList.map(player => {
        // 根据排名查找奖励比例
        const rate = findRewardRate(player.rank, rankReward)

        // 计算具体奖金
        const prize = calculatePrize(pool, rate)

        // 格式化百分比
        const formattedRate = formatRate(rate)

        return {
            col1: player.rank,
            col2: decodeName(player.nickname) || player.uid || '',
            col3: floorToTwoDecimalsString(player.bet),
            col4: floorToTwoDecimalsString(prize),
            rate: formattedRate
        }
    })
}




