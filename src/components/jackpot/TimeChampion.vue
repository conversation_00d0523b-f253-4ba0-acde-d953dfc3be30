<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-23 15:46:06
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 00:06:06
 * @FilePath     : /src/components/jackpot/TimeChampion.vue
 * @Description  : Time Remaining 和 Last Champion 双卡片组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-23 15:46:06
-->
<template>
    <div class="jackpot-cards-container">
        <div class="time-card">
            <div class="card-title">{{ $t('jackpot.time.remaining') }}</div>
            <div class="time-display">
                <div class="time-unit">
                    <div class="time-value">{{ `${hours}`.padStart(2, '0') }}</div>
                    <div class="time-label">{{ $t('jackpot.time.hour') }}</div>
                </div>
                <b class="time-separator">:</b>
                <div class="time-unit">
                    <div class="time-value">{{ `${minutes}`.padStart(2, '0') }}</div>
                    <div class="time-label">{{ $t('jackpot.time.minute') }}</div>
                </div>
                <b class="time-separator">:</b>
                <div class="time-unit">
                    <div class="time-value">{{ `${seconds}`.padStart(2, '0') }}</div>
                    <div class="time-label">{{ $t('jackpot.time.second') }}</div>
                </div>
            </div>
        </div>
        <div class="champion-card" v-if="showLastChampion">
            <div class="winner-badge">
                <img
                    width="129"
                    height="117"
                    src="data:image/png;base64,UklGRoACAABXRUJQVlA4THQCAAAvgAAdEEehoG0jx78Hhef/MO9oKGzbtlGS9qb+/9Wu1DaS1Ow/NoKcEui/OX8IItnG/QReAypoQBJRjQY/wo/wGgjAH+ODSk0130QFZ/nqkcT69XQryR9vTc5rnN/I+vuefruF8QswC5kYZuACCBwoSAFXaEChIlPZwj9N4NGECyXd0lyGLNu227a57r09PKgQlEQJkh/mP8X03Hf/I/o/AeAPN9r3pztk2IvRvl1dXT0kcDLe16sfr+/Ydsbb7n7JNbbGOyPFqEZbFqTY3WhLR4rHYrQeSHFnvB5IcW+8NZDhWBlvHcgw3HgnpHguxntAiicjbkhxZ8QNGY61ES/IMKrxlgsy7G683pHhsRivBzLcGbEHMtwacR1IMKoRTwMJhhvxARmeixE3ZHgy5oYM98a8IMGxMuJyQYLhRuwdCXY3Yg8keCxG7IEEd8ZcB/jH1pg3A/xRjXlCguHG3JDguRhzQ4Ino56R4N6YywL+sTLm0sEfbswe4O9uzB7gPxl1DfDvjHo1QD+2Rj2BP6pRN/B3N+oG/mMx6hn8O+NewL836tJBP1ZG7R304UbtAfpzMWoP0J+Muw7Q74x7GmAfW+M+gD6qcTfwu3E3JGjcC8SVC8R5hzgPiPOAuDogbhoQNyFTjgZ1DeoWiCvfEFc6xHlAnAfE1YC41YC4CUn/Rw3qGtTNEFcWiCsd4jwgzgPiakBcHRA3If9/dYC6BnUN6haIKxeI8w5xHhDnAXF1QNw0IO4ArX+jQV2DugXiygXiSoc4D4jzgLg6IG4zIG6C7F80qGtQN0NcWSDu6wPiPl9uxX2+XKl7vpJ3eyMP9/rwqE89"
                />
                <div class="winner-text">{{ $t('jackpot.champion.winner') }}</div>
            </div>
            <div class="card-title">{{ $t('jackpot.champion.last') }}</div>
            <div class="champion-info">
                <div class="avatar-container">
                    <img
                        class="crown-icon"
                        width="66"
                        height="30"
                        src="data:image/png;base64,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"
                    />
                    <img class="avatar-image" width="131" height="131" :src="championData.avatar" />
                </div>
                <div class="info-text">
                    <div class="champion-name">{{ decodeName(championData.name) }}</div>
                    <div class="champion-bet">
                        {{ championData.bet }}
                        <!-- <span class="bet-percentage">({{ championData.rate }})</span> -->
                    </div>
                    <div class="champion-prize">
                        <span>{{ curSymbol[wallet?.currency] }}{{ championData.prize }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { getTimeDiffMs } from '@/utils/date'
import { decodeName } from '@/utils'

const store = useBaseStore()
const jackpotStore = useJackpotStore()
const { curSymbol, wallet } = storeToRefs(store)
const { jackpotData } = storeToRefs(jackpotStore)

// 倒计时状态
const hours = ref(0)
const minutes = ref(0)
const seconds = ref(0)
const countdownTimer = ref<number | null>(null)
const totalRemainingMs = ref(0)

const championData = computed(() => {
    const lastWinner = jackpotData.value?.lastWinner

    return {
        name: lastWinner?.nickname || '',
        uid: lastWinner?.uid || '',
        bet: Math.round(lastWinner?.bet || 0),
        // rate: '20.0%',
        prize: lastWinner?.prize || '0',
        avatar: lastWinner?.avatar,
    }
})

const showLastChampion = computed(() => {
    return !!jackpotData.value?.lastWinner
})

// 初始化倒计时（计算固定时间差）
const initCountdown = () => {
    if (!jackpotData.value?.settleTime || !jackpotData.value?.time) return

    // 计算初始时间差值
    totalRemainingMs.value = getTimeDiffMs(jackpotData.value.settleTime, jackpotData.value.time)

    updateTimeDisplay()
}

// 更新倒计时时分秒显示
const updateTimeDisplay = () => {
    if (totalRemainingMs.value <= 0) {
        hours.value = 0
        minutes.value = 0
        seconds.value = 0
        if (countdownTimer.value) {
            clearInterval(countdownTimer.value)
            countdownTimer.value = null
        }
        return
    }

    // 转换毫秒为时分秒
    const totalSeconds = Math.floor(totalRemainingMs.value / 1000)
    hours.value = Math.floor(totalSeconds / 3600)
    minutes.value = Math.floor((totalSeconds % 3600) / 60)
    seconds.value = totalSeconds % 60
}

// 倒计时递减逻辑
const countdown = () => {
    if (totalRemainingMs.value > 0) {
        totalRemainingMs.value -= 1000
        updateTimeDisplay()
    } else {
        // 倒计时结束
        if (countdownTimer.value) {
            clearInterval(countdownTimer.value)
            countdownTimer.value = null
        }
    }
}

const startCountdown = () => {
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
    }

    initCountdown()

    // 每秒递减倒计时
    countdownTimer.value = setInterval(countdown, 1000)
}

watch(
    () => jackpotData.value,
    (newData) => {
        if (newData?.settleTime && newData?.time) {
            startCountdown()
        }
    },
    { immediate: true }
)

onMounted(() => {
    if (jackpotData.value?.settleTime && jackpotData.value?.time) {
        startCountdown()
    }
})

onUnmounted(() => {
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
    }
})
</script>

<style lang="scss" scoped>
.jackpot-cards-container {
    display: flex;
    gap: 20.9341px;
    height: 237.2537px;
    flex-wrap: wrap;
}

.time-card,
.champion-card {
    flex: 1 1 0%;
    background-color: rgba(58, 65, 66, 1);
    border-radius: 12px;
    padding: 34.8902px 0;

    border-radius: 20.9341px;
}
.time-card {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-title {
    font-size: 27.9122px;
    line-height: 41.8683px;
    font-weight: 400;
    margin-bottom: 20.9341px;
    color: rgba(36, 238, 137, 1);
    text-align: center;

    &:nth-child(2) {
        margin-bottom: 27.9122px;
    }
}

.time-card {
    .time-display {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .time-unit {
        width: 83.7366px;
        height: 104.6707px;
        background-color: rgba(35, 38, 38, 1);
        border-radius: 20.9341px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 6.978px 0;
    }

    .time-value {
        font-size: 34.8902px;
        line-height: 48.8464px;
        font-weight: 800;
        color: #ffffff;
    }

    .time-label {
        font-size: 20.9341px;
        line-height: 27.9122px;
        transform: scale(0.833);
        color: rgba(179, 190, 193, 1);
    }

    .time-separator {
        color: rgba(255, 255, 255, 0.4);
        font-size: 24px;
        font-weight: bold;
    }
}

.champion-card {
    position: relative;

    .winner-badge {
        position: absolute;
        top: -3.489px;
        left: -3.489px;
        z-index: 1;

        img {
            width: 111.6488px;
            height: auto;
        }

        .winner-text {
            position: absolute;
            color: #000;
            font-weight: 800;
            font-size: 20.9341px;
            transform: rotate(-42deg);
            top: 24px;
            left: -10px;
            text-align: center;
            width: 100%;
        }
    }

    .champion-info {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 34.8902px;
    }

    .avatar-container {
        position: relative;
        margin-top: 20.9341px;
        margin-right: 13.9561px;

        .crown-icon {
            position: absolute;
            width: 55.8244px;
            height: auto;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
        }

        .avatar-image {
            width: 69.7805px;
            height: 69.7805px;
            border-radius: 9999px;
        }
    }

    .info-text {
        font-size: 20.9341px;
        line-height: 27.9122px;
        text-align: left;
    }

    .champion-name {
        color: #ffffff;
    }

    .champion-bet {
        color: rgba(179, 179, 193, 1);
        .bet-percentage {
            color: #ffffff;
            margin-left: 6.978px;
        }
    }

    .champion-prize {
        color: #10c580;
        font-weight: 500;
    }
}
</style>
