<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-25 19:29:28
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 16:45:53
 * @FilePath     : /src/components/jackpot/Rules.vue
 * @Description  : Rules 弹窗组件，展示 Jackpot of The Day 的游戏规则和奖励计算公式
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-25 19:29:28
-->

<template>
    <div class="rules-popup">
        <div class="rules-header">
            <h2 class="rules-title">{{ $t('jackpot.rules.title') }}</h2>
            <button type="button" class="close-button" @click="$emit('close')">
                <svg class="close-icon" aria-hidden="true">
                    <use href="#icon-svg-Close"></use>
                </svg>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="rules-content">
            <!-- 规则文本区域 -->
            <div class="rules-text-section">
                <h2 class="content-title">{{ $t('jackpot.rules.subtitle') }}</h2>
                <ol class="rules-list">
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_1') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_2') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_3') }}
                        <ul type="a" class="sub-rules">
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_1') }}</li>
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_2') }}</li>
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_3') }}</li>
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_4') }}</li>
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_5') }}</li>
                            <li class="sub-rule-item">{{ $t('jackpot.rules.rule_3_sub_6') }}</li>
                        </ul>
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_4') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_5') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_6') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_7') }}
                    </li>
                    <li class="rule-item">
                        {{ $t('jackpot.rules.rule_8') }}
                    </li>
                </ol>

                <p class="blessings-text">{{ $t('jackpot.rules.good_luck') }}</p>
            </div>

            <!-- 奖励计算区域 -->
            <div class="prize-calculation-section">
                <h2 class="content-title">{{ $t('jackpot.rules.prize_calculation') }}</h2>
                <ol class="prize-calculation-list">
                    <!-- 个人名次奖励 -->
                    <li v-for="item in prizeCalculationData" :key="item.place" class="prize-item">
                        <span class="place">{{ item.place }}</span> {{ $t('jackpot.rules.place_suffix') }} -
                        <span class="percentage">{{ item.percentage }}</span> {{ $t('jackpot.rules.of_jackpot_pool') }}
                    </li>

                    <!-- 范围奖励 -->
                    <li v-for="item in rangeRewardsData" :key="item.range" class="prize-item">
                        <span class="place">{{ item.range }}</span> {{ $t('jackpot.rules.place_suffix') }} -
                        <span class="percentage">{{ item.percentage }}</span> {{ $t('jackpot.rules.of_jackpot_pool') }}
                    </li>
                </ol>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'

interface RankReward {
    min: number
    max: number
    rate: number
}

// 使用 jackpot store
const jackpotStore = useJackpotStore()
const { jackpotData } = storeToRefs(jackpotStore)

defineEmits<{
    close: []
}>()

// 序数词转换函数
const getOrdinal = (num: number): string => {
    if (num === 1) {
        return '1st'
    }
    if (num === 2) {
        return '2nd'
    }
    if (num === 3) {
        return '3rd'
    }

    return num + 'th'
}

// 百分比格式化函数
const formatPercentage = (rate: number): string => {
    const percentage = rate * 100
    if (percentage >= 0.1) {
        return `${percentage.toFixed(2)}%`
    } else {
        return `${(percentage * 10).toFixed(2)}‰`
    }
}

// 个人名次奖励数据 (max - min == 0)
const prizeCalculationData = computed(() => {
    const rankReward = jackpotData.value?.rankReward || []
    return rankReward
        .filter((item) => item.max - item.min === 0)
        .map((item) => ({
            place: getOrdinal(item.min),
            percentage: formatPercentage(item.rate),
        }))
})

// 范围奖励数据 (max - min > 0)
const rangeRewardsData = computed(() => {
    const rankReward = jackpotData.value?.rankReward || []
    return rankReward
        .filter((item) => item.max - item.min > 0)
        .map((item) => ({
            range: `${getOrdinal(item.min)}-${getOrdinal(item.max)}`,
            percentage: formatPercentage(item.rate),
        }))
})
</script>

<style lang="scss" scoped>
.rules-popup {
    height: 100%;
    background: #292d2e;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .rules-header {
        background: #292d2e;
        color: #fff;
        display: flex;
        align-items: center;
        font-size: 27.9122px;
        font-weight: 800;
        height: 97.6927px;
        justify-content: center;
        flex-shrink: 0;
        position: relative;

        .rules-title {
            color: #ffffff;
            font-size: 27.9122px;
            font-weight: 600;
            margin: 0;
            padding: 0;
            white-space: nowrap;
            text-align: center;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .close-button {
            width: 55.8244px;
            height: 55.8244px;
            border-radius: 10.4671px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(58, 65, 66, 1);
            margin-left: auto;
            margin-right: 27.9122px;

            .close-icon {
                fill: #fff;
                width: 25.121px;
                height: 25.121px;
            }
        }
    }

    .rules-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px 30px;

        .content-title {
            color: #ffffff;
            font-size: 27.9122px;
            font-weight: 800;
            line-height: 41.8683px;
        }

        .rules-text-section {
            margin-bottom: 24px;

            .rules-list {
                color: #fff;
                padding: 27.9122px;
                margin: 0;
                list-style-type: decimal;
                list-style-position: outside;

                .rule-item {
                    margin-bottom: 25.121px;
                    font-size: 24.4232px;
                    line-height: 37.6815px;
                    font-weight: 600;
                    display: list-item;

                    &::marker {
                        color: #fff;
                        font-weight: 600;
                        unicode-bidi: isolate;
                        font-variant-numeric: tabular-nums;
                        text-transform: none;
                        text-indent: 0px !important;
                        text-align: start !important;
                        text-align-last: auto !important;
                    }
                    .sub-rules {
                        margin-top: 8px;
                        margin-left: 20px;

                        .sub-rule-item {
                            margin-bottom: 4px;
                            font-size: 24px;
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        .blessings-text {
            color: rgba(36, 238, 137, 1);
            font-size: 31.4012px;
            line-height: 48.8464px;
            font-weight: 800;
            margin-bottom: 27.9122px;
        }

        .prize-calculation-section {
            margin-top: 24px;

            .prize-calculation-list {
                margin-top: 8px;
                margin-left: 12px;
                list-style-type: disc;
                .prize-item {
                    white-space: nowrap;
                    margin-bottom: 25.121px;
                    color: #fff;
                    font-size: 27px;
                    line-height: 22.3298px;
                    font-weight: 600;

                    .percentage {
                        color: rgba(36, 238, 137, 1);
                    }
                }
            }
        }
    }
}
</style>
