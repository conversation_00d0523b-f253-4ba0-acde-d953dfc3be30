<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-11 10:26:09
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 10:35:03
 * @FilePath     : /src/components/jackpot/RewardClaimCard.vue
 * @Description  : 获奖信息展示组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-11 10:26:09
-->

<template>
    <div class="winner-card">
        <div class="you-win-badge">
            <img
                src="data:image/webp;base64,UklGRoACAABXRUJQVlA4THQCAAAvgAAdEEehoG0jx78Hhef/MO9oKGzbtlGS9qb+/9Wu1DaS1Ow/NoKcEui/OX8IItnG/QReAypoQBJRjQY/wo/wGgjAH+ODSk0130QFZ/nqkcT69XQryR9vTc5rnN/I+vuefruF8QswC5kYZuACCBwoSAFXaEChIlPZwj9N4NGECyXd0lyGLNu227a57r09PKgQlEQJkh/mP8X03Hf/I/o/AeAPN9r3pztk2IvRvl1dXT0kcDLe16sfr+/Ydsbb7n7JNbbGOyPFqEZbFqTY3WhLR4rHYrQeSHFnvB5IcW+8NZDhWBlvHcgw3HgnpHguxntAiicjbkhxZ8QNGY61ES/IMKrxlgsy7G683pHhsRivBzLcGbEHMtwacR1IMKoRTwMJhhvxARmeixE3ZHgy5oYM98a8IMGxMuJyQYLhRuwdCXY3Yg8keCxG7IEEd8ZcB/jH1pg3A/xRjXlCguHG3JDguRhzQ4Ino56R4N6YywL+sTLm0sEfbswe4O9uzB7gPxl1DfDvjHo1QD+2Rj2BP6pRN/B3N+oG/mMx6hn8O+NewL836tJBP1ZG7R304UbtAfpzMWoP0J+Muw7Q74x7GmAfW+M+gD6qcTfwu3E3JGjcC8SVC8R5hzgPiPOAuDogbhoQNyFTjgZ1DeoWiCvfEFc6xHlAnAfE1YC41YC4CUn/Rw3qGtTNEFcWiCsd4jwgzgPiakBcHRA3If9/dYC6BnUN6haIKxeI8w5xHhDnAXF1QNw0IO4ArX+jQV2DugXiygXiSoc4D4jzgLg6IG4zIG6C7F80qGtQN0NcWSDu6wPiPl9uxX2+XKl7vpJ3eyMP9/rwqE89"
            />
            <div class="winner-text">{{ $t('jackpot.winner.you_win') }}</div>
        </div>
        <div class="winner-content">
            <!-- 左侧：头像 -->
            <div class="left-section">
                <div class="avatar-container">
                    <img src="@/assets/img/jackpot/icon_crown.avif" alt="Crown" class="crown-image" />
                    <img :src="avatarSrc" alt="" class="avatar-image" />
                </div>
            </div>

            <!-- 右侧：获奖详情 -->
            <div class="right-section">
                <div class="rank-info">
                    <img src="@/assets/img/jackpot/icon_wheat.avif" alt="decoration" class="olive-branch" />
                    <div class="rank-text">No.{{ winnerData.rank }}</div>
                    <img src="@/assets/img/jackpot/icon_wheat.avif" alt="decoration" class="olive-branch right" />
                </div>
                <div class="prize-details">
                    <div class="you-get-text">{{ $t('jackpot.winner.you_get') }}</div>
                    <div class="prize-amount">
                        <span class="currency-symbol">{{ curSymbol[wallet?.currency] }}</span
                        >{{ formattedPrize }}
                    </div>
                    <button class="claim-button" @click="handleClaim" :disabled="isLoading">
                        {{ isLoading ? $t('jackpot.winner.claiming') : $t('jackpot.winner.claim') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { formatDate } from '@/utils/date'

const { t } = useI18n()

const store = useBaseStore()
const jackpotStore = useJackpotStore()
const { userInfo, wallet, curSymbol } = storeToRefs(store)
const { jackpotData } = storeToRefs(jackpotStore)

interface WinnerData {
    uid?: string
    nickname?: string
    reward?: number
    rank?: number
    day?: string
    bet?: number
    remainRank?: number
    remainBet?: number
}

const emit = defineEmits(['claim'])

const isLoading = ref(false)

const avatarSrc = computed(() => {
    return userInfo.value?.avatar || ''
})

const winnerData = computed(() => {
    return jackpotData.value?.last || {}
})

const formattedPrize = computed(() => {
    const amount = winnerData.value?.reward
    return `${(amount ?? 0).toFixed(2)}`
})

// 处理领奖按钮点击
const handleClaim = async () => {
    if (isLoading.value) return
    isLoading.value = true
    try {
        const day = formatDate(winnerData.value.day, 'YYYYMMDD')
        const res = await store.ReceiveRewards({
            id: 'daylyJackpot',
            content: {
                day,
            },
        })

        if (res.code === 200) {
            showSuccessToast(t('message_claim_success'))
            emit('claim')
        } else {
            showFailToast(t('message_claim_fail'))
        }
    } catch (error) {
        showFailToast(t('message_claim_fail'))
    } finally {
        isLoading.value = false
    }
}
</script>

<style lang="scss" scoped>
.winner-card {
    position: relative;
    box-sizing: border-box;
    width: 720px;
    height: 240px;
    background: #1e4a3a;
    border-radius: 20px;
    overflow: visible;
    color: white;
    font-family: sans-serif;
    background-image: url('@/assets/img/jackpot/bg_dayjackpot.avif');
    background-size: cover;
    background-position: center;
}

.you-win-badge {
    position: absolute;
    top: -3px;
    left: -3px;
    z-index: 2;

    img {
        width: 112px;
        height: auto;
    }

    .winner-text {
        position: absolute;
        color: #1e4a3a;
        font-weight: 800;
        font-size: 18px;
        transform: rotate(-42deg);
        top: 20px;
        left: -12px;
        text-align: center;
        width: 100%;
    }
}

.winner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    height: 100%;
}

.left-section {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    .avatar-container {
        position: relative;
        width: 190px;
        height: 190px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .crown-image {
        position: absolute;
        top: -30px;
        left: 80%;
        transform: translateX(-50%) rotate(10deg);
        width: 120px;
        height: auto;
        z-index: 1;
    }

    .avatar-image {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid rgba(255, 255, 255, 0.4);
    }
}

.right-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 100%;
    padding: 10px 0;
    margin-left: 20px;

    .rank-info {
        display: flex;
        align-items: center;
        justify-content: center;

        .rank-text {
            font-size: 36px;
            font-weight: bold;
            color: #24ef8a;
            margin: 0 25px;
        }

        .olive-branch {
            width: 35px;
            height: 50px;

            &.right {
                transform: scaleX(-1);
            }
        }
    }

    .prize-details {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .you-get-text {
        font-size: 16px;
        color: #bac5c8;
        letter-spacing: 1px;
        margin-top: -20px;
        margin-bottom: 10px;
    }

    .prize-amount {
        font-size: 36px;
        font-weight: bold;
        line-height: 1.1;
        color: #24ee89;

        .currency-symbol {
            font-size: 28px;
            margin-right: 4px;
        }
    }

    .claim-button {
        width: 340px;
        height: 60px;
        background: #24ee89;
        border-radius: 20px;
        font-size: 32px;
        font-weight: bold;
        color: #000;
        transition: all 0.2s ease;

        &:hover {
            filter: brightness(1.1);
        }

        &:active {
            transform: scale(0.98);
        }
    }
}
</style>
