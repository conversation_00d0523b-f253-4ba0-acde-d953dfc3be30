<template>
    <button :class="['tiktoko default', type, { disabled: disabled }]" @click="handleClick"><slot></slot></button>
</template>
<script lang="ts" setup name="commonDialog">
const props = defineProps({
    type: {
        type: String,
        default: () => '',
    },
    disabled: {
        type: <PERSON><PERSON><PERSON>,
        default: () => false,
    },
})
const emit = defineEmits(['confirm'])
const handleClick = () => {
    if (props.disabled) {
        return
    }
    emit('confirm')
}
</script>
<style scoped lang="scss">
.tiktoko {
    &.default {
        padding: 10px;
        min-width: 300px;
        height: 80px;
        line-height: 80px;
        border-radius: 40px;
        line-height: 1;
        background: #fff;
        border: 1px solid #ccc;
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        transition: al 0.1s ease;
    }

    &.submit {
        background-image: linear-gradient(100deg, #1acf9a 0%, #13b187 45%, #0c9274 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        color: #ffffff;
    }
    &.disabled {
        opacity: 0.6;
    }
    &:active {
        opacity: 0.8;
    }
    &.sign,
    &.primary {
        width: 600px;
        height: 100px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
        background-blend-mode: normal, normal;
        border-radius: 50px;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
    }
    &.primary {
        background-image: linear-gradient(#25f4ee, #25f4ee), linear-gradient(#41aefe, #41aefe);
        color: #232626;
    }
}
</style>
