<template>
    <div class="sys-item" @click="handleClick">
        <img v-if="showIcon" class="sys-icon" :src="item.icon" />
        <p class="sys-name">{{ $t(item.name) }}</p>
        <slot v-if="$slots.right" name="right" :item="item"></slot>
        <div v-if="showArrow" class="sys-arrow"></div>
    </div>
</template>
<script setup lang="ts" name="sys-list">
const props = defineProps({
    item: {
        type: Object,
        default: () => {},
    },
    path: {
        type: String,
        default: () => '/me',
    },
    showIcon: {
        type: Boolean,
        default: () => true,
    },
    showArrow: {
        type: Boolean,
        default: () => true,
    },
})
const emits = defineEmits(['click'])
const handleClick = () => {
    emits('click', props.item)
}
</script>
<style scoped lang="scss">
.sys-item {
    @apply flex items-center;
    height: 88px;

    .sys-icon {
        width: 40px;
        height: 40px;
        margin-right: 20px;
    }
    .sys-name {
        flex: 1;
        font-size: 26px;
        font-weight: bold;
    }
    .sys-arrow {
        width: 20px;
        height: 34px;
        margin-left: 20px;
        background: url('@/assets/img/me/row.png') no-repeat;
        background-size: 100% 100%;
    }
}
</style>
