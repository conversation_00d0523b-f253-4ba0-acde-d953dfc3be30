<template>
    <div :class="['home-server', { 'scroll-hide': !store.showBuoy }]" @click="goServer"><img src="@/assets/img/new-home/cusomer.png" /></div>
</template>
<script setup lang="ts">
import { CustomerUrl } from '@/enums'
import { useBaseStore } from '@/stores'
const store = useBaseStore()

const goServer = () => {
    var url = CustomerUrl
    if (store.partner == 10002000) {
        url = 'https://www.facebook.com/profile.php?id=61552549876486'
    }
    window.open(url)
}
</script>
<style lang="scss" scoped>
.home-server {
    position: fixed;
    right: 20px;
    bottom: calc(var(--footer-height) + 30px);
    width: 80px;
    height: 80px;
    transition: all 0.3s ease;
    transform: translate3d(0, 0, 0);
}
.scroll-hide {
    &.home-server {
        transform: translate3d(130%, 0, 0);

        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
