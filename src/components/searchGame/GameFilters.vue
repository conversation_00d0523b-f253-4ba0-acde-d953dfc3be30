<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:53:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 15:03:27
 * @FilePath     : /src/components/searchGame/GameFilters.vue
 * @Description  : 游戏筛选器组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:53:00
-->

<template>
    <div class="filters">
        <div class="filter-button" @click="handleSortClick">
            <span class="filter-label">Sort by:</span>
            <span class="filter-value">{{ sortBy }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
        <div class="filter-button" @click="handleProviderClick">
            <span class="filter-label">Providers:</span>
            <span class="filter-value">{{ provider }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
    </div>

    <!-- 排序选择弹窗 -->
    <SelectSheet
        v-model:show="showSortSheet"
        title="Select"
        :options="sortOptions"
        :selected-value="sortBy"
        custom-class="sort-action-sheet"
        @select="onSortSelect"
    />

    <!-- 供应商选择弹窗 -->
    <ProviderSheet
        v-model:show="showProviderSheet"
        title="Select"
        :providers="providerList"
        :selected-providers="selectedProviders"
        custom-class="provider-action-sheet"
        @update:selectedProviders="onProvidersUpdate"
        @clear-all="onProvidersClearAll"
    />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import SelectSheet from './SelectSheet.vue'
import ProviderSheet from './ProviderSheet.vue'
import providerLogos from './cc.json'

// 定义属性
interface Props {
    sortBy?: string
    provider?: string
}

// 定义事件
interface Emits {
    (e: 'sort-click'): void
    (e: 'provider-click'): void
    (e: 'update:sortBy', value: string): void
    (e: 'update:provider', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    sortBy: 'Default',
    provider: 'All',
})

const emit = defineEmits<Emits>()

const sortBy = ref(props.sortBy)
const provider = ref(props.provider)

// 弹窗显示状态
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 排序选项数据 - 默认顺序 + 字母排序
const sortOptions = [
    { name: 'Default', value: 'Default' },
    { name: 'A-Z', value: 'A-Z' },
    { name: 'Z-A', value: 'Z-A' },
]

// 供应商数据
const providerList = ref([
    { id: 'playtech', name: 'Playtech', logo: providerLogos[0], gameCount: 459 },
    { id: 'pragmatic', name: 'Pragmatic Play', logo: providerLogos[1], gameCount: 565 },
    { id: 'tada', name: 'TaDa Gaming', logo: providerLogos[2], gameCount: 167 },
    { id: 'pg', name: 'PG Soft', logo: providerLogos[3], gameCount: 145 },
    { id: 'afunmx', name: 'Afun.MX', logo: providerLogos[4], gameCount: 15 },
    { id: 'spirit', name: 'Spirit', logo: providerLogos[5], gameCount: 24 },
    { id: 'revenge', name: 'Revenge', logo: providerLogos[6], gameCount: 12 },
    { id: 'spribe', name: 'Spribe', logo: providerLogos[7], gameCount: 10 },
    { id: 'evolution', name: 'Evolution', logo: providerLogos[8], gameCount: 131 },
    { id: 'playson', name: 'PlaySon', logo: providerLogos[9], gameCount: 9 },
])

// 选中的供应商对象数组
const selectedProviders = ref<Provider[]>([])

// Provider 接口定义
interface Provider {
    id: string
    name: string
    logo: string
    gameCount: number
}

// 供应商选项数据（保留原有的单选逻辑）
const providerOptions = [
    { name: 'All', value: 'All' },
    { name: 'Evolution', value: 'Evolution' },
    { name: 'Pragmatic Play', value: 'Pragmatic Play' },
    { name: 'NetEnt', value: 'NetEnt' },
]

const handleSortClick = () => {
    showSortSheet.value = true
    // emit('sort-click')
}

const handleProviderClick = () => {
    showProviderSheet.value = true
}

// 排序选择处理
const onSortSelect = (option: any) => {
    sortBy.value = option.name
    showSortSheet.value = false
    emit('update:sortBy', option.value)
}

// 供应商选择处理
const onProviderSelect = (option: any) => {
    provider.value = option.name
    showProviderSheet.value = false
    emit('update:provider', option.value)
}

// 供应商多选更新处理
const onProvidersUpdate = (newSelectedProviders: Provider[]) => {
    selectedProviders.value = newSelectedProviders

    // 更新显示文本 - 按照设计图显示格式
    if (newSelectedProviders.length === 0) {
        provider.value = 'All'
        emit('update:provider', 'All')
    } else {
        provider.value = `+${newSelectedProviders.length}` // 显示 "+7" 格式
        // 向父组件传递provider名称，用于条件搜索
        const providerNames = newSelectedProviders.map((p) => p.name).join(',')
        emit('update:provider', providerNames)
    }
}

// 清空所有供应商选择
const onProvidersClearAll = () => {
    selectedProviders.value = []
    provider.value = 'All'
    emit('update:provider', 'All')
}

// 监听外部值变化
watch(
    () => props.sortBy,
    (newValue) => {
        sortBy.value = newValue
    }
)

// 只监听外部 props 的变化，但不覆盖我们的格式化显示值
watch(
    () => props.provider,
    (newValue) => {
        // 如果当前显示值是我们的格式化值（+数字），则不要被外部值覆盖
        const isFormattedValue = provider.value.startsWith('+') && /^\+\d+$/.test(provider.value)

        if (!isFormattedValue && newValue !== provider.value) {
            provider.value = newValue
        }
    }
)

// 监听内部值变化，同步到外部
watch(sortBy, (newValue) => {
    emit('update:sortBy', newValue)
})

// 移除 provider 的自动同步 watch，改为手动控制
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;
$background-color: rgba(255, 255, 255, 0.1);

.filters {
    display: flex;
    gap: 12px;
    padding: 0 16px 16px;
    flex-shrink: 0;

    .filter-button {
        flex: 1;
        display: flex;
        align-items: center;
        background: #2c3031;
        border-radius: 6px;
        padding: 12px 16px;
        height: 80px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        font-size: 24px;

        &:hover {
            opacity: 0.8;
        }

        .filter-label {
            color: #bec7ca;
            margin-right: 4px;
            white-space: nowrap;
        }

        .filter-value {
            color: $text-primary;
            font-weight: 500;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-arrow {
            width: 44px;
            height: 44px;
            color: #fff;
            flex-shrink: 0;
            margin-left: 8px;
            background-color: #404647;
            border-radius: 8px;
            padding: 2px;
            // 关键：让伪元素居中
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            // 确保伪元素的定位基准
            position: relative;
        }
    }
}
</style>
