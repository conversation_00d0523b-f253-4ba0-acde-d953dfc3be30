<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:20:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:20:00
 * @FilePath     : /src/components/searchGame/demo.vue
 * @Description  : 搜索组件演示页面
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:20:00
-->

<template>
    <div class="search-demo">
        <div class="demo-header">
            <h2>游戏搜索组件演示</h2>
            <p>点击搜索框聚焦时会从右侧弹出搜索页面</p>
        </div>

        <div class="demo-content">
            <!-- 使用搜索输入框组件 -->
            <SearchGameInput />
        </div>
    </div>
</template>

<script setup lang="ts">
import SearchGameInput from './input.vue'
</script>

<style lang="scss" scoped>
.search-demo {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 20px;

    .demo-header {
        text-align: center;
        margin-bottom: 40px;
        color: #fff;

        h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
    }

    .demo-content {
        max-width: 400px;
        margin: 0 auto;
    }
}
</style>
