<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-30 15:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-30 15:17:48
 * @FilePath     : /src/components/searchGame/SelectSheet.vue
 * @Description  : 通用选择弹窗组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-30 15:00:00
-->

<template>
    <van-action-sheet v-model:show="visible" :class="sheetClass" :duration="0.1">
        <div class="sheet-header">
            <span class="sheet-title">{{ title }}</span>
            <van-icon name="cross" class="sheet-close" @click="handleClose" />
        </div>
        <div class="custom-panel">
            <div
                v-for="option in options"
                :key="option.value"
                class="panel-item"
                :class="{ 'panel-item--selected': selectedValue === option.value }"
                @click="handleSelect(option)"
            >
                <span class="panel-item-text">{{ option.name }}</span>
                <van-icon v-if="selectedValue === option.value" name="success" class="panel-item-icon" />
            </div>
        </div>
    </van-action-sheet>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 选项接口定义
interface Option {
    name: string
    value: string
}

// 定义属性
interface Props {
    /** 是否显示弹窗 */
    show?: boolean
    /** 弹窗标题 */
    title?: string
    /** 选项列表 */
    options?: Option[]
    /** 当前选中的值 */
    selectedValue?: string
    /** 自定义样式类名 */
    customClass?: string
}

// 定义事件
interface Emits {
    /** 更新显示状态 */
    (e: 'update:show', value: boolean): void
    /** 选择选项 */
    (e: 'select', option: Option): void
    /** 关闭弹窗 */
    (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
    title: 'Select',
    options: () => [],
    selectedValue: '',
    customClass: '',
})

const emit = defineEmits<Emits>()

// 计算属性：弹窗显示状态
const visible = computed({
    get: () => props.show,
    set: (value: boolean) => emit('update:show', value),
})

// 计算属性：弹窗样式类名
const sheetClass = computed(() => {
    return `select-sheet ${props.customClass}`.trim()
})

// 处理选择
const handleSelect = (option: Option) => {
    emit('select', option)
    visible.value = false
}

// 处理关闭
const handleClose = () => {
    visible.value = false
    emit('close')
}
</script>

<style lang="scss" scoped>
// 弹窗样式
.select-sheet {
    .sheet-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 24px;
        background-color: #2c3031;
        border-radius: 12px 12px 0 0;
        // 使用负边距来覆盖可能的白线
        margin-bottom: -2px;
        // 确保在最上层
        position: relative;
        z-index: 1;

        .sheet-title {
            font-size: 32px;
            font-weight: 600;
            color: #ffffff;
        }

        .sheet-close {
            font-size: 20px;
            color: #ffffff;
            cursor: pointer;
            width: 56px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #404647;
            border-radius: 8px;

            &:hover {
                opacity: 0.8;
            }
        }
    }

    // 自定义面板样式
    .custom-panel {
        background-color: #2c3031;
        padding-bottom: 40px;
        // 使用负边距来消除与header之间的间隙
        margin-top: -2px;
        .panel-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            background-color: #2c3031;
            color: #ffffff;
            font-size: 28px;
            font-weight: 400;
            cursor: pointer;
            transition: background-color 0.2s ease;
            height: 100px;

            &:hover {
                background-color: rgba(255, 255, 255, 0.03);
            }

            &.panel-item--selected {
                background-color: #363838;
            }

            .panel-item-text {
                flex: 1;
                color: #ffffff;
            }

            .panel-item-icon {
                color: #00d4aa;
                font-size: 28px;
                font-weight: bold;
                width: 56px;
                height: 56px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 12px;
                flex-shrink: 0;
            }
        }
    }
}
</style>
