<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:52:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-30 19:02:41
 * @FilePath     : /src/components/searchGame/GameCategories.vue
 * @Description  : 游戏分类标签组件 - 修正版：图标文字水平排列，统一样式
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:52:00
-->

<template>
    <div class="game-categories">
        <van-tabs v-model:active="activeTab" @change="handleTabChange" type="card" swipeable>
            <van-tab v-for="category in categories" :key="category.id" :name="category.id">
                <template #title>
                    <div class="tab-title">
                        <van-icon :name="category.icon" size="32" />
                        <span>{{ category.name }}</span>
                    </div>
                </template>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup lang="ts">
// 定义分类数据类型
interface Category {
    id: string
    name: string
    icon: string
}

// 定义属性
interface Props {
    selectedCategory?: string
    categories?: Category[]
}

// 定义事件
interface Emits {
    (e: 'category-select', categoryId: string): void
}

const props = withDefaults(defineProps<Props>(), {
    selectedCategory: 'all',
    categories: () => [
        { id: 'all', name: 'All games', icon: 'wap-home-o' }, // 房屋图标
        { id: 'original', name: 'Original', icon: 'star-o' }, // 星形图标
        { id: 'slots', name: 'Slots', icon: 'diamond-o' }, // 钻石图标
        { id: 'live', name: 'Live', icon: 'play-circle-o' }, // 播放图标
        { id: 'lottery', name: 'Lottery', icon: 'contact' }, // @符号图标
        { id: 'sports', name: 'Sports', icon: 'medal-o' }, // 奖牌图标
        { id: 'poker', name: 'Poker', icon: 'fire-o' }, // 火焰图标
        { id: 'fishing', name: 'Fishing', icon: 'location-o' }, // 位置图标
        { id: 'arcade', name: 'Arcade', icon: 'gem-o' }, // 宝石图标
        { id: 'crash', name: 'Crash', icon: 'warning-o' }, // 警告图标
        { id: 'table', name: 'Table', icon: 'apps-o' }, // 应用图标
        { id: 'virtual', name: 'Virtual', icon: 'tv-o' }, // 电视图标
    ],
})

const emit = defineEmits<Emits>()

// 使用 ref 创建响应式的活动标签
const activeTab = ref(props.selectedCategory)

// 监听 selectedCategory 属性变化，同步更新 activeTab
watch(
    () => props.selectedCategory,
    (newValue) => {
        activeTab.value = newValue
    },
    { immediate: true }
)

// 处理标签切换事件
const handleTabChange = (name: string | number) => {
    emit('category-select', name as string)
}
</script>

<style lang="scss" scoped>
// 颜色变量 - 根据设计图定义
$active-background: #414647; // 选中状态背景色
$active-text: #ffffff; // 选中状态文字颜色
$active-border: #ffffff0d; // 选中状态边框颜色
$inactive-background: #202222; // 未选中状态背景色
$inactive-text: #adb7ba; // 未选中状态文字颜色

.game-categories {
    padding: 16px;
    flex-shrink: 0;
    overflow-x: auto;

    // 完全重写 van-tabs 样式以匹配设计图
    :deep(.van-tabs) {
        .van-tabs__nav {
            background: transparent;
            border: none;
            padding: 0;
            height: auto;
            width: auto !important; // 让导航宽度自适应内容
            flex-wrap: nowrap !important; // 不换行，确保水平滚动
        }

        .van-tabs__wrap {
            height: auto; // 自适应高度
            overflow: visible;
            width: 100% !important; // 确保容器宽度不受限制
        }

        .van-tabs__nav--card {
            .van-tab {
                // 未选中状态样式
                background: $inactive-background !important;
                border: 1px solid transparent !important;
                border-radius: 12px;
                margin: 0;
                padding: 0 16px !important;
                height: 76px;
                width: auto !important;
                color: $inactive-text;
                flex-shrink: 0;
                flex-grow: 0;
                position: relative !important;

                // 选中状态
                &.van-tab--active {
                    background: $active-background !important;
                    border: 1px solid $active-border !important;
                    color: $active-text !important;
                    font-weight: 800;
                }

                // 最后一个标签
                &:last-child {
                    margin-right: 0;
                }
            }
        }

        // 完全隐藏不需要的元素
        .van-tabs__line {
            display: none !important;
        }

        .van-tabs__content {
            display: none !important;
        }

        // 隐藏滚动条
        .van-tabs__nav {
            &::-webkit-scrollbar {
                display: none;
            }
            scrollbar-width: none;
        }
    }

    // 标签标题样式 - 图标和文字水平布局
    .tab-title {
        display: flex;
        flex-direction: row !important;
        align-items: center;
        justify-content: center;
        gap: 8px;
        white-space: nowrap;
        height: 76px;
        width: auto;

        // 图标样式
        .van-icon {
            color: currentColor !important;
            width: 32px; // 图标大小调整为 32px
            height: 32px; // 图标大小调整为 32px
            font-size: 32px !important; // 确保图标字体大小也是 32px
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        // 文字样式
        span {
            font-size: 26px; // 文字大小调整为 26px
            font-weight: 400;
            color: currentColor;
            line-height: 1;
        }
    }
}
</style>
