<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:52:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 14:23:51
 * @FilePath     : /src/components/searchGame/GameCategories.vue
 * @Description  : 游戏分类标签组件 - 修正版：图标文字水平排列，统一样式
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:52:00
-->

<template>
    <div class="game-categories">
        <van-tabs v-model:active="activeTab" @change="handleTabChange" type="card" swipeable>
            <van-tab v-for="category in categories" :key="category.type" :name="category.type">
                <template #title>
                    <div class="tab-title">
                        <img v-if="category.picture" v-lazy="category.picture" class="tab-icon" />
                        <img v-else-if="category.menupicture" v-lazy="category.menupicture" class="tab-icon" />
                        <van-icon v-else name="wap-home-o" size="32" class="tab-icon" />
                        <span>{{ $t(category.name) }}</span>
                    </div>
                </template>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { Categorys } from '@/api/home/<USER>'
import AllIcon from '@/assets/img/new-home/all.png'

// 定义属性
interface Props {
    selectedCategory?: string
    categories?: Categorys[]
}

// 定义事件
interface Emits {
    (e: 'category-select', category: Categorys): void
}

const store = useBaseStore()

const props = withDefaults(defineProps<Props>(), {
    selectedCategory: 'all',
})

// 使用 computed 来动态获取分类数据
const categories = computed(() => {
    if (props.categories) {
        return props.categories
    }

    // 添加 "All games" 选项到 store.menuData 前面
    const allGamesCategory: Categorys = {
        type: 'all',
        name: 'All games',
        picture: AllIcon,
        menupicture: AllIcon,
    }
    console.log('%c------ store.menuData ', 'background-color:blue;font-size:12px;color:#fff', store.menuData)
    return store.menuData?.length ? [allGamesCategory, ...store.menuData] : [allGamesCategory]
})

const emit = defineEmits<Emits>()

// 使用 ref 创建响应式的活动标签
const activeTab = ref(props.selectedCategory)

// 监听 selectedCategory 属性变化，同步更新 activeTab
watch(
    () => props.selectedCategory,
    (newValue) => {
        activeTab.value = newValue
    },
    { immediate: true }
)

// 处理标签切换事件
const handleTabChange = (name: string | number) => {
    // 根据 name (type) 找到对应的分类对象
    const selectedCategory = categories.value.find((category) => category.type === name)
    if (selectedCategory) {
        emit('category-select', selectedCategory)
    }
}
</script>

<style lang="scss" scoped>
// 颜色变量 - 根据设计图定义
$active-background: #414647; // 选中状态背景色
$active-text: #ffffff; // 选中状态文字颜色
$active-border: #ffffff0d; // 选中状态边框颜色
$inactive-background: #202222; // 未选中状态背景色
$inactive-text: #adb7ba; // 未选中状态文字颜色

.game-categories {
    padding: 16px;
    flex-shrink: 0;
    overflow-x: auto;

    // 完全重写 van-tabs 样式以匹配设计图
    :deep(.van-tabs) {
        .van-tabs__nav {
            background: transparent;
            border: none;
            padding: 0;
            height: auto;
            width: auto !important; // 让导航宽度自适应内容
            flex-wrap: nowrap !important; // 不换行，确保水平滚动
        }

        .van-tabs__wrap {
            height: auto; // 自适应高度
            overflow: visible;
            width: 100% !important; // 确保容器宽度不受限制
        }

        .van-tabs__nav--card {
            .van-tab {
                // 未选中状态样式
                background: $inactive-background !important;
                border: 1px solid transparent !important;
                border-radius: 12px;
                margin: 0;
                padding: 0 16px !important;
                height: 76px;
                width: auto !important;
                color: $inactive-text;
                flex-shrink: 0;
                flex-grow: 0;
                position: relative !important;

                // 选中状态
                &.van-tab--active {
                    background: $active-background !important;
                    border: 1px solid $active-border !important;
                    color: $active-text !important;
                    font-weight: 800;
                }

                // 最后一个标签
                &:last-child {
                    margin-right: 0;
                }
            }
        }

        // 完全隐藏不需要的元素
        .van-tabs__line {
            display: none !important;
        }

        .van-tabs__content {
            display: none !important;
        }

        // 隐藏滚动条
        .van-tabs__nav {
            &::-webkit-scrollbar {
                display: none;
            }
            scrollbar-width: none;
        }
    }

    // 标签标题样式 - 图标和文字水平布局
    .tab-title {
        display: flex;
        flex-direction: row !important;
        align-items: center;
        justify-content: center;
        gap: 8px;
        white-space: nowrap;
        height: 76px;
        width: auto;

        // 图标样式 - 统一处理图片和van-icon
        .tab-icon {
            width: 32px;
            height: 32px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            object-fit: contain; // 对于img标签，保持图片比例
        }

        .van-icon {
            color: currentColor !important;
            font-size: 32px !important;
        }

        // 文字样式
        span {
            font-size: 26px; // 文字大小调整为 26px
            font-weight: 400;
            color: currentColor;
            line-height: 1;
        }
    }
}
</style>
