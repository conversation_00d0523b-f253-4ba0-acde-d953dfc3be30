<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 17:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 17:00:00
 * @FilePath     : /src/components/searchGame/ComponentDemo.vue
 * @Description  : 组件化搜索页面演示
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 17:00:00
-->

<template>
    <div class="component-demo">
        <div class="demo-header">
            <h2>组件化搜索页面演示</h2>
            <p>展示 5 个独立组件的组合效果</p>
        </div>
        
        <div class="demo-content">
            <!-- 使用搜索输入框组件，点击后弹出完整搜索页面 -->
            <SearchGameInput />
            
            <!-- 组件说明 -->
            <div class="component-info">
                <h3>组件架构说明：</h3>
                <ul>
                    <li><strong>SearchGameInput</strong> - 搜索输入框，点击聚焦弹出搜索页面</li>
                    <li><strong>SearchGameIndex</strong> - 搜索页面容器，组合以下 5 个组件：</li>
                    <li>　├── <strong>SearchHeader</strong> - 导航条组件</li>
                    <li>　├── <strong>SearchBar</strong> - 搜索栏组件</li>
                    <li>　├── <strong>GameCategories</strong> - 游戏分类组件</li>
                    <li>　├── <strong>GameFilters</strong> - 筛选器组件</li>
                    <li>　└── <strong>GameGrid</strong> - 游戏列表组件</li>
                </ul>
                
                <h3>组件特点：</h3>
                <ul>
                    <li>✅ 高度模块化，每个组件职责单一</li>
                    <li>✅ 可独立开发、测试和维护</li>
                    <li>✅ 支持 Props 和 Events 通信</li>
                    <li>✅ 样式封装，避免样式冲突</li>
                    <li>✅ 符合 Vue 3 Composition API 最佳实践</li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SearchGameInput from './input.vue'
</script>

<style lang="scss" scoped>
.component-demo {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 20px;
    color: #fff;

    .demo-header {
        text-align: center;
        margin-bottom: 40px;

        h2 {
            font-size: 24px;
            margin-bottom: 10px;
            color: #4caf50;
        }

        p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
    }

    .demo-content {
        max-width: 400px;
        margin: 0 auto;

        .component-info {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);

            h3 {
                font-size: 16px;
                margin-bottom: 12px;
                color: #4caf50;
            }

            ul {
                margin: 0;
                padding-left: 20px;

                li {
                    margin-bottom: 8px;
                    font-size: 14px;
                    line-height: 1.5;

                    strong {
                        color: #fff;
                    }
                }
            }
        }
    }
}
</style>
