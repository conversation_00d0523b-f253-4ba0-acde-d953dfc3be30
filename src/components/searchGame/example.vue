<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:00:00
 * @FilePath     : /src/components/searchGame/example.vue
 * @Description  : 搜索框组件使用示例
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:00:00
-->

<template>
    <div class="search-example">
        <div class="example-container">
            <h3>游戏搜索框示例</h3>
            
            <!-- 基础用法 -->
            <div class="example-section">
                <h4>基础用法</h4>
                <SearchGameInput 
                    v-model="searchValue1" 
                    @search="handleSearch"
                    @input="handleInput"
                />
                <p>当前搜索值: {{ searchValue1 }}</p>
            </div>

            <!-- 自定义占位符 -->
            <div class="example-section">
                <h4>自定义占位符</h4>
                <SearchGameInput 
                    v-model="searchValue2" 
                    placeholder="请输入游戏名称..."
                    @search="handleSearch"
                />
                <p>当前搜索值: {{ searchValue2 }}</p>
            </div>

            <!-- 事件监听 -->
            <div class="example-section">
                <h4>事件监听</h4>
                <SearchGameInput 
                    v-model="searchValue3" 
                    @search="handleSearch"
                    @input="handleInput"
                    @focus="handleFocus"
                    @blur="handleBlur"
                />
                <div class="event-log">
                    <p>最后触发的事件: {{ lastEvent }}</p>
                    <p>事件详情: {{ eventDetail }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SearchGameInput from './input.vue'

// 搜索值
const searchValue1 = ref('')
const searchValue2 = ref('')
const searchValue3 = ref('')

// 事件记录
const lastEvent = ref('')
const eventDetail = ref('')

// 处理搜索事件
const handleSearch = (value: string) => {
    lastEvent.value = 'search'
    eventDetail.value = `搜索内容: ${value}`
    console.log('搜索:', value)
}

// 处理输入事件
const handleInput = (value: string) => {
    lastEvent.value = 'input'
    eventDetail.value = `输入内容: ${value}`
    console.log('输入:', value)
}

// 处理聚焦事件
const handleFocus = (event: Event) => {
    lastEvent.value = 'focus'
    eventDetail.value = '输入框获得焦点'
    console.log('聚焦:', event)
}

// 处理失焦事件
const handleBlur = (event: Event) => {
    lastEvent.value = 'blur'
    eventDetail.value = '输入框失去焦点'
    console.log('失焦:', event)
}
</script>

<style lang="scss" scoped>
.search-example {
    padding: 20px;
    background: #1a1a1a;
    min-height: 100vh;
    color: #fff;

    .example-container {
        max-width: 600px;
        margin: 0 auto;

        h3 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }

        .example-section {
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;

            h4 {
                margin-bottom: 15px;
                color: #fff;
                font-size: 16px;
            }

            p {
                margin-top: 10px;
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
            }

            .event-log {
                margin-top: 15px;
                padding: 10px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                border-left: 3px solid #007aff;

                p {
                    margin: 5px 0;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
