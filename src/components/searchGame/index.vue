<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 14:24:49
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 1. 导航条 -->
        <SearchHeader @close="handleClose" />

        <!-- 2. 搜索栏 -->
        <SearchBar v-model="searchKeyword" @search="performSearch" @category-click="showCategorySheet = true" />

        <!-- 3. 分类标签 -->
        <GameCategories :selected-category="selectedCategory" @category-select="selectCategory" />

        <!-- 4. 排序筛选器 -->
        <GameFilters
            v-model:sort-by="sortBy"
            v-model:provider="provider"
            @sort-click="showSortSheet = true"
            @provider-click="showProviderSheet = true"
        />

        <!-- 5. 游戏列表 -->
        <GameGrid :games="filteredGames" @game-select="selectGame" />

        <!-- 分类选择器 -->
        <van-action-sheet v-model:show="showCategorySheet" :actions="categoryActions" @select="onCategorySelect" />

        <!-- 排序选择器 -->
        <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" />

        <!-- 供应商选择器 -->
        <van-action-sheet v-model:show="showProviderSheet" :actions="providerActions" @select="onProviderSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBaseStore } from '@/stores'
import { Categorys } from '@/api/home/<USER>'
import SearchHeader from './SearchHeader.vue'
import SearchBar from './SearchBar.vue'
import GameCategories from './GameCategories.vue'
import GameFilters from './GameFilters.vue'
import GameGrid from './GameGrid.vue'

// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

const store = useBaseStore()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 获取所有游戏数据（从 store 中获取真实数据）
const allGames = computed(() => {
    if (!store.gameList || !store.menuData) return []

    // 获取所有分类的游戏
    const games = []
    store.menuData.forEach((category) => {
        const categoryGames = store.getGameList(category.type) || []
        // 为每个游戏添加分类信息
        categoryGames.forEach((game) => {
            games.push({
                ...game,
                categoryType: category.type, // 使用 type 作为分类标识
                categoryName: category.name,
            })
        })
    })

    return games
})

// 游戏数据现在从 allGames computed 属性获取真实数据

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = allGames.value

    // 按分类过滤（使用 categoryType 字段，与 GameCategories 的 type 匹配）
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.categoryType === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        filtered = filtered.filter(
            (game) =>
                (game.name && game.name.toLowerCase().includes(keyword)) ||
                (game.gamefirm && store.getGameName(game.gamefirm).toLowerCase().includes(keyword)) ||
                (game.gametag && game.gametag.toLowerCase().includes(keyword))
        )
    }

    // 按供应商过滤（使用 gamefirm 字段）
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => {
            const gameName = store.getGameName(game.gamefirm)
            return gameName.toLowerCase().includes(provider.value.toLowerCase())
        })
    }

    return filtered
})

// 方法
const handleClose = () => {
    emit('close')
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (category: Categorys) => {
    selectedCategory.value = category.type || 'all'
    console.log('选中的分类对象:', category)
    // 现在可以访问完整的分类信息：
    // category.type - 分类类型
    // category.name - 分类名称
    // category.picture - 分类图片
    // category.menupicture - 菜单图片
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;

.search-game-container {
    background: #202222;
    height: 100vh;
    display: flex;
    flex-direction: column;
    color: $text-primary;
    overflow: auto;
}
</style>
