# SelectSheet 通用选择弹窗组件

## 功能描述
通用的选择弹窗组件，支持单选功能，可在多个页面复用。

## 组件特性
- ✅ 支持自定义标题
- ✅ 支持选项列表配置
- ✅ 支持当前选中值显示
- ✅ 支持自定义样式类名
- ✅ 完整的事件回调
- ✅ TypeScript 类型支持
- ✅ 响应式设计
- ✅ 统一的视觉风格

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| show | boolean | false | 是否显示弹窗 |
| title | string | 'Select' | 弹窗标题 |
| options | Option[] | [] | 选项列表 |
| selectedValue | string | '' | 当前选中的值 |
| customClass | string | '' | 自定义样式类名 |

### Option 接口
```typescript
interface Option {
    name: string    // 显示名称
    value: string   // 选项值
}
```

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:show | boolean | 更新显示状态 |
| select | Option | 选择选项时触发 |
| close | - | 关闭弹窗时触发 |

## 使用示例

### 基础用法
```vue
<template>
    <div>
        <!-- 触发按钮 -->
        <button @click="showSheet = true">选择排序方式</button>
        
        <!-- 选择弹窗 -->
        <SelectSheet
            v-model:show="showSheet"
            title="选择排序"
            :options="sortOptions"
            :selected-value="selectedSort"
            @select="handleSelect"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SelectSheet from './SelectSheet.vue'

const showSheet = ref(false)
const selectedSort = ref('popular')

const sortOptions = [
    { name: 'Popular', value: 'popular' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
    { name: 'New Game', value: 'new' }
]

const handleSelect = (option: { name: string; value: string }) => {
    selectedSort.value = option.value
    console.log('选择了:', option)
}
</script>
```

### 自定义样式
```vue
<SelectSheet
    v-model:show="showSheet"
    title="选择供应商"
    :options="providerOptions"
    :selected-value="selectedProvider"
    custom-class="my-custom-sheet"
    @select="handleProviderSelect"
/>
```

### 完整事件处理
```vue
<SelectSheet
    v-model:show="showSheet"
    title="选择选项"
    :options="options"
    :selected-value="selectedValue"
    @select="handleSelect"
    @close="handleClose"
    @update:show="handleShowChange"
/>
```

## 样式定制
组件提供了 `customClass` 属性，可以传入自定义类名来覆盖默认样式：

```scss
.my-custom-sheet {
    .sheet-header {
        background-color: #your-color;
    }
    
    .panel-item {
        font-size: 24px;
    }
}
```

## 注意事项
1. 组件使用了 Vant 的 ActionSheet 作为基础组件
2. 样式采用了深色主题设计
3. 支持 v-model:show 双向绑定
4. 选择后会自动关闭弹窗
5. 所有样式都经过了移动端适配
