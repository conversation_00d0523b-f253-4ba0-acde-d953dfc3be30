<template>
    <div :id="`chat-panel${userId}`" class="chat-view" @scroll="onChatScroll($event)">
        <!-- 数据加载状态栏 -->
        <div v-show="!hideEmptyTip" class="load-toolbar pointer">
            <!-- <span v-if="state.finish" class="no-more"> {{ $t('No_more') }} </span> -->
            <div v-if="state.loading" class="load"><van-loading :size="10" /> {{ $t('Loading_please_wait') }}</div>
        </div>
        <slot v-if="$slots.default" :list="list" :state="state"></slot>
        <template v-else>
            <div :id="`chat${item.id}`" v-for="(item, index) in list" :key="index">
                <div class="chat-time" v-if="item.event === 'transition' || getSHowStatus(item, index)">
                    {{ showTime(item) }}
                </div>
                <div :class="['chat-item', checkSelf(item) ? 'self' : 'other']">
                    <div class="chart-avatar">
                        <img v-if="checkSelf(item)" :src="userInfo.avatar" />
                        <img
                            v-else
                            :src="
                                userId === '10000'
                                    ? 'https://betfugu.com/static/system.png'
                                    : userId === '20000'
                                    ? 'https://betfugu.com/static/transition.png'
                                    : chatUser.avatar
                            "
                        />
                    </div>
                    <div
                        :class="[
                            'chat-pop',
                            {
                                transfer: item.event === 'transition',
                            },
                        ]"
                        @click="handleChatPop(item)"
                    >
                        <p v-html="$t(item.content.text, translateTtoText(item))"></p>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<script lang="ts" setup>
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useTalkRecord } from '@/hooks/useTalkRecord'
import dayjs from 'dayjs'
import { getAvatar } from '@/api/notice'
import { useSubscript } from '@/components/notice/hook/useSubscribe'
import { getChannel } from '@/utils'
import { useRouter } from 'vue-router'

const { translateTtoText } = useSubscript('')

const store = useBaseStore()
const { userInfo } = storeToRefs(store)
const router = useRouter()
const props = defineProps({
    userId: {
        type: [String, Number],
        default: () => '4416900',
    },
    showEmptyTip: {
        type: Boolean,
        default: () => true,
    },
})
const model = defineModel()
const emits = defineEmits(['limit'])

const chatUser = ref<{ avatar?: string }>({})
const hideEmptyTip = computed(() => !props.showEmptyTip && !list.value.length)

const { onRefreshLoad, onScrollBottom, onLoad, list, state } = useTalkRecord({ chatId: props.userId })

const getSHowStatus = (item, index) => {
    if (item?.showTime) return true
    if (index === 0) return state.finish
    const prev = list.value[index - 1]
    const curDate = dayjs(item.time)
    const prevDate = dayjs(prev.time)
    const isSame = curDate.isSame(prevDate, 'day')
    if (!isSame) {
        return true
    }
    return false
}
const showTime = (item) => {
    const today = dayjs()
    const cur = dayjs(item.time)
    if (today.isSame(cur, 'day')) {
        return `Today ${cur.format('HH:mm')}`
    }
    return dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
}

const checkSelf = (item) => {
    return userInfo.value.uid == item?.content?.sender
}
const onChatScroll = (e: any) => {
    if (e.target.scrollTop === 0) {
        onRefreshLoad()
    }
}
const handleChatPop = (item) => {
    console.log(item, 'itemitemitem')
    if (item.event === 'video') {
        store.buyVideoParam = item.content.context
        router.push('/video')
    }
}
watch(
    () => state.count,
    () => {
        model.value = state.count
    }
)

onMounted(async () => {
    await onLoad()
    const res = await store.subscribe(getChannel(props.userId, store.userInfo?.uid))
    if (res.code === 200) {
        emits('limit', res.limit)
    }
    getAvatar([props.userId as string]).then((res) => {
        if (res.code === 200) {
            chatUser.value = res.users[props.userId]
        }
    })
})
onActivated(() => {
    if (list.value.length) {
        onScrollBottom(false)
    }
})
defineExpose({
    onScrollBottom,
})
</script>
<style lang="scss" scoped>
.chat-view {
    padding: 47px 30px 0;
    height: 100%;
    overflow: auto;

    .chat-item {
        @apply flex;
        margin: 40px 0;

        .chart-avatar {
            margin-right: 20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #fff;
        }
        .chat-pop {
            max-width: 486px;
            padding: 26px 32px;
            border-radius: 30px;
            min-height: 76px;
            background-color: #65605e;
            font-family: San-Francisco-Text-Regular;
            font-size: 28px;
            color: #fff;
            word-break: break-all;

            &.transfer {
                @apply flex items-center gap-[18px];
                background: linear-gradient(#1bb28f, #1bb28f), linear-gradient(#ffb62a, #ffb62a);
                color: #fff;

                &::before {
                    display: inline-block;
                    content: '';
                    width: 63px;
                    height: 63px;
                    background: url('@/assets/img/chat/transfer.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            :deep(.text-yellow) {
                color: #e5ff06;
            }
            :deep(.underlined-red) {
                color: orange;
                text-decoration: underline;
            }
        }
        &.other .chat-pop {
            border-top-left-radius: 0;
        }
        &.self {
            @apply flex-row-reverse;
            .chat-pop {
                border-top-right-radius: 0;
                background-color: #fff;
                color: #333333;
                &.transfer {
                    background: linear-gradient(#1bb28f, #1bb28f), linear-gradient(#ffb62a, #ffb62a);
                    color: #fff;
                }
            }
            .chart-avatar {
                margin-left: 20px;
            }
        }
    }
    .chat-time {
        font-family: San-Francisco-Text-Regular;
        font-size: 24px;
        line-height: 30px;
        color: #999999;
        text-align: center;
    }
    .chat-tip {
        @apply flex gap-[10px];
        padding: 0 21px;
        font-family: San-Francisco-Text-Regular;
        font-size: 24px;
        color: #d59944;

        &::after {
            content: '';
            display: inline-block;
            width: 32px;
            height: 27px;
            background: url('@/assets/img/chat/auth.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .load-toolbar {
        height: 38px;
        text-align: center;
        line-height: 38px;
        font-size: 13px;
        color: #b9b3b3;
        .load {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
    }
}
</style>
