<template>
    <van-overlay :show="visible" :z-index="300" :lock-scroll="false" @click="handleClose">
        <section @click.stop class="transfer-dialog">
            <div v-if="store.userInfo?.isReseller" class="tranfer-content">
                <div
                    class="tranfer-balance tip"
                    v-html="
                        $t('wallet_reseller_deposit1', {
                            current_type: store.cureencySymbol(),
                            number1: store.wallet?.gold || 0,
                        })
                    "
                ></div>
                <div class="tip">
                    {{ $t('wallet_reseller_deposit2') }} <span>{{ count || 0 }}</span>
                </div>
                <div class="transfer-input">
                    <van-form>
                        <van-field
                            ref="formCom"
                            v-model="count"
                            type="digit"
                            :placeholder="$t('wallet_reseller_deposit3')"
                            :rules="[
                                {
                                    required: true,
                                    message: $t('wallet_reseller_deposit_fail2'),
                                    validator,
                                },
                            ]"
                        />
                    </van-form>
                </div>
                <div
                    class="limit-tip"
                    v-html="
                        $t('wallet_reseller_deposit4', {
                            current_type: store.cureencySymbol(),
                            number2: min,
                        })
                    "
                ></div>
                <div :class="['tranfer-btn', { active: count }]" @click="doDeposite">{{ $t('Confirm') }}</div>
            </div>
            <div v-else class="tranfer-content">
                <div
                    class="tip"
                    v-html="
                        $t('wallet_reseller_withdraw1', {
                            number1: accountInfo?.balanceTimesInfo?.times || 0,
                        })
                    "
                ></div>
                <div
                    class="tip"
                    v-html="
                        $t('wallet_reseller_withdraw2', {
                            current_type: store.cureencySymbol(),
                            number2: store.wallet?.balance || 0,
                        })
                    "
                ></div>
                <div class="transfer-level-title">{{ $t('wallet_reseller_withdraw3') }}</div>
                <div class="transfer-level">
                    <div class="level-wrap">
                        <div
                            :class="['level-item', { active: index === checked, disable: (store.wallet?.balance || 0) < item.cny }]"
                            v-for="(item, index) in payOutList"
                            :key="index"
                            @click="changeItem(index)"
                        >
                            {{ formatLocalMoney(item.cny, 0) }}
                        </div>
                    </div>
                </div>
                <div
                    :class="[
                        'tranfer-btn level-btn',
                        {
                            active: checked != -1 || !accountInfo?.balanceTimesInfo?.times,
                        },
                    ]"
                    @click="doWithDrawl"
                >
                    {{ $t('Confirm') }}
                </div>
            </div>
            <div class="close" @click="handleClose"></div>
        </section>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { getPayoutstore, getAccountInfo } from '@/api/wallet'
import { AccountInfo } from '@/api/wallet/type'
import { formatLocalMoney } from '@/utils/toolsValidate'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const visible = defineModel()
const props = defineProps({
    min: {
        type: Number,
        default: () => 0,
    },
})

const store = useBaseStore()
const route = useRoute()

const i18n = useI18n()

const count = ref('')
const formCom = ref()
const payOutList = ref([])
const accountInfo = ref<AccountInfo>({})
const checked = ref(-1)

const handleClose = () => {
    visible.value = false
}
const validator = (value) => {
    //@ts-ignore
    if (value == 0 || (store.wallet?.gold || 0) < Number(value) || Number(value) < props.min) {
        return false
    }
    return true
}
const doDeposite = () => {
    const staus = formCom.value.getValidationStatus()
    if (staus !== 'passed') {
        return
    }
    confirm(+count.value)
}
const confirm = (gold) => {
    return showDialog({
        message: i18n.t('wallet_reseller_info', { current_number: gold, current_type: store.cureencySymbol(), username: route.query.nickname }),
        className: 'common-dialog',
        showCancelButton: true,
        confirmButtonText: i18n.t('Confirm'),
        cancelButtonText: i18n.t('Cancel'),
        allowHtml: true,
    })
        .then(async () => {
            const res = await store.SendChatGold({
                uid: +route.query.userID,
                gold,
            })
            if (res.code === 200) {
                visible.value = false
            } else {
                showFailToast(i18n.t('error_info'))
            }
            return res.code
        })
        .catch((e) => {
            console.log(e, 'Cancel')
        })
}
const doWithDrawl = () => {
    confirm(payOutList.value[checked.value].cny).then((code) => {
        if (code === 200) {
            getAccountInfos()
        }
    })
}
const getAccountInfos = async (loading = true) => {
    try {
        const res = await getAccountInfo(loading)
        if (res.code === 200) {
            accountInfo.value = res.data
            store.setWallet({ balance: res.data.balance })
        }
    } catch (e) {
        console.log(e)
    }
}
const getPayoutstores = async () => {
    try {
        const res = await getPayoutstore(store.userInfo.uid)
        if (res.code === 200) {
            payOutList.value = res.items
            if (res.items.some((item) => item.cny <= store.wallet?.balance && checked.value === -1)) {
                checked.value = 0
            }
        }
    } catch (e) {
        console.log(e)
    }
}
const changeItem = (index) => {
    checked.value = index
}

const getInfo = () => {
    if (!store.userInfo?.isReseller) {
        getAccountInfos()
        getPayoutstores()
    }
}
onMounted(() => {
    getInfo()
})
</script>
<style lang="scss" scoped>
.transfer-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 684px;
    padding: 55px 30px 61px;
    background-color: #2a2d3b;
    border-radius: 27px;

    transform: translate3d(-50%, -50%, 0);
    font-family: MicrosoftYaHei;
    color: #ffffff;

    .close {
        position: absolute;
        width: 33px;
        height: 34px;
        right: 30px;
        top: 35px;
        background: url('@/assets/img/wallets/account_close.png') no-repeat;
        background-size: 100% 100%;
    }
    .tranfer-content {
        .tip {
            margin-bottom: 40px;
            font-size: 30px;
            font-weight: bold;
            :deep(span) {
                color: #ffa72a;
            }
        }
        .tranfer-btn {
            @apply flex justify-center items-center;
            margin: 44px auto 0;
            width: 320px;
            height: 80px;
            background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
            background-blend-mode: normal, normal;
            border-radius: 40px;
            font-size: 36px;
            opacity: 0.6;
            pointer-events: none;

            &.active {
                opacity: 1;
                pointer-events: auto;
            }
            &.level-btn {
                margin-top: 0;
            }
        }
        .transfer-input {
            :deep(.van-cell) {
                position: relative;
                background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#0579fe, #0579fe);
                background-blend-mode: normal, normal;
                border-radius: 22px;
                overflow: inherit;
                .van-field__control {
                    color: #fff;
                    &::placeholder {
                        color: #747a8d;
                    }
                }
                .van-field__error-message {
                    display: flex;
                    align-items: center;
                    position: absolute;
                    bottom: -38px;
                    left: -20px;
                    width: 100%;
                    line-height: 40px;
                    font-size: 24px;
                    font-weight: bold;
                    color: #ff5688;
                    transform: translate3d(0, 110%, 0);
                    &::before {
                        position: relative;
                        top: 0px;
                        content: '';
                        display: inline-block;
                        margin-right: 10px;
                        width: 42px;
                        height: 38px;
                        background: url('@/assets/img/wallets/exclam_mark.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }
            }
        }
        .limit-tip {
            @apply flex items-center justify-end;
            margin-top: 22px;
            height: 38px;
            font-size: 22px;
            color: #8d8d8d;
            :deep(span) {
                color: #ffb245;
            }
        }
        .transfer-level-title {
            @apply flex items-center justify-center gap-[7px];
            margin-bottom: 40px;
            font-size: 36px;
            font-weight: bold;
            &::before,
            &::after {
                display: inline-block;
                content: '';
                width: 148px;
                height: 4px;
                background: url('@/assets/img/chat/line.png') no-repeat;
                background-size: 100% 100%;
            }
            &::after {
                transform: rotateY(180deg);
            }
        }
        .transfer-level {
            margin-bottom: 30px;
            height: 254px;
            overflow-y: scroll;
            .level-wrap {
                @apply grid grid-cols-3 gap-x-[8px] gap-y-[24px];
                .level-item {
                    @apply flex justify-center items-center;
                    position: relative;
                    width: 198px;
                    height: 108px;
                    background: linear-gradient(#444b5e, #444b5e), linear-gradient(#ffffff, #ffffff);
                    border: 8px solid #373d4c;
                    border-radius: 20px;
                    font-size: 36px;

                    &.active {
                        border-color: #10c580;

                        &::after {
                            position: absolute;
                            right: -5px;
                            bottom: -4px;
                            display: inline-block;
                            content: '';
                            width: 52px;
                            height: 52px;
                            background: url('@/assets/img/wallets/checked.png') no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                    &.disable {
                        pointer-events: none;
                        opacity: 0.6;
                    }
                }
            }
        }
    }
}
</style>
