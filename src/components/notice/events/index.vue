<template>
    <div class="event-wrap">
        <template v-if="filterEvents.length">
            <div class="event-item" v-for="item in filterEvents" :key="item._id" @click="goActive(item)">
                <img class="event-pic" :src="item.picture" />
                <van-badge :dot="!item.read" :offset="[20, -12]" position="bottom-left">
                    <div :class="['event-title', { read: item.read }]">
                        <div class="flex-1">{{ item.title }}</div>
                        <div class="time">
                            {{
                                item.begintime
                                    ? dayjs(item.begintime).format('YYYY/MM/DD') + ' - ' + dayjs(item.endtime).format('YYYY/MM/DD')
                                    : $t('Long_lasting_effect')
                            }}
                        </div>
                    </div>
                </van-badge>
            </div>
        </template>
        <van-empty class="tip" v-else>{{ $t('No_more_content') }}</van-empty>
    </div>
</template>
<script lang="ts" setup>
import * as service from '@/api'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'

const router = useRouter()
const store = useBaseStore()
const events = ref([])
const filterEvents = computed(() => {
    const now = dayjs()
    return events.value.filter((item) => {
        if (store.userInfo?.telephone && item.link === '/bindPhone') {
            return false
        }
        if (store.userInfo?.shareid && item.link === '/binInviter') return false
        return now.isBefore(dayjs(item.endtime))
    })
})

const getList = async () => {
    service.getEventsList().then((res) => {
        if (res.code === 200) {
            events.value = res.data
        }
    })
}

const goActive = async (item) => {
    if (!item.read) {
        await service.sendActivityReaded(item._id)
        if (store.activeNum) {
            store.activeNum = store.activeNum - 1
        }
        getList()
    }
    if (/^http/i.test(item.link)) {
        location.href = item.link
    } else {
        router.push({
            path: item.link,
            query: {
                id: item.actid,
            },
        })
    }
}
onBeforeMount(() => {
    getList()
})
</script>
<style lang="scss" scoped>
.event-wrap {
    .event-item {
        height: 280px;
        margin-bottom: 40px;

        .event-pic,
        .van-badge__wrapper {
            width: 100%;
        }
        .event-pic {
            border-radius: 30px;
        }

        .event-title {
            @apply flex items-center;
            width: 100%;
            height: 50px;
            padding-left: 60px;
            line-height: 50px;
            font-size: 24px;
            font-weight: bold;
            color: #fff;

            &.read {
                color: #6d8bbb;
            }
            // .time {
            //     font-size: 18px;
            // }
        }
    }
    .tip {
        font-size: 20px;
    }
}
</style>
