<template>
    <div>
        <div class="chat-item">
            <div class="chart-avatar"><img :src="type === '10000' ? System : Transition" /></div>
            <div class="chat-content">
                <p v-if="item.time" class="chat-time">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</p>
                <p class="chat-pop" v-html="text"></p>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import System from '@/assets/img/system.png'
import Transition from '@/assets/img/transition.png'
defineProps({
    item: {
        type: Object,
        default: () => ({}),
    },
    text: {
        type: String,
        default: () => '',
    },
    type: {
        type: String,
        default: () => '',
    },
})
</script>
<style lang="scss" scoped>
.chat-item {
    @apply flex;
    margin: 40px 0;

    .chart-avatar {
        margin-right: 20px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #fff;
    }
    .chat-time {
        font-size: 20px;
    }
    .chat-content {
        max-width: 84%;
    }
    .chat-pop {
        padding: 26px 32px;
        border-radius: 30px;
        min-height: 76px;
        background-color: #65605e;
        font-family: San-Francisco-Text-Regular;
        font-size: 24px;
        color: #fff;
        border-top-left-radius: 0;
    }
}
</style>
