<template>
    <Chat ref="charComp" class="trans-view" v-model="store.unReadCount[getChannel(20000, store.userInfo.uid)]" userId="20000" :showEmptyTip="false">
        <template #default="{ list, state }">
            <van-empty v-if="state.finish && !list.length">{{ $t('No_more_content') }}</van-empty>
            <template v-else>
                <ChatItem
                    :id="`chat${item?.id}`"
                    :class="{ 'chat-bot': true }"
                    v-for="(item, index) in list"
                    :key="index"
                    :item="item"
                    type="20000"
                    :text="$t(item.content.text, translateTtoText(item))"
                />
            </template>
        </template>
    </Chat>
</template>
<script lang="ts" setup>
import { useBaseStore } from '@/stores'
import { MESSAGE_CODE } from '@/enums'
import ChatItem from '../chatItem.vue'
import Chat from '@/components/chat/chat.vue'
import { useSubscript } from '../hook/useSubscribe'
import { getChannel } from '@/utils'

const props = defineProps({
    active: {
        type: Boolean,
        default: () => false,
    },
})

const store = useBaseStore()
const { deSubscribe, subscribe, translateTtoText } = useSubscript(getChannel(MESSAGE_CODE.TRANSACTION, store.userInfo.uid))

const charComp = ref(null)
watch(
    () => props.active,
    (val) => {
        if (val) {
            store.$patch((state) => {
                state.unReadCount[getChannel(MESSAGE_CODE.TRANSACTION, store.userInfo.uid)] = 0
            })
            subscribe()
            charComp.value?.onScrollBottom()
        } else {
            deSubscribe()
        }
    },
    {
        immediate: true,
    }
)
</script>
<style lang="scss" scoped>
.trans-view {
    padding-top: 0;
}
:deep(.van-empty__bottom) {
    font-size: 20px;
}
</style>
