<template>
    <Chat ref="charComp" class="trans-view" v-model="store.unReadCount[getChannel(10000, store.userInfo.uid)]" userId="10000">
        <template #default="{ list }">
            <ChatItem
                :id="`chat${item.id}`"
                :class="{ 'chat-bot': true }"
                v-for="(item, index) in list"
                :key="index"
                :item="item"
                type="10000"
                :text="$t(item.content.text, translateTtoText(item))"
            />
        </template>
    </Chat>
</template>
<script lang="ts" setup>
import { useBaseStore } from '@/stores'
import ChatItem from '../chatItem.vue'
import Chat from '@/components/chat/chat.vue'
import { useSubscript } from '../hook/useSubscribe'
import { MESSAGE_CODE } from '@/enums'
import { getChannel } from '@/utils'

const store = useBaseStore()
const { deSubscribe, subscribe, translateTtoText } = useSubscript(getChannel(MESSAGE_CODE.SYSTEM, store.userInfo.uid))
const props = defineProps({
    active: {
        type: Boolean,
        default: () => false,
    },
})
const charComp = ref(null)
watch(
    () => props.active,
    (val) => {
        if (val) {
            store.$patch((state) => {
                state.unReadCount[getChannel(MESSAGE_CODE.SYSTEM, store.userInfo.uid)] = 0
            })
            subscribe()
            charComp.value?.onScrollBottom()
        } else {
            deSubscribe()
        }
    },
    {
        immediate: true,
    }
)
</script>
<style lang="scss" scoped>
.trans-view {
    padding-top: 0;
}
:deep(.van-empty__bottom) {
    font-size: 20px;
}
</style>
