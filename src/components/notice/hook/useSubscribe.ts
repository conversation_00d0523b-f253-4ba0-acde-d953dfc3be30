import { useBaseStore } from '@/stores'
import { formatNumber } from '@/utils/toolsValidate'
import { decodeName } from '@/utils'

export const useSubscript = (channel) => {
    const store = useBaseStore()
    const subscribe = () => {
        return store.subscribe(channel)
    }
    const deSubscribe = () => {
        store.deSubscribe(channel)
    }
    const translateTtoText = (item) => {
        const { text, context } = item.content
        switch (text) {
            case '${bindshare}':
                return {
                    uid: context.uid,
                }
            case '${payment}':
                return {
                    count: formatNumber(context.item.worth.gold),
                    name: context.item.currency,
                }
            case '${receiver.gold}':
                return {
                    count: formatNumber(context.gold),
                    name: context.currency,
                }
            case '${receiver.bindtelephone}':
                return {
                    count: context.gold,
                }
            case '${receiver.signActivitiy}':
                return {
                    count: formatNumber(context.addreward?.bonus || context.addreward?.gold),
                    currency: context.currency,
                }
            case '${receiver.lostmoneyrebate}':
                return {
                    count: formatNumber(context.addreward?.bonus || context.addreward?.gold),
                    // currency: context.currency,
                }
            case '${receiver.paymentrebate}':
                const { addreward, currency } = context
                return {
                    count: formatNumber(addreward.gold + addreward.bonus),
                    currency: currency,
                }
            case '${receiver.reliefaward}':
                return {
                    count: formatNumber(context.addreward.gold),
                    currency: context.currency,
                }
            case '${payout.ok}':
                return {
                    count: context.amount,
                }
            case '${receiver.directorActivitiy@register}':
                return {
                    gold: formatNumber(context.gold),
                    currency: context.currency,
                }
            case '${receiver.directorActivitiy@montday}':
                return {
                    gold: formatNumber(context.gold),
                    currency: context.currency,
                }
            case '${receiver.rebate}':
                return {
                    gold: formatNumber(context.addreward.gold),
                }
            case '${receiver.ladderloserebate}':
                return {
                    gold: formatNumber(context.addreward.gold),
                }
            case '${message_transfer_touser_1}':
            case '${message_transfer_touser_2}':
            case '${message_transfer_touser_3}':
                return {
                    currency_type: context.currency,
                    number2: formatNumber(context.gold),
                    Username: decodeName(context.nickname),
                }
            case '${reels_transaction_info}':
                return {
                    currency_type: context.currency,
                    number1: context.gold,
                    reels_name: context.name,
                    EP1: context.index === 0 ? '' : context.index,
                }
            default:
                return {}
        }
    }

    return {
        subscribe,
        deSubscribe,
        translateTtoText,
    }
}
