<template>
    <div :class="['earn-animate', { active: show }]" @animationend="animationend"></div>
</template>
<script setup lang="ts">
import { preloadImages } from '@/utils'
import { useBaseStore } from '@/stores'

const store = useBaseStore()

const imagePaths = import.meta.glob('../assets/img/coin-animate/*', { eager: true, as: 'url' })

const timer = ref(null)
const show = ref(false)

const seconds = 5 * 60 * 1000

const getStatus = () => {
    const now = Date.now()
    if (now - store.coinData.date > seconds) {
        store.coinData.date = now
        show.value = true
    }
}

const animationend = () => {
    show.value = false

    timer.value = setTimeout(() => {
        getStatus()
    }, seconds)
}
const loadImg = () => {
    preloadImages(Object.values(imagePaths)).then(() => {
        store.coinData.finish = true
        store.coinData.date = Date.now()
        nextTick(() => {
            show.value = true
        })
    })
}

onBeforeMount(() => {
    if (store.coinData.finish) {
        return getStatus()
    }
    setTimeout(() => {
        if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
                loadImg()
            })
        } else {
            loadImg()
        }
    }, 5000)
})
onMounted(() => {})
onBeforeUnmount(() => {
    if (timer.value) {
        clearInterval(timer.value)
        timer.value = null
    }
    show.value = false
})
</script>
<style lang="scss" scoped>
.earn-animate {
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 187px;
    height: 256px;
    z-index: 100;
    background: url('@/assets/img/coin-animate/feijinbi_0.png') no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    transform: translate3d(-50%, 15%, 0);
    opacity: 0;

    &.active {
        opacity: 1;
        animation: coinFly 1s steps(18);
    }
}

@keyframes coinFly {
    @for $i from 0 through 18 {
        #{$i * 5.5}% {
            background-image: url('@/assets/img/coin-animate/feijinbi_#{$i}.png');
        }
    }
}
</style>
