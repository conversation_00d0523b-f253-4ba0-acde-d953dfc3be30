<template>
    <van-overlay :show="showed" :lock-scroll="lockScroll" v-bind="$attrs" @click="handleClose" z-index="3001">
        <div class="dialog" @click.stop>
            <div :class="['dialog-head', { active: showLine }]">
                <slot v-if="$slots.head" name="head"></slot>
                <p v-else>{{ $t(title) }}</p>
                <div v-if="showClose" class="close-icon" @click="handleClose"></div>
            </div>
            <div class="dialog-content">
                <slot></slot>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="commonDialog">
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockScroll: {
        type: Boolean,
        default: () => false,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
    closeOnClickOverlay: {
        type: Boolean,
        default: () => true,
    },
    showLine: {
        type: Boolean,
        default: () => true,
    },
})

const emits = defineEmits(['close', 'update:modelValue'])
const showed = defineModel()

const handleClose = () => {
    if (!props.closeOnClickOverlay) {
        return
    }
    showed.value = false
    emits('close')
}

const bodyScrollType = computed(() => (props.lockBodyScroll ? 'hidden' : 'auto'))
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 684px;
    // max-height: 65vh;
    background-image: linear-gradient(#2a2d3d, #2a2d3d), linear-gradient(#2a2d3b, #2a2d3b);
    background-blend-mode: normal, normal;
    border-radius: 30px;
    border: solid 2px #4a4a4a;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-head {
    position: relative;
    height: 96px;
    font-size: 40px;
    font-weight: bold;
    color: #ffffff;

    &.active {
        &::after {
            position: absolute;
            bottom: 5px;
            left: 50%;
            content: '';
            width: 620px;
            height: 4px;
            background-color: #ffffff;
            border-radius: 2px;
            opacity: 0.2;

            transform: translate3d(-50%, 0, 0);
        }
    }
    p {
        line-height: 95px;
        text-align: center;
    }
}
.dialog-content {
    flex: 1;
    overflow: v-bind(bodyScrollType);
}
.close-icon {
    position: absolute;
    top: 30px;
    right: 20px;
    width: 33px;
    height: 34px;
    background: url('@/assets/img/new-home/close.png') no-repeat;
    background-size: 100% 100%;
}
</style>
