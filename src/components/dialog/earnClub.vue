<template>
    <van-overlay :show="showed" v-bind="$attrs" @click="handleClose">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="earn-title">{{ $t('Join our VIP Elite Club') }}</div>
                <div class="earn-text">
                    <p>{{ $t('earnMoney_pop_up_text1') }}</p>
                    <p>{{ $t('earnMoney_pop_up_text2') }}</p>
                    <p>{{ $t('earnMoney_pop_up_text3') }}</p>
                </div>
                <div class="earn-btn" @click="goPage">{{ $t('Alamin ang Elite Club') }}</div>
            </div>
            <div class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="earn-dialog">
import { showENStar } from '@/api/earnCash'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
})
const emits = defineEmits(['close', 'update:modelValue'])
const showed = defineModel()

const goPage = () => {
    showENStar()
    window.open('https://t.me/astronautime')
    showed.value = false
}
const handleClose = () => {
    showed.value = false
    emits('close')
}
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 700px;
    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    padding: 35px;
    height: 900px;
    background: url('@/assets/img/dialog/earn_bg.png') no-repeat;
    background-size: 100% 100%;
    color: #fff;
    font-size: 30px;
    .earn-title {
        padding-top: 10px;
        width: 313px;
        line-height: 54px;
        font-size: 40px;
        font-weight: bold;
    }

    .earn-btn {
        margin: 60px auto 0;
        width: 400px;
        height: 80px;
        line-height: 80px;
        background-image: linear-gradient(100deg, #1acf9a 0%, #13b187 65%, #0c9274 100%), linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        border-radius: 40px;
        text-align: center;
    }
    .earn-text {
        p {
            margin: 40px 0;
        }
    }
}
.close-icon {
    margin: 50px auto 0;
    width: 80px;
    height: 80px;
    background: url('@/assets/img/dialog/sold_close.png') no-repeat;
    background-size: 100% 100%;
}
</style>
