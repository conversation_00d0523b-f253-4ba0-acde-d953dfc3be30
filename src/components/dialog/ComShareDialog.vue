<template>
    <van-popup class="share-pop" v-model:show="showed" round position="bottom" closeable>
        <slot></slot>
        <div class="title">
            {{ $t('Invite_Friends') }}
            <!-- <van-icon name="close" @click="showed = false" /> -->
        </div>
        <van-tabs class="share-tab" v-model:active="activeTab" :animated="true">
            <van-tab v-for="index in 3" :title="'Option ' + index" :key="index">
                <div class="scrollable-content">
                    {{ $t('common_invite_share_txt_' + index) }}
                </div>
            </van-tab>
        </van-tabs>
        <div class="share-actions">
            <div class="button-grid">
                <button class="grid-item" v-for="item in options" :key="item.name" @click="hanldeShare(item)">
                    <img :src="item.icon" />
                </button>
            </div>
        </div>
        <div class="share-link">
            <div class="share-link-title">
                <span>{{ $t('common_invite_share_txt1') }}</span>
            </div>
            <div class="share-link-content">
                <!-- <span>{{ $t('common_invite_share_txt2') }}:</span> -->
                <van-text-ellipsis rows="1" :content="comurl" />
                <span @click="handleCopy(comurl)"></span>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
const emits = defineEmits(['shared'])
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
import { useClipboard1 } from '@/hooks/useCopy'
import FBIcon from '/images/facebook.png'
import Telegram from '@/assets/img/share/t_logo.png'
import WhatsApp from '@/assets/img/share/whatsapp.svg'
import Twitter from '@/assets/img/share/twitter.png'
import More from '@/assets/img/share/more.png'
import { SAJSShareContent } from '../common/SAJSBrige'
// import { encode } from 'js-base64'
// import { getDeviceInfo } from '@/utils'
const { toClipboard } = useClipboard1()
const store = useBaseStore()
const i18n = useI18n()
const props = defineProps({
    social: {
        type: Array,
        default: () => ['facebook', 'SystemShare'],
    },
    config: {
        type: Object,
        default: () => ({}),
    },
})

const comurl = ref('')

const defaulttitle = 'betfugu.com'
const currencyPageMap = {
    PHP: 'app_ph',
    USD: 'app_us',
    TRY: 'app_tr',
}
const options = computed(() => {
    if (store.isTg) {
        return [
            {
                name: 'telegram',
                type: 'telegram',
                icon: 'https://betfugu.com/static/img/telegram.png',
            },
        ]
    }
    return [
        {
            name: 'facebook',
            type: 'facebook',
            icon: FBIcon,
            detail: 'https://www.facebook.com/sharer/sharer.php?u=你的网址',
        },
        {
            name: 'telegram',
            type: 'telegram',
            icon: Telegram,
            detail: 'https://t.me/share/url?url=你的链接&text=分享文字',
        },
        {
            name: 'Whatsapp',
            type: 'whatsapp',
            icon: WhatsApp,
            detail: 'https://wa.me/?text=你的分享文字%20加上%20网址',
        },
        {
            name: 'Twitter',
            type: 'Twitter',
            icon: Twitter,
            detail: 'https://twitter.com/intent/tweet?text=分享文字&url=网址',
        },
        {
            name: 'SystemShare',
            type: 'SystemShare',
            icon: More,
        },
    ]
})

const showed = defineModel<boolean>()
const activeTab = ref(2)

watch(
    () => activeTab.value,
    (val) => {
        console.log(val)
    }
)

const isWebShareSupported = () => {
    return navigator && navigator.share && typeof navigator.share === 'function'
}

const shareContent = async (options) => {
    if (!isWebShareSupported()) {
        return
    }

    try {
        await navigator.share({
            title: options.title || defaulttitle,
            text: options.text || defaulttitle,
            url: options.url || defaulttitle,
        })
        return
    } catch (error) {
        // showToast(i18n.t('Withdrawal_failed'))
    }
}

const handleCopy = (text) => {
    toClipboard(`${text}`)
    showToast({
        message: i18n.t('Copy_successfully'),
        zIndex: 4000,
    })
}

const hanldeShare = async (option) => {
    let host = `https://betfugu.com/${currencyPageMap[store.wallet.currency] || 'app_ph'}.html`
    if (import.meta.env.MODE !== 'production') {
        host = 'https://betfugu.com/app_test.html'
    }
    const shareOption = props.config
    const stxt = i18n.t('common_invite_share_txt_' + (activeTab.value + 1))
    if (!shareOption.url) {
        let url = `${host}?userId=${store.userInfo.uid}&currency=${store.wallet?.currency}&partner=${store.userInfo.partner}`
        shareOption.url = url
        console.log('url', url)
    }
    if (shareOption.from) {
        shareOption.url += `&sfrom=${shareOption.from}`
    }
    if (option.type === 'facebook') {
        const surl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareOption.url)}&&quote=${encodeURIComponent(stxt)}`
        window.open(surl)
    } else if (option.type === 'telegram') {
        const surl = `https://t.me/share/url?url=${encodeURIComponent(shareOption.url)}&text=${encodeURIComponent(stxt)}`
        window.open(surl)
    } else if (option.type === 'viber') {
        window.open(`viber://forward?text=${encodeURIComponent(shareOption.url) || shareOption.text || ''} `)
    } else if (option.type === 'code') {
        toClipboard(`${store.userInfo.uid}`)
        showToast({
            message: 'copy code successful',
            zIndex: 4000,
        })
    } else if (option.type === 'SystemShare') {
        if (store.ntvfrom === 'selfapk') {
            SAJSShareContent(shareOption.url, stxt, defaulttitle)
        } else {
            shareContent({
                title: defaulttitle,
                text: stxt || defaulttitle,
                url: shareOption.url || defaulttitle,
            })
        }
    } else if (option.type === 'whatsapp') {
        const surl = `https://wa.me/?text=${encodeURIComponent(stxt)}%20${encodeURIComponent(shareOption.url)}`
        window.open(surl)
    } else if (option.type === 'Twitter') {
        const surl = 'https://twitter.com/intent/tweet?text=' + encodeURIComponent(stxt) + '&url=' + encodeURIComponent(shareOption.url)
        window.open(surl)
    } else {
        toClipboard(shareOption.url)
        showToast({
            message: 'copy link successful',
            zIndex: 4000,
        })
    }
    emits('shared')
}

onMounted(() => {
    let host = `https://betfugu.com/${currencyPageMap[store.wallet.currency] || 'app_ph'}.html`
    if (import.meta.env.MODE !== 'production') {
        host = 'https://betfugu.com/app_test.html'
    }
    let url = `${host}?userId=${store.userInfo.uid}&currency=${store.wallet?.currency}&partner=${store.userInfo.partner}`
    const shareOption = props.config
    if (shareOption.from) {
        url += `&sfrom=${shareOption.from}`
    }
    comurl.value = url
})
</script>
<style lang="scss" scoped>
.share-pop {
    background-color: #202222;
    width: 100%;
    max-height: auto;
    border: 0px;
    overflow: hidden;

    .title {
        margin-top: 30px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        font-size: 38px;
        color: #fff;
    }
    .share-tab {
        padding: 20px;
        padding-bottom: 0px;
    }
    :deep(.van-tabs) {
        margin-top: 20px;
        margin-bottom: 20px;
        .van-tabs__wrap {
            height: 85px;
            padding: 0;
        }
        .van-tabs__nav {
            background-color: #2c3031;
            height: 60px;
            padding: 6px;
            border-radius: 20px;
            font-size: 30px;
            .van-tab--line {
                height: 100%;
                line-height: 100%;
                margin-right: 0;
                color: #fffa;
                font-size: 30px;
                background-color: transparent;
            }
            .van-tab--active {
                background-color: #3b4041;
                color: #fff;
            }
        }
        .van-tabs__content {
            margin-top: 0px;
            height: 230px;
            background-color: #1c1e1d;
            border: #3b4041 1px solid;
            border-radius: 15px;
            padding: 10px;
            .van-swipe__track {
                width: 100%;
                color: rgb(145, 151, 145);
                font-size: 26px;
                font-weight: 500;
                .van-tab__panel {
                    width: 100%;
                    height: 100%;
                    .scrollable-content {
                        height: 100%;
                        overflow-y: auto;
                    }
                }
            }
        }
    }

    .share-actions {
        background-color: #2c3031;
        border: 0px;
        height: 170px;
        .button-grid {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px 16px;

            .grid-item {
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 10px;
            }
        }
    }

    .share-link {
        background-color: #2c3031;
        border: 0px;
        height: 160px;
        width: 100%;
        padding: 0px 25px;
        .share-link-title {
            text-align: left;
            font-size: 28px;
            color: #fffa;
            margin-bottom: 10px;
        }
        .share-link-content {
            border: #9ba2a4 1px solid;
            padding: 0px 15px;
            min-height: 70px; // height: 70px;
            width: 100%;
            border-radius: 15px;
            font-size: 26px;
            font-weight: 500;
            color: #fffb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            :deep(.van-text-ellipsis) {
                max-width: 90%;
            }

            span {
                margin-left: 8px;
                width: 30px;
                height: 32px;
                background: url('@/assets/img/new-home/copy.png') no-repeat center;
                background-size: 100% 100%;
                cursor: pointer;
            }
        }
    }
}
</style>
