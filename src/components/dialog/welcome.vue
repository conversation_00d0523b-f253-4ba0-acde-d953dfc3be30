<template>
    <van-overlay v-model:show="visible">
        <div class="dialog">
            <div class="dialog-close" @click="handleClose"></div>
            <div class="dialog-title" v-html="$t('welcome_title')"></div>
            <div class="dialog-tips" v-html="$t('welcome_info')"></div>
            <div class="dialog-btn" @click="handleClick">{{ $t(store.userInfo?.telephone ? 'Confirm' : 'welcome_button') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import eventBus from '@/utils/bus'
import { useBaseStore } from '@/stores'
import popupManager from '@/utils/PopupManager'

const visible = defineModel()
const store = useBaseStore()

const handleClose = (clsoed = true) => {
    visible.value = false
    if (clsoed) {
        popupManager.checkQueue()
    }
}
const handleClick = () => {
    // eventBus.emit('activity', {
    //     param: '/bindPhone',
    // })
    handleClose()
}
const init = () => {
    if (store.wallet?.currency === 'PHP' && store.newUser) {
        visible.value = true
    } else {
        popupManager.checkQueue()
    }
}
onMounted(() => {
    init()
})
</script>
<style scoped lang="scss">
.dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 710px;
    height: 766px;
    padding: 0 62px;
    background: url('@/assets/img/dialog/welcome_bg.png') no-repeat;
    background-size: 100% 100%;
    font-family: MicrosoftYaHei;
    color: #fff;

    transform: translate3d(-50%, -50%, 0);

    .dialog-close {
        position: absolute;
        right: 36px;
        top: 49px;
        width: 35px;
        height: 36px;
        background: url('@/assets/img/dialog/welcome_shut.png') no-repeat;
        background-size: 100% 100%;
    }
    .dialog-title {
        padding-top: 80px;
        font-size: 38px;
        line-height: 34px;
        box-shadow: 0px 3px 5px 1px rgba(37, 0, 161, 0.14);
    }
    .dialog-tips {
        // margin-top: 190px;
        position: absolute;
        top: 320px;
        width: 600px;

        height: 170px;
        padding: 0 20px 0 15px;
        font-size: 23px;
        font-weight: bold;
    }

    .dialog-btn {
        position: absolute;
        bottom: 113px;
        margin: 85px auto 0;
        width: 544px;
        height: 100px;
        line-height: 100px;
        text-align: center;
        background-image: linear-gradient(rgba(241, 153, 28, 0.55), rgba(241, 153, 28, 0.55)),
            linear-gradient(150deg, #ffc44f 0%, #ffb944 40%, #ffad38 100%);
        background-blend-mode: normal, normal;
        border-radius: 49px;
        font-size: 48px;
        font-weight: bold;
    }
}
</style>
