<template>
    <van-overlay :show="showed" v-bind="$attrs" @click="handleClose">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="redeem-input">
                    <van-field v-model="giftCode" :placeholder="$t('Please_enter_the_redemption')" :maxlength="12" />
                </div>
                <div class="redeem" @click="receiveCash">{{ $t('Confirm') }}</div>
            </div>
            <div v-if="showClose" class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="redeem-dialog">
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockBodyScroll: {
        type: <PERSON>ole<PERSON>,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
})
const store = useBaseStore()
const i18n = useI18n()

const emits = defineEmits(['close', 'update:modelValue'])
const showed = defineModel()

const giftCode = ref('')

const handleClose = () => {
    showed.value = false
    emits('close')
}

const receiveCash = async () => {
    if (!giftCode.value) {
        return showToast(i18n.t('Redemption_code_no_null'))
    }
    if (!/^[A-Za-z0-9]{12}$/.test(giftCode.value)) {
        return showToast(i18n.t('Redemption_code_uncor'))
    }
    const res = await store.exchangeGiftCdk(giftCode.value)
    if (res.code === 200) {
        showed.value = false
        showDialog({
            message: i18n.t('Gift_redemption_succ'),
            className: 'common-dialog',
            showCancelButton: false,
            confirmButtonText: i18n.t('Confirm'),
            cancelButtonText: i18n.t('Cancel'),
        })
    }
}
// watch(showed, (val) => {
//     if (val) {
//         navigator.clipboard.readText().then((clipText) => {
//             if (/^[A-Za-z0-9]{16}$/.test(clipText)) {
//                 giftCode.value = clipText
//             }
//         })
//     }
// })
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 742px;
    max-height: 900px;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    height: 873px;
    padding-top: 420px;
    background: url('@/assets/img/redeem-dialog/CDK.png') no-repeat;
    background-size: 100% 100%;

    .redeem-input {
        margin: 0 auto;
        width: 620px;
        // height: 104px;
        border-radius: 52px;
        overflow: hidden;

        :deep(.van-field) {
            padding: 0;
            .van-field__control {
                box-sizing: border-box;
                height: 104px;
                padding: 37px 56px;
                font-size: 30px;
                font-weight: bold;
                color: #999999;
            }
        }
    }
    .redeem {
        width: 610px;
        height: 104px;
        line-height: 104px;
        margin: 100px auto 0;
        background-image: linear-gradient(90deg, #5be8fe 0%, #0069b5 100%), linear-gradient(90deg, #5bddf9 0%, #2ea3d7 40%, #0069b5 100%);
        background-blend-mode: normal, normal;
        border-radius: 52px;
        font-size: 48px;
        font-weight: Bold;
        color: #fff;
        text-align: center;
    }
}
.close-icon {
    position: absolute;
    top: -40px;
    right: 30px;
    width: 32px;
    height: 32px;
    background: url('@/assets/img/home-notice/close.png') no-repeat;
    background-size: 100% 100%;
}
</style>
