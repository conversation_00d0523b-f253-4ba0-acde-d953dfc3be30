<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="300">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="cash-title">{{ $t('popup_bonus_title') }}</div>
                <div class="cash-num">
                    <span>{{ store.cureencySymbol() }}</span
                    >{{ formatNumber(gold) }}
                </div>
                <div class="cash-tip" v-html="$t('popup_bonus_info')"></div>
            </div>
            <div class="close-icon" @click="receiveCash">{{ $t('Claim') }}</div>
            <div
                v-if="store.vipRules?.dailyFlowmult"
                class="tip"
                v-html="
                    $t('claim_cash_times_info', {
                        number: store.vipRules?.dailyFlowmult,
                    })
                "
            ></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="cash-dialog">
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import { formatNumber } from '@/utils/toolsValidate'
import popupManager from '@/utils/PopupManager'

const store = useBaseStore()
const i18n = useI18n()
defineProps({
    showClose: {
        type: Boolean,
        default: () => false,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
})

const emits = defineEmits(['close'])
const showed = ref(false)
const gold = ref(0)
const handleClose = () => {
    showed.value = false
    emits('close')
}
const receiveCash = async () => {
    try {
        const res = await store.pickVipRewards({
            type: 'daily',
            level: store.vipInfo.curLevel,
        })
        if (res.code === 200) {
            showed.value = false
            showSuccessToast(i18n.t('message_claim_success'))
            popupManager.checkQueue()
        } else {
            showFailToast(i18n.t('message_claim_fail'))
        }
    } catch (e) {
        console.log(e)
    }
    handleClose()
}

const getBonus = async () => {
    try {
        const info = await store.getVipInfo(false)
        if (info.code === 200) {
            store.setvipInfo(info)
            const golds = info.info?.dailyReward
            if (golds) {
                showed.value = true
                gold.value = golds
            } else {
                popupManager.checkQueue()
            }
        } else {
            popupManager.checkQueue()
        }
    } catch (e) {
        console.log(e)
        popupManager.checkQueue()
    }
}
onMounted(() => {
    getBonus()
})
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 634px;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    width: 100%;
    height: 881px;
    padding-top: 145px;
    background: url('@/assets/img/dialog/bonus_bg.png') no-repeat;
    background-size: 100% 100%;

    .cash-title {
        padding-left: 200px;
        font-family: MicrosoftYaHei;
        font-size: 37px;
        font-weight: bold;
        font-style: italic;
        color: #b97363;
    }
    .cash-num {
        width: 500px;
        padding-left: 50px;
        margin-top: 120px;
        font-family: MicrosoftYaHei;
        font-size: 112px;
        font-weight: bold;
        color: #f7534a;

        transform: rotateZ(10deg);
        span {
            position: relative;
            top: -20px;
            font-size: 81px;
            font-weight: 400;
        }
    }
    .cash-tip {
        @apply flex justify-center items-center;
        margin-top: 250px;
        padding: 0 60px;
        height: 144px;
        font-family: MicrosoftYaHei;
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        color: #b97363;
    }
}
.close-icon {
    margin: 30px auto 0;
    width: 457px;
    height: 105px;
    line-height: 105px;
    background: url('@/assets/img/dialog/bonus_btn.png') no-repeat;
    background-size: 100% 100%;
    font-family: MicrosoftYaHei;
    font-size: 48px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
}
.tip {
    margin: 10px auto 0;
    width: 457px;
    color: #fff050;
    text-align: center;
    font-family: 'Microsoft YaHei';
    font-size: 36px;
    font-weight: 600;

    :deep(span) {
        color: #ff6c00;
    }
}
</style>
