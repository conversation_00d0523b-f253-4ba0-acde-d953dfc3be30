<template>
    <van-overlay :show="showed" :lock-scroll="lockScroll" v-bind="$attrs">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="title">{{ $t('menu_bind_phone') }}</div>
                <div class="input-wrap">
                    <div class="bind-label">
                        <CountyMobile @countryCodeChange="changeCode" />
                    </div>
                    <van-field class="bind-input" v-model="phoneNum" type="digit" :maxlength="15" :placeholder="$t('Enter_mobile_number')" />
                </div>
                <div class="input-wrap code-input">
                    <div class="bind-label" @click.stop="sendCode">
                        <div class="sended" v-if="codeOpt.isSend">
                            <span v-if="codeOpt.timer">({{ codeOpt.seconds }}s)<br /></span>
                            <span :class="{ 'text-[#000]': !codeOpt.timer }"> {{ $t('Resend') }}</span>
                        </div>
                        <div :class="!!phoneNum ? 'text-[#fff]' : 'text-[#fff]'" v-else>{{ $t('send code') }}</div>
                    </div>
                    <van-field class="bind-input" v-model="codeNum" type="digit" :placeholder="$t('Enter_verification_code')" :maxlength="4" />
                </div>
                <div class="red-tip">{{ $t('bind_phone_security_info') }}</div>
                <div class="bind-btn" @click="handleBind">{{ $t('Bind') }}</div>
            </div>
            <div v-if="showClose" class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup>
import { useCutdown } from '@/hooks/useCutdown'
import CountyMobile from '../countyMobile.vue'
import { useBaseStore } from '@/stores'
import * as service from '@/api'
import md5 from 'md5'
import { isValidPhoneNumber } from 'libphonenumber-js'
import { useI18n } from 'vue-i18n'
import popupManager from '@/utils/PopupManager'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockScroll: {
        type: Boolean,
        default: () => false,
    },
    title: {
        type: String,
        default: () => '',
    },
    canClosed: {
        type: Boolean,
        default: () => true,
    },
    tip: {
        type: String,
        default: () => '',
    },
    type: {
        type: Number,
        default: () => 0,
    },
})
const store = useBaseStore()
const i18n = useI18n()
//

const { codeOpt, doCutDown } = useCutdown()

const emits = defineEmits(['close', 'update:modelValue', 'confirm'])
const showed = defineModel()
const phoneNum = ref('')
const codeNum = ref('')
const areaCode = ref('')

const handleClose = () => {
    if (!props.canClosed) {
        return
    }
    showed.value = false
    store.closeBind = true
    popupManager.checkQueue()
    emits('close')
}
const handleBind = async () => {
    if (!codeNum.value || !phoneNum.value) {
        return
    }
    // @ts-ignore
    if (!isValidPhoneNumber(phoneNum.value, areaCode.value)) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    if (!/\d{4}/.test(`${codeNum.value}`)) {
        return showToast(i18n.t('Incorrect_verification'))
    }
    const res = await service.bindPhone({
        country: areaCode.value, //国家
        code: codeNum.value, //验证码
        telephone: phoneNum.value, //手机号
    })
    if (res.code === 200) {
        showSuccessToast(i18n.t('Binding_successful'))
        store.getProfile()
        emits('confirm')
        showed.value = false
        popupManager.checkQueue()
    }
}
const sendCode = () => {
    if (!phoneNum.value) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    // @ts-ignore
    if (!isValidPhoneNumber(phoneNum.value, areaCode.value)) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    if (codeOpt.timer) {
        return
    }
    codeOpt.times = codeOpt.times + 1
    try {
        service
            .getSms({
                apptitle: 'h5',
                country: areaCode.value,
                grant: 'bindtelephone',
                sendnum: codeOpt.times,
                sign: md5(phoneNum.value),
                telephone: phoneNum.value,
            })
            .then((res) => {
                if (res.code === 200) {
                    codeOpt.isSend = true
                    doCutDown()
                    showToast(i18n.t('Verification_code_ha'))
                }
            })
    } catch (e) {
        console.log(e, 'eeeeeee')
    }
}

const changeCode = (code) => {
    areaCode.value = code
}
const getVipInfo = async (loading = true) => {
    try {
        const info = await store.getVipInfo(loading)
        if (info.code === 200) {
            store.setvipInfo(info)
            if (store.vipInfo.recharge || store.vipInfo.rechargeMax === 0) {
                showed.value = true
            } else {
                popupManager.checkQueue()
            }
        } else {
            popupManager.checkQueue()
        }
    } catch (e) {
        console.log(e)
        popupManager.checkQueue()
    }
}
onBeforeMount(() => {
    console.log('onBeforeMount')
    if (props.type === 2) {
        console.log('00000', props.type)
        return
    }
    if ((props.type === 0 && store.closeBind) || store.userInfo?.telephone) {
        console.log(2222)
        return popupManager.checkQueue()
    }
    console.log(3333)
    if (!store.userInfo.telephone || props.type === 1) {
        console.log(4444)
        if (props.type === 0) {
            if (Object.keys(store.vipInfo).length) {
                if (store.vipInfo.recharge || store.vipInfo.rechargeMax === 0) {
                    showed.value = true
                } else {
                    popupManager.checkQueue()
                }
            } else {
                getVipInfo()
            }
        } else {
            showed.value = true
        }
    } else {
        console.log(5555)
        popupManager.checkQueue()
    }
})
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 742px;
    max-height: 900px;
    font-family: MicrosoftYaHei;
    color: #fff;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    padding-bottom: 40px;
    min-height: 570px;
    background-image: linear-gradient(#2a2d3d, #2a2d3d), linear-gradient(#2a2d3b, #2a2d3b);
    background-blend-mode: normal, normal;
    border-radius: 27px;
    .title {
        @apply flex justify-center items-center;
        position: relative;
        top: -36px;
        margin: 0 auto;
        width: 470px;
        height: 96px;
        background: url('@/assets/img/wallets/bind_title.png') no-repeat;
        background-size: 100% 100%;
        font-size: 36px;
        font-weight: bold;
    }
    .input-wrap {
        display: flex;
        width: 620px;
        height: 104px;
        margin: 30px auto;
        background-color: #444b5e;
        border-radius: 22px;

        .bind-label {
            position: relative;
            width: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            font-weight: bold;
            color: #fff;
            text-align: center;
            line-height: 30px;

            &::before {
                position: absolute;
                right: 0;
                content: '';
                width: 4px;
                height: 56px;
                background-color: #747a8d;
            }
            .sended {
                color: #999999;
            }
            :deep(.code-icon) {
                width: 50px;
                text-indent: 12px;
            }
            :deep(.coutry) {
                &::after {
                    display: inline-block;
                    width: 0;
                    height: 0;
                    border-left: 10px solid transparent; /* 左边框透明 */
                    border-right: 10px solid transparent; /* 右边框透明 */
                    border-top: 10px solid #fff; /* 顶部边框为黑色，形成尖角 */
                }
            }
            :deep(.coutry-input) {
                text-align: center;
                // padding-right: 0;
            }
        }
        .bind-input {
            border-radius: 0 52px 52px 0;
        }
        :deep(.van-field__body) {
            height: 100%;

            .van-field__control {
                font-size: 36px;
                color: #ffffff;

                &::placeholder {
                    font-size: 32px;
                    color: #747a8d;
                }
            }
        }
        :deep(.van-cell) {
            background-color: #444b5e;
            border-radius: 22px;
        }
    }
    .code-input {
        // margin-bottom: 85px;
    }
    .bind-btn {
        margin: 0 auto;
        width: 400px;
        height: 88px;
        line-height: 88px;
        background-image: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        font-size: 48px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
    .red-tip {
        margin: 0 auto 16px;
        width: 378px;
        font-family: MicrosoftYaHei;
        font-size: 24px;
        color: #ff9821;
        &::before {
            display: inline-block;
            content: '';
            position: relative;
            top: 5px;
            width: 30px;
            height: 26px;
            background: url('@/assets/img/wallets/exclam_mark.png') no-repeat;
            background-size: 100% 100%;
        }
    }
}
.close-icon {
    position: absolute;
    top: 21px;
    right: 21px;
    width: 64px;
    height: 64px;
    background: url('@/assets/img/home-notice/close.png') no-repeat right top;
    background-size: 32px 32px;
}
</style>
