<template>
    <van-overlay :show="showed" v-bind="$attrs" @click="handleClose">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <van-field
                    class="bind-input"
                    @update:model-value="checkUser"
                    v-model="inviterCode"
                    type="digit"
                    :placeholder="$t('Enter_your_inviter_ID')"
                    :maxlength="7"
                />
                <div :class="['cash-btn', { 'opacity-50': !inviterCode || inviterCode == store.userInfo.id || !canClick }]" @click="handleBind">
                    {{ $t('Bind') }}
                </div>
            </div>
            <div v-if="showClose" class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="inviter-dialog">
import { useBaseStore } from '@/stores'
import { showDialog } from 'vant'
import { useI18n } from 'vue-i18n'
import { getUserInfo } from '@/api/index'
import popupManager from '@/utils/PopupManager'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
    canClosed: {
        type: Boolean,
        default: () => true,
    },
})

const store = useBaseStore()
const i18n = useI18n()

const emits = defineEmits(['close', 'update:modelValue', 'confirm'])
const showed = ref(false)
const inviterCode = ref('')
const canClick = ref(false)

const checkUser = (val) => {
    if (val.length === 7) {
        getUserInfo([val]).then((res) => {
            if (res.code === 200) {
                if (Object.keys(res.users[val]).length) {
                    canClick.value = true
                } else {
                    showToast(i18n.t('ID_no_exist'))
                }
            }
        })
    }
}

const handleClose = () => {
    if (!props.canClosed) {
        return
    }
    showed.value = false
    popupManager.checkQueue()
    emits('close')
}
const handleBind = async () => {
    if (!canClick.value) {
        return
    }
    if (!inviterCode.value) {
        return showToast(i18n.t('Enter_the_correct_ID'))
    }
    if (+inviterCode.value == store.userInfo.uid) {
        return showToast(i18n.t('Cannot_bind_yourself'))
    }
    showDialog({
        className: 'common-dialog',
        title: `${i18n.t('Confirm')}?`,
        message: `${i18n.t('Dyw_bind_inviter_ID')}: ${inviterCode.value}?`,
        showCancelButton: true,
        confirmButtonText: 'confirm',
        cancelButtonText: 'cancel',
    })
        .then(async () => {
            let sfrom = ''
            // if (import.meta.env.MODE !== 'production') {
            //     sfrom = 'redfission'
            // }
            const res = await store.bindInviter({
                shareid: +inviterCode.value, // 邀请人id
                auto: true, //是否自动绑定
                from: sfrom, //渠道来源
            })
            if (res.code === 200) {
                showSuccessToast(i18n.t('Successfully_bound'))
                emits('confirm')
                showed.value = false
                popupManager.checkQueue()
            } else if (res.code === 302) {
                showToast(i18n.t('Failed_bind_inviter_ID'))
            } else {
                showToast(i18n.t('Failed_bind_inviter_ID'))
            }
        })
        .catch((e) => {
            console.log(e, 'Cancel')
        })
}
onMounted(() => {
    showed.value = true
})
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 742px;
    max-height: 900px;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    height: 873px;
    padding-top: 380px;
    background: url('@/assets/img/events/inviter_bg.png') no-repeat;
    background-size: 100% 100%;

    .bind-input {
        width: 620px;
        height: 104px;
        margin: 0 auto;
        padding: 0;
        border-radius: 52px;
        :deep(.van-field__body) {
            height: 100%;

            .van-field__control {
                height: 100%;
                text-align: center;
                font-size: 48px;
                color: #000000;

                &::placeholder {
                    font-size: 36px;
                    color: #999999;
                }
            }
        }
    }
    .cash-num {
        @apply flex justify-center items-center gap-[46px];
        font-family: MicrosoftYaHeiHeavy;
        font-size: 104px;
        color: #ffff00;

        &::before {
            display: inline-block;
            content: '';
            width: 109px;
            height: 129px;

            background: url('@/assets/img/cash_dialog/cash_icon.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .cash-btn {
        width: 610px;
        height: 104px;
        line-height: 104px;
        margin: 100px auto 0;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb944 40%, #ffad38 100%);
        border-radius: 52px;
        font-size: 48px;
        font-weight: Bold;
        color: #fff;
        text-align: center;
    }
}
.close-icon {
    position: absolute;
    top: -40px;
    right: 30px;
    width: 32px;
    height: 32px;
    background: url('@/assets/img/home-notice/close.png') no-repeat;
    background-size: 100% 100%;
}
</style>
