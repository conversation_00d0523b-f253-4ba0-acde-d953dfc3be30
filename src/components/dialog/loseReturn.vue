<template>
    <div>
        <van-overlay :show="show" @click="handleClose" :z-index="3001">
            <div class="dialog" @click.stop>
                <div class="lose-title"></div>
                <div class="close" @click="handleClose"></div>
                <div class="help" @click="showRule = true"></div>
                <div class="content">
                    <div v-if="status === 1 && !eventStatus.start" class="content-tip">{{ $t('firstpay_intro') }}</div>
                    <div class="content-percent">
                        <img class="percent-tip" src="@/assets/img/lose_return/percent-tip.png" />
                        <img class="percent-num" :src="`//betfugu.com/static/img/playtok/percent/${eventStatus.rate * 100}.png`" />
                    </div>
                    <div v-if="status === 1 && !eventStatus.start" class="contnt-product">
                        <div class="product">
                            <div
                                :class="['content-item', { active: index === actvieIdx }]"
                                v-for="(item, index) in productConfig"
                                :key="index"
                                @click="clickChange(index)"
                            >
                                <img
                                    :class="['coin-bg', `coin-bg${index}`]"
                                    :src="`//betfugu.com/static/img/playtok/loseReturn/newCoins${index + 1}.png`"
                                />
                                <div class="up-tip">
                                    <img class="up-icon" src="@/assets/img/lose_return/UPTO.png" />
                                    <img :src="`//betfugu.com/static/img/playtok/loseReturn/currency/${store.wallet?.currency}.png`" />
                                    <img
                                        v-for="(itd, idx) in `${item.cny}`.split('')"
                                        :key="idx"
                                        :src="`//betfugu.com/static/img/playtok/number/${itd === '.' ? 'dian' : itd}.png`"
                                    />
                                </div>
                                <div class="content-num">
                                    <img :src="`//betfugu.com/static/img/playtok/loseReturn/currency/${store.wallet?.currency}.png`" />
                                    <img
                                        v-for="(itd, idx) in `${item.cny}`.split('')"
                                        :key="idx"
                                        :src="`//betfugu.com/static/img/playtok/number/${itd === '.' ? 'dian' : itd}.png`"
                                    />
                                </div>
                            </div>
                        </div>
                        <div :class="['product-btn', { disabled: actvieIdx === -1 }]" @click="handleDeposit">{{ $t('Recharge') }}</div>
                    </div>
                    <div v-else class="deposit-produt">
                        <div class="product">
                            <div
                                :class="['content-item', { active: item.cny === eventStatus.max }]"
                                v-for="(item, index) in productConfig"
                                :key="index"
                            >
                                <img class="content-deposited" src="@/assets/img/lose_return/deposited.png" />
                                <img
                                    :class="['coin-bg', `coin-bg${index}`]"
                                    :src="`//betfugu.com/static/img/playtok/loseReturn/newCoins${index + 1}.png`"
                                />
                                <div class="content-num">
                                    <img :src="`//betfugu.com/static/img/playtok/loseReturn/currency/${store.wallet?.currency}.png`" />
                                    <img
                                        v-for="(num, index) in `${item.cny}`.split('')"
                                        :key="index"
                                        :src="`//betfugu.com/static/img/playtok/number/${num === '.' ? 'dian' : num}.png`"
                                    />
                                </div>
                                <img class="content-deposite" src="@/assets/img/lose_return/deposite.png" />
                                <img class="product-checked" src="@/assets/img/lose_return/product-checked.png" />
                            </div>
                        </div>
                        <div v-if="status === 1" class="deposit-tip">
                            <div class="text-item">
                                <span>{{ $t('firstpay_details_1') }}</span
                                ><span>{{ store.cureencySymbol() }}{{ eventStatus.max }}</span>
                            </div>
                            <div class="text-item">
                                <span>{{ $t('firstpay_details_2') }}</span
                                ><span>{{ store.cureencySymbol() }}{{ eventStatus.loss < 0 ? '0.00' : eventStatus.loss }}</span>
                            </div>
                            <div class="text-item">
                                <span>{{ $t('firstpay_details_3') }}</span
                                ><span>{{ store.cureencySymbol() }}{{ eventStatus.reward }}</span>
                            </div>
                        </div>
                        <div v-else class="recived">
                            <div class="recived-tip">
                                {{
                                    $t('firstpay_return', {
                                        number: `${eventStatus.rate * 100}%`,
                                    })
                                }}
                            </div>
                            <div class="recived-num">{{ store.cureencySymbol() }}{{ eventStatus.reward }}</div>
                            <div class="recived-btn" @click="recievReward">{{ !eventStatus.reward ? $t('Confirm') : $t('Claim') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </van-overlay>
        <van-overlay :show="showRule" @click="showRule = false" :z-index="3001" :lock-scroll="false">
            <div class="dialog-rule">
                <div class="rule-head">
                    {{ $t('firstpay_rules') }}
                    <div class="close" @click="showRule = false"></div>
                </div>
                <div class="rule-content">
                    <div class="rule-part">
                        <p>1.</p>
                        <p>
                            {{
                                $t('firstpay_rules_1', {
                                    number: `${eventStatus.rate * 100}%`,
                                })
                            }}
                        </p>
                    </div>
                    <div class="rule-part">
                        <p>2.</p>
                        <p>{{ $t('firstpay_rules_2') }}</p>
                    </div>
                    <div class="rule-part">
                        <p>3.</p>
                        <p>{{ $t('firstpay_rules_3') }}</p>
                    </div>
                    <div class="rule-part">
                        <p>4.</p>
                        <p>{{ $t('firstpay_rules_4') }}</p>
                    </div>
                    <div class="rule-part">
                        <p>5.</p>
                        <p>{{ $t('firstpay_rules_5') }}</p>
                    </div>
                </div>
            </div>
        </van-overlay>
        <ProductChannel v-model="showChannel" @recharged="show = false" />
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import ProductChannel from './productChannel.vue'
import eventBus from '@/utils/bus'
import { Log } from '@/api/log'
import popupManager from '@/utils/PopupManager'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})

const store = useBaseStore()
const show = ref(false)
const showRule = ref(false)
const actvieIdx = ref(1)
const status = ref(1)
const showChannel = ref(false)
const productConfig = ref([])
const eventStatus = ref({})

const orderParams = reactive({
    item: {},
    source: 'ladderloserebate',
})
provide('orderParams', orderParams)

// watch([() => store.wallet?.currency, () => store.userInfo?.guideCurrency], () => {
//     getEvents()
// })

const handleClose = () => {
    show.value = false
    popupManager.checkQueue()
}
const clickChange = (index) => {
    actvieIdx.value = index
}
const handleDeposit = () => {
    Log({
        event: 'ladderloserebate',
        content: {
            entrance: 'deposit',
        },
    })
    show.value = false
    showChannel.value = true
    orderParams.item = productConfig.value[actvieIdx.value]
}
const getEvents = async () => {
    try {
        const res = await store.getSignEvent(
            {
                id: 'ladderloserebate',
            },
            false,
            false
        )
        if (res.code === 200) {
            const acticity = res.acticity
            if (acticity.status !== 0) {
                eventBus.emit('loseReturn', true)
                status.value = acticity.status
                eventStatus.value = acticity.meta
                productConfig.value = acticity.products
                show.value = props.type === 1 ? true : acticity.popup
                if (!show.value) {
                    popupManager.checkQueue()
                }
            } else {
                eventBus.emit('loseReturn', false)
                popupManager.checkQueue()
            }
        } else {
            popupManager.checkQueue()
        }
    } catch (e) {
        popupManager.checkQueue()
    }
}
const recievReward = async () => {
    const res = await store.ReceiveRewards({
        id: 'ladderloserebate',
    })
    if (res.code === 200) {
        show.value = false
        eventBus.emit('loseReturn', false)
        popupManager.checkQueue()
    }
}
// onActivated(() => {
//     getEvents()
// })
onBeforeMount(() => {
    getEvents()
})
// onBeforeMount(() => {
//     eventBus.on('ladderloserebate', getEvents)
//     // eventBus.on('currentChange', currencyChange)
// })
// onBeforeUnmount(() => {
//     eventBus.off('ladderloserebate', getEvents)
//     // eventBus.off('currentChange', currencyChange)
// })
</script>
<style lang="scss" scoped>
@keyframes breathe {
    0% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}
.dialog-rule {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 640px;
    height: 980px;
    padding: 0 22px 0 25px;
    transform: translate3d(-50%, -50%, 0);
    background-color: #332a66;
    border-radius: 40px;
    border: solid 4px #403776;
    font-family: MicrosoftYaHei;

    .rule-head {
        position: relative;
        margin-bottom: 36px;
        border-bottom: 4px solid rgba(255, 255, 255, 0.2);
        font-size: 36px;
        color: #ffffff;
        line-height: 93px;
        text-align: center;

        .close {
            position: absolute;
            top: 30px;
            right: 0px;
            width: 38px;
            height: 38px;
            background: url('@/assets/img/lose_return/close.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .rule-content {
        overflow-y: scroll;
        .rule-part {
            display: flex;
            margin-bottom: 44px;
            font-size: 24px;
            color: #ffffff;

            p:first-child {
                margin-right: 5px;
            }
        }
    }
}
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 740px;
    height: 820px;
    padding: 0 30px;
    background: url('@/assets/img/lose_return/bg.png') no-repeat;
    background-size: 100% 100%;
    transform: translate3d(-50%, -50%, 0);

    .lose-title {
        position: absolute;
        top: 0;
        left: 50%;
        width: 590px;
        height: 150px;
        background: url('@/assets/img/lose_return/title.png') no-repeat;
        background-size: 100% 100%;

        transform: translate3d(-50%, -60%, 0);
    }
    .help {
        position: absolute;
        top: 12px;
        right: 30px;
        width: 46px;
        height: 49px;
        background: url('@/assets/img/lose_return/help.png') no-repeat;
        background-size: 100% 100%;
    }

    .close {
        position: absolute;
        bottom: -120px;
        left: 50%;
        margin-left: -32px;
        width: 64px;
        height: 64px;
        background: url('@/assets/img/lose_return/shut.png') no-repeat;
        background-size: 100% 100%;
    }
    .coin-bg0 {
        width: 134px;
        height: 109px;
    }
    .coin-bg1 {
        width: 180px;
        height: 126px;
    }
    .coin-bg2 {
        width: 182px;
        height: 167px;
    }
    .content {
        padding-top: 50px;
        .content-tip {
            padding-top: 35px;
            font-family: MicrosoftYaHei-Bold;
            font-size: 36px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 68px;
            color: #70e2ff;
            text-align: center;
        }

        .content-percent {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-top: 40px;
            height: 64px;

            .percent-tip {
                margin-right: 27px;
                height: 49px;
            }
            .percent-num {
                height: 64px;

                animation: breathe 2s ease infinite alternate;
            }
        }
        .contnt-product {
            margin-top: 20px;

            .content-item {
                display: flex;
                justify-content: center;
                align-items: flex-end;
                position: relative;
                width: 222px;
                height: 275px;
                padding-bottom: 30px;
                background: url('@/assets/img/lose_return/check.png') no-repeat;
                background-size: 100% 100%;

                &.active {
                    background-image: url('@/assets/img/lose_return/checked.png');
                }

                img {
                    max-width: none;
                }
                .content-num {
                    position: absolute;
                    bottom: -5px;
                    display: flex;

                    img {
                        height: 40px;
                    }
                }
                .up-tip {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    top: 20px;
                    width: 194px;
                    height: 80px;
                    padding-bottom: 20px;
                    background: url('@/assets/img/lose_return/buble.png') no-repeat;
                    background-size: 100% 100%;

                    .up-icon {
                        height: 25px;
                    }
                    img {
                        height: 25px;
                    }
                }
            }
            .product-btn {
                margin: 70px auto 0;
                width: 382px;
                height: 113px;
                line-height: 90px;
                background: url('@/assets/img/lose_return/btn.png') no-repeat;
                background-size: 100% 100%;
                font-family: MicrosoftYaHei;
                font-size: 40px;
                font-weight: bold;
                color: #ffffff;
                text-align: center;

                &.disabled {
                    opacity: 0.6;
                }
            }
        }
        .product {
            @apply flex  items-center gap-[20px];
        }
        .deposit-produt {
            margin: 24px auto 0;
            width: 580px;
            padding: 30px 20px;
            background: rgba(0, 0, 0, 0.4);
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-radius: 30px;

            .content-item {
                position: relative;
                display: flex;
                justify-content: center;
                align-items: flex-end;
                width: 160px;
                height: 160px;
                padding-bottom: 8px;

                .content-num {
                    position: absolute;
                    left: 0;
                    bottom: 15px;
                    display: flex;
                    justify-content: center;
                    width: 100%;
                    height: 38px;
                    zoom: 0.7;
                }
                .content-deposite,
                .img-bg,
                .content-deposited {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
                .coin-bg {
                    max-width: none;
                    transform-origin: center bottom;
                    transform: scale(0.8);
                }
                // .coin-bg0 {
                //     width: 104px;
                //     height: 107px;
                // }
                // .coin-bg1 {
                //     width: 180px;
                //     height: 126px;
                // }
                // .coin-bg2 {
                //     width: 182px;
                //     height: 167px;
                // }

                .content-deposited {
                    display: none;
                }
                .product-checked {
                    display: none;
                    position: absolute;
                    bottom: -15px;
                    right: -16px;
                    width: 48px;
                    height: 48px;
                }

                &.active {
                    width: 180px;
                    height: 180px;

                    .coin-bg {
                        transform: scale(0.9);
                        // zoom: 0.9;
                    }
                    .content-deposite {
                        display: none;
                    }
                    .content-deposited {
                        display: block;
                    }
                    .product-checked {
                        display: block;
                    }
                    .content-num {
                        zoom: 0.9;
                    }
                }
            }

            .deposit-tip {
                margin-top: 64px;
                padding: 0 30px;
                font-family: MicrosoftYaHei;
                font-size: 32px;
                color: #ffffff;

                .text-item {
                    margin-bottom: 43px;
                    display: flex;
                    justify-content: space-between;

                    span:last-child {
                        width: 80px;
                    }

                    &:last-child {
                        align-items: flex-end;
                        padding-top: 40px;
                        margin-bottom: 0px;
                        border-top: 4px solid rgba(255, 255, 255, 0.2);

                        & span:last-child {
                            font-size: 40px;
                            color: #ffdb33;
                        }
                    }
                }
            }
            .recived {
                margin: 20px 0 10px;
                text-align: center;
                font-family: MicrosoftYaHei;
                font-size: 30px;
                color: #ffffff;

                .recived-num {
                    font-size: 48px;
                    color: #ffdb32;
                }
                .recived-btn {
                    margin: 30px auto 0;
                    width: 382px;
                    height: 113px;
                    line-height: 90px;
                    font-size: 40px;
                    background: url('@/assets/img/lose_return/btn.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
}
</style>
