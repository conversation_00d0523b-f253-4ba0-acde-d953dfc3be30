<template>
    <van-overlay :show="showed" v-bind="$attrs" z-index="400">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="dialog-title">{{ $t('Seed_user_pop_up_tit') }}</div>
                <div class="dialog-text" v-html="$t('Seed_user_pop_up_tex')"></div>
                <div class="dialog-btn" @click="goFbGroup">{{ $t('Seed_user_pop_up_button') }}</div>
            </div>
            <div v-if="showClose" class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="director-dialog">
import { useBaseStore } from '@/stores'
import { Log } from '@/api/log'
import { uploadDirector } from '@/api/wallet'

defineProps({
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
})
const store = useBaseStore()
const emits = defineEmits(['close'])
const showed = defineModel()

const handleClose = async () => {
    await Log({
        logname: 'event-click',
        event: 'director-close',
        content: {
            uid: store.userInfo.uid,
        },
    })
    showed.value = false
    emits('close')
}

const goFbGroup = async () => {
    Log({
        logname: 'event-click',
        event: 'director-join',
        content: {
            uid: store.userInfo.uid,
        },
    })
    if (store.userInfo.director) {
        await uploadDirector()
    }
    window.open('https://www.facebook.com/groups/3834346583512517/')
}
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 710px;
    max-height: 900px;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    height: 873px;
    padding: 0 40px;
    background: url('@/assets/img/dialog/director_bg.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;
    font-family: MicrosoftYaHei;
    color: #ffffff;

    .dialog-title {
        font-size: 60px;
        font-weight: bold;
        line-height: 200px;
    }
    .dialog-text {
        padding-top: 60px;
        font-size: 36px;
        line-height: 72px;
    }
    .dialog-btn {
        margin-top: 227px;
        width: 610px;
        height: 104px;
        line-height: 104px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb944 40%, #ffad38 100%);
        border-radius: 52px;
        font-size: 44px;
    }
}
.close-icon {
    position: absolute;
    top: -40px;
    right: 10px;
    width: 32px;
    height: 32px;
    background: url('@/assets/img/home-notice/close.png') no-repeat;
    background-size: 100% 100%;
}
</style>
