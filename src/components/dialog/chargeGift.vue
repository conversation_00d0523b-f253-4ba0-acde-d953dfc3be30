<template>
    <div>
        <van-overlay :show="visible" :z-index="300">
            <div class="charge-gift" @click.stop>
                <div class="charge-area">
                    <div class="close" @click="handleClose"></div>
                    <div class="sub-script">{{ $t('First Deposit') }}</div>
                    <div class="title-area">
                        <div class="title">FREE</div>
                        <div class="sub-title">
                            UP TO <span>{{ store.cureencySymbol() }}{{ maxGold }}</span>
                        </div>
                    </div>
                    <div class="product-list">
                        <div class="produtc-title">
                            <div>DEPOSIT</div>
                            <div>REWARD</div>
                        </div>
                        <div class="list">
                            <div
                                v-for="(item, index) in giftInfo"
                                :key="index"
                                :class="['item', { active: index === checkIdx }]"
                                @click="changeProduct(item, index)"
                            >
                                <div class="item-inner">
                                    <div class="left">{{ item.worth?.gold || 0 }}</div>
                                    <div class="center">+</div>
                                    <div class="right">{{ (item.gifts?.bonus || 0) + (item.gifts?.gold || 0) }}</div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="87" height="84" viewBox="0 0 87 84" fill="none">
                                        <path d="M87 44V0L0 84H47C69.0914 84 87 66.0914 87 44Z" fill="#04BA4A" />
                                        <path
                                            d="M74.1304 52.2058C74.6947 51.6264 75.0069 50.8505 74.9999 50.0451C74.9928 49.2397 74.667 48.4692 74.0927 47.8997C73.5183 47.3302 72.7413 47.0071 71.9291 47.0001C71.1168 46.9931 70.3343 47.3027 69.7501 47.8623L54.9022 62.5854L46.2499 54.0059C45.6657 53.4463 44.8832 53.1367 44.0709 53.1437C43.2587 53.1507 42.4817 53.4737 41.9073 54.0433C41.333 54.6128 41.0072 55.3833 41.0001 56.1887C40.9931 56.9941 41.3053 57.77 41.8696 58.3494L52.712 69.1006C53.2929 69.6765 54.0807 70 54.9022 70C55.7236 70 56.5114 69.6765 57.0923 69.1006L74.1304 52.2058Z"
                                            fill="white"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="btn" @click="goRecharge">{{ $t('firstdps_button', { price: depositNum }) }}</div>
                <van-count-down v-if="time" millisecond :time="time" format="HH:mm:ss" />
                <van-checkbox v-model="checked" class="custom-checkbox" @change="handleCheckboxChange">{{
                    $t('No more reminders today')
                }}</van-checkbox>
            </div>
        </van-overlay>
        <ProductChannel v-model="showChannel" />
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import eventBus from '@/utils/bus'
import { useI18n } from 'vue-i18n'
import popupManager from '@/utils/PopupManager'
import ProductChannel from './productChannel.vue'
import dayjs from 'dayjs'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})
const store = useBaseStore()
const i18n = useI18n()

const time = ref(0)

const checked = ref(false)
const visible = ref(false)
const giftInfo = ref([])
const showChannel = ref(false)
const checkIdx = ref(-1)
const orderParams = reactive({
    item: {},
    source: 'firstpayment',
})
provide('orderParams', orderParams)

const handleCheckboxChange = (val) => {
    const today = orderParams.source + dayjs().format('YYYY-MM-DD')
    const uid = store.userInfo?.uid
    let dyaList = store.chargeDate[uid]
    if (!dyaList) {
        dyaList = store.chargeDate[uid] = []
    }

    const isExist = dyaList.includes(today)
    //从dyaList中删除今天的日期
    if (!val && isExist) {
        dyaList = dyaList.filter((item) => item !== today)
        store.chargeDate[uid] = [...dyaList]
    } else {
        store.chargeDate[uid] = [...dyaList, today]
    }

    console.log('dyaList122', dyaList, store.chargeDate[uid])
}

const maxGold = computed(() => {
    return (
        giftInfo.value
            .map((item) => {
                return (item.gifts?.bonus || 0) + (item.gifts?.gold || 0)
            })
            .sort((a, b) => b - a)[0] || 0
    )
})
const changeProduct = (item, index) => {
    if (checkIdx.value === index) {
        return
    }
    checkIdx.value = index
    orderParams.item = item
}
const depositNum = computed(() => {
    if (!giftInfo.value.length) {
        return 0
    }
    return giftInfo.value[checkIdx.value].worth?.gold || 0
})

const getEvent = async (type = 0) => {
    try {
        const res = await store.getSignEvent({ id: 'firstpayment' }, false, false)

        if (res.code === 200 && res.acticity.status === 1) {
            eventBus.emit('chargeGift', true)
            const { products, configs, countdown } = res.acticity.meta
            giftInfo.value = products.map((item) => {
                return {
                    ...item,
                    ...(configs[item.id] || {}),
                }
            })
            const idx = giftInfo.value.findIndex((item) => item.default)
            changeProduct(giftInfo.value[idx], idx !== -1 ? idx : 0)
            time.value = countdown * 60 * 1000
            const today = orderParams.source + dayjs().format('YYYY-MM-DD')
            const uid = store.userInfo?.uid
            let dyaList = store.chargeDate[uid]
            if (!dyaList) {
                dyaList = store.chargeDate[uid] = []
            }
            const isExist = dyaList.includes(today)
            checked.value = isExist === true
            console.log('isExist', isExist, dyaList)

            if (props.type === 1 || type === 1) {
                visible.value = true
            } else if (!isExist) {
                visible.value = true
            } else {
                visible.value = true
                popupManager.checkQueue()
            }
        } else {
            eventBus.emit('chargeGift', false)
            if (props.type === 1) {
                showFailToast(i18n.t('event_end'))
            }

            store.newUser = false
            popupManager.checkQueue()
        }
    } catch (e) {
        console.log(e)
        popupManager.checkQueue()
    }
}
const goRecharge = () => {
    store.newUser = false
    showChannel.value = true
}
const handleClickEvents = () => {
    getEvent(1)
}
const handleClose = () => {
    store.newUser = false
    visible.value = false
    popupManager.checkQueue()
}

onBeforeMount(() => {
    eventBus.on('clickEvents', handleClickEvents)
    eventBus.on('paymentFinish', handleClose)
})
onBeforeUnmount(() => {
    eventBus.off('clickEvents', handleClickEvents)
    eventBus.on('paymentFinish', handleClose)
})
onMounted(() => {
    getEvent()
})
</script>
<style lang="scss" scoped>
.charge-gift {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    min-height: 982px;

    transform: translate3D(-50%, -50%, 0);
    font-family: 'Microsoft YaHei';

    .charge-area {
        position: relative;
        margin: 0 auto;
        width: 706px;
        height: 1006px;
        padding-bottom: 20px;
        background: url('@/assets/img/dialog/charge-gift/chargeGift_bg.png') no-repeat;
        background-size: 100% 100%;

        .sub-script {
            position: absolute;
            top: 23px;
            left: 4px;
            height: 64px;
            line-height: 64px;
            padding: 0 38px;
            border-radius: 60px 0px;
            background: linear-gradient(90deg, #f8eb65 0%, #eea41c 100%);

            box-shadow: 0px 4px 4px 0px rgba(154, 0, 0, 0.25);
            color: #512211;
            font-size: 36px;
            font-weight: 700;
            text-align: center;
        }
        .close {
            position: absolute;
            right: 20px;
            top: -20px;
            width: 35px;
            height: 35px;
            background: url('@/assets/img/close.png') no-repeat;
            background-size: 100% 100%;
        }
        .title-area {
            padding: 140px 0 0 35px;
            font-family: Helvet;
            color: #fff;
            font-weight: 900;

            .title {
                font-size: 155px;
                line-height: 130px;
            }
            .sub-title {
                @apply flex items-center;
                font-size: 55px;
                line-height: 70px;
                span {
                    margin-left: 11px;
                    font-size: 80px;
                }
            }
        }
        .product-list {
            margin: 0 auto;
            width: 668px;
            height: 656px;
            padding: 0 19px;
            border-radius: 50px;
            background: linear-gradient(180deg, rgba(224, 35, 35, 0.6) 0%, rgba(227, 18, 18, 0.3) 100%);

            .produtc-title {
                @apply flex items-center;
                font-size: 34px;
                font-weight: 700;

                div {
                    flex: 1;
                    height: 72px;
                    line-height: 72px;
                    text-align: center;
                    color: #ffed26;
                }
            }
            .list {
                .item {
                    position: relative;
                    margin-bottom: 16px;
                    width: 631px;
                    height: 128px;
                    border-radius: 40px;
                    background: #fff;
                    color: #434445;
                    font-size: 38px;
                    font-weight: 400;
                    .item-inner {
                        @apply flex items-center w-full h-full;
                        padding: 0 32px;

                        svg {
                            display: none;
                            position: absolute;
                            right: 0;
                            bottom: 0;
                            width: 87px;
                            height: 84px;
                        }
                    }

                    .left,
                    .right {
                        flex: 1;
                    }
                    .center {
                        position: relative;
                        top: -10px;
                        margin-right: 14px;
                        width: 78px;
                        font-size: 55px;
                        text-align: center;
                    }
                    .left {
                        @apply flex items-center;
                        // padding-left: 97px;
                        &::before {
                            display: inline-block;
                            content: '';
                            margin-right: 8px;
                            width: 89px;
                            height: 93px;
                            background: url('@/assets/img/dialog/charge-gift/coin1.png') no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                    .right {
                        @apply flex items-center;
                        // padding-left: 144px;
                        color: #f24649;

                        &::before {
                            display: inline-block;
                            content: '';
                            margin-right: 6px;
                            width: 130px;
                            height: 79px;
                            background: url('@/assets/img/dialog/charge-gift/coin2.png') no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                    &.active {
                        .item-inner {
                            position: absolute;
                            // top: -16px;
                            left: 50%;
                            width: 642px;
                            height: 133px;
                            // padding: 0 85px;
                            border-radius: 40px;
                            border: 11px solid #04ba4a;

                            background: var(--Linear, linear-gradient(90deg, #fffc7f 0%, #ffdc6d 100%));
                            box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.3);

                            transform: translateX(-50%);

                            .left {
                                font-size: 44px;
                                color: #434445;
                                // font-weight: 700;
                            }
                            .center {
                                font-size: 58px;
                                font-weight: 700;
                            }
                            .right {
                                font-size: 44px;
                                font-weight: 700;
                            }
                            svg {
                                display: block;
                                right: -6px;
                                bottom: -6px;
                            }
                        }
                    }
                }
            }
        }
    }
    .btn {
        @apply flex items-center justify-center;
        margin: 36px auto 14px;
        width: 609px;
        height: 92px;
        border-radius: 100px;
        background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%);
        font-size: 44px;
        font-weight: 400;
        color: #000;
    }
    :deep(.van-count-down) {
        font-size: 30px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.5);
        text-align: center;
    }

    .custom-checkbox {
        @apply flex justify-center;
        margin-top: 30px;
        :deep(.van-checkbox__icon .van-icon) {
            border-radius: 8px;
        }

        :deep(.van-checkbox__icon--checked .van-icon) {
            border-color: #07f279; /* 选中边框颜色 */
            background-color: #07f279; /* 选中背景色 */
            color: rgb(15, 14, 14); /* 对勾颜色 */
        }

        :deep(.van-checkbox__label) {
            color: rgb(143, 152, 143);
            font-size: 20px;
        }
    }
}
</style>
