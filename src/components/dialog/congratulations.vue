<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="3000">
        <div class="congratulation">
            <div class="title">Congratulations</div>
            <div :class="['content', { active: showActive }]">
                <template v-if="type === 1">
                    <div v-if="msg?.gifts?.gold" class="item cash">
                        <div class="pic">
                            <img src="@/assets/img/dialog/congratulations/cash.png" />
                        </div>
                        <div class="text">
                            Cash <span>{{ store.cureencySymbol() }}{{ (msg?.gifts?.gold || 0) + msg?.worth.gold }}</span>
                        </div>
                    </div>
                    <div v-if="msg?.gifts?.bonus" class="item bonus">
                        <div class="pic">
                            <img src="@/assets/img/dialog/bonus/bouns.png" />
                        </div>
                        <div class="text">
                            Bonus <span>{{ store.cureencySymbol() }}{{ msg?.gifts?.bonus || 0 }}</span>
                        </div>
                    </div>
                </template>
                <template v-if="type === 2">
                    <div v-for="(item, index) in typeData" :key="index + item.id" class="item">
                        <img class="pic" :src="item?.img" />
                        <div class="text">
                            {{ item.name }}
                            <span>{{ store.cureencySymbol() }}{{ item.worth }}</span>
                        </div>
                    </div>
                </template>
            </div>
            <div class="btn" @click="handleConfirm">{{ $t('Confirm') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import popupManager from '@/utils/PopupManager'

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
    msg: {
        type: [Object, Array],
    },
})

const showed = defineModel()
const store = useBaseStore()
const showActive = computed(() => {
    const { bonus, gold } = props.msg?.gifts || {}
    return props.type === 1 && bonus && gold
})
const typeData = computed(() => {
    console.log(props.type === 2, Array.isArray(props.msg), props.msg, 888)
    return props.type === 2 && !Array.isArray(props.msg) ? [props.msg] : props.msg
})
const handleConfirm = () => {
    popupManager.checkQueue()
}

onMounted(() => {
    showed.value = true
})
</script>
<style lang="scss" scoped>
.congratulation {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 718px;
    transform: translate3d(-50%, -50%, 0);
    font-family: 'Microsoft YaHei';
    text-align: center;

    .title {
        line-height: 60px;
        font-size: 72px;
        font-weight: 700;
        color: #fff050;
    }
    .content {
        @apply flex justify-center items-center;
        height: 500px;

        .item {
            @apply flex flex-col items-center;
            position: relative;
            width: 525px;
            min-height: 518px;
            padding-top: 140px;

            &::before {
                position: absolute;
                top: 0;
                left: 50%;
                width: 525px;
                height: 518px;
                content: '';
                margin-left: -262.5px;
                background: url('@/assets/img/dialog/welcome-gift/sun.png') no-repeat;
                background-size: 100% 100%;
                z-index: -1;
                animation: rotate 2.5s linear infinite;
            }
            .text {
                padding-top: 80px;
            }
            .pic {
                @apply flex justify-center items-start;
                width: 200px;
                height: 200px;

                img {
                    width: 100%;
                }
            }

            &.tiket {
                .text {
                    padding-top: 40px;
                }
            }
            .text {
                font-size: 32px;
                font-weight: 400;
                color: #fff;
                span {
                    color: #fff050;
                }
            }
        }
        &.active {
            height: 450px;
            .item {
                width: 310px;
                min-height: 318px;
                padding-top: 70px;
                &::before {
                    top: -40px;
                    width: 378px;
                    height: 372px;
                    margin-left: -189px;
                }
            }
            .pic {
                width: 190px;
                height: 190px;
            }
            .bonus {
                .pic {
                    img {
                        width: 160px;
                        height: 160px;
                    }
                }
            }
            .text {
                padding-top: 30px;
            }
        }
    }
    .btn {
        @apply flex justify-center items-center;
        margin: 0 auto;
        width: 589px;
        height: 96px;
        border-radius: 100px;
        background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%);
        font-size: 44px;
        font-weight: 400;
        color: #000;
    }
}
@keyframes rotate {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}
</style>
