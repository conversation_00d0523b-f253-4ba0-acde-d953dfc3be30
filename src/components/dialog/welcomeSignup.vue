<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="3000">
        <div class="dialog" @click.stop>
            <div class="close" @click="handleClose"></div>
            <div class="title">{{ $t('welcome_gifts_3') }}</div>
            <div class="bg"><img src="@/assets/img/dialog/welcome-signup/kuang.avif" /></div>
            <!-- <div
                class="num"
                v-html="
                    $t('sign_up_gift_2', {
                        current_type: store.cureencySymbol(),
                        number: formatNumber(data.config?.worth || 0, Number.isInteger(data.config?.worth || 0) ? 0 : 2),
                    })
                "
            ></div> -->
            <div class="btn" @click="goLogin">
                {{ $t('Claim') }}
                <span class="btn-num"
                    >{{ store.cureencySymbol() }}{{ formatNumber(data.config?.worth || 0, Number.isInteger(data.config?.worth || 0) ? 0 : 2) }}</span
                >
            </div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import popupManager from '@/utils/PopupManager'
import { formatNumber } from '@/utils/toolsValidate'
import { useBaseStore } from '@/stores'
import { useRouter } from 'vue-router'
import { getRegistGifts } from '@/api/home'
import { Log } from '@/api/log'
import { globalState } from '@/stores/globalstate'

const store = useBaseStore()
const router = useRouter()

const showed = defineModel()

const regeistData = ref({})

const data = computed(() => {
    const { claimed, config, items } = store.gameList
    if (typeof claimed === 'boolean') {
        return {
            claimed,
            config,
            items,
        }
    }
    return regeistData.value
})

watch(
    () => showed.value,
    () => {
        if (showed.value) globalState.signup_pop = true
    }
)

const goLogin = () => {
    showed.value = false
    popupManager.checkQueue()
    router.push('/login')
}
const handleClose = () => {
    showed.value = false
    popupManager.checkQueue()
}
const getGift = () => {
    if (globalState.signup_pop) {
        popupManager.checkQueue()
        return
    }
    const claimed = store.gameList.claimed
    if (typeof claimed === 'boolean') {
        showed.value = true
    } else {
        getRegistGifts().then((res) => {
            if (res.code === 200) {
                showed.value = true
                regeistData.value = res.data
            } else {
                popupManager.checkQueue()
            }
        })
    }
}

onMounted(() => {
    if (!store.token) {
        getGift()
        Log({
            event: 'unRegister_welcome',
        })
    } else {
        popupManager.checkQueue()
    }
})
</script>
<style lang="scss" scoped>
.dialog {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 750px;
    // padding: 0 37px;
    transform: translate3d(-50%, -50%, 0);
    font-family: Helve;

    .close {
        position: absolute;
        width: 54px;
        height: 54px;
        top: 100px;
        right: 76px;
        background: url('@/assets/img/close.png') no-repeat center center;
        background-size: 27px 27px;
    }

    .bg {
        margin: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
    }
    .title {
        position: absolute;
        width: 100%;
        height: 100px;
        top: 20px;
        right: 10px;
        font-family: MicrosoftYaHei;
        font-size: 62px;
        display: flex;
        text-align: center;
        justify-content: center;
        // letter-spacing: -1px;
        color: #fcef6d;
        font-weight: bold;
    }
    .num {
        margin-top: -20px;
        color: #fff050;
        font-size: 130px;
        font-family: Helvet;

        :deep(span) {
            margin-right: 15px;
            font-family: Helvet;
        }
    }
    .btn {
        @apply flex justify-center items-center;
        // margin: 150px auto;
        margin: 20px auto;
        width: 432px;
        height: 88px;
        background-image: linear-gradient(90deg, #fffc7f 0%, #ffdc6d 100%), linear-gradient(#8b0500, #8b0500);
        background-blend-mode: normal, normal;
        border-radius: 14px;
        font-family: MicrosoftYaHei;
        font-size: 40px;
        font-weight: bold;
        letter-spacing: -1px;
        color: #000000;
        .btn-num {
            padding-left: 10px;
            font-size: 40px;
            color: red;
        }
    }
}
</style>
