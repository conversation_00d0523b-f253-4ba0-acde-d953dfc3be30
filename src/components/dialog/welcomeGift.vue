<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="299">
        <div class="dialog" @click.stop>
            <div class="title">{{ $t('welcome_gifts_1') }}</div>
            <div class="boom"><img :src="gameGame.img" /></div>
            <div
                class="num"
                v-html="
                    $t('welcome_gifts_2', {
                        game_type: $t(gameGame.name),
                        current_type: store.cureencySymbol(),
                        number: formatNumber(data.config?.worth || 0, Number.isInteger(data.config?.worth || 0) ? 0 : 2),
                    })
                "
            ></div>
            <div
                class="tip"
                v-html="
                    $t('welcome_info', {
                        number: formatNumber(data.config?.win || 0),
                    })
                "
            ></div>
            <div class="btn" @click="confirm">{{ $t('Confirm') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import popupManager from '@/utils/PopupManager'
import { formatNumber } from '@/utils/toolsValidate'
import { useBaseStore } from '@/stores'
import { getRegistGifts, claimRegistGifts } from '@/api/home'
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/bus'

const store = useBaseStore()
const i18n = useI18n()

const showed = defineModel()
const regeistData = ref({})

const giftconfigs = ref([])

const data = computed(() => {
    // const { claimed, config, items } = store.gameList
    // if (typeof claimed === 'boolean') {
    //     return {
    //         claimed,
    //         config,
    //         items,
    //     }
    // }
    return regeistData.value
})
const gameGame = computed(() => {
    const id = data.value?.config?.items[0].id
    if (!id) {
        return {}
    }
    console.log('data.value.items[id]--------', data.value.items[id])

    return data.value.items[id]
})

const confirm = () => {
    showed.value = false
    // eventBus.emit('bloomAnimate', 1)
    eventBus.emit('chargeGiftClose')
    eventBus.emit('freeGametimes')
}

const claimGift = () => {
    claimRegistGifts().then((res) => {
        if (res.code === 200) {
            store.gameList.claimed = true
            eventBus.emit('bloomAnimate', { type: 1, data: gameGame.value?.img })
            eventBus.emit('freeGametimes')
        } else {
            showed.value = false
            popupManager.checkQueue()
        }
    })
}

const getGift = () => {
    const claimed = store.gameList.claimed
    if (claimed) {
        popupManager.checkQueue()
        return
    }
    // if (typeof claimed === 'boolean') {
    //     showed.value = true
    // } else {
    getRegistGifts()
        .then((res) => {
            console.log('getRegistGifts -- res---', res)

            if (res.code === 200 && !res.data.claimed) {
                regeistData.value = res.data
                store.gameList.config = res.data.config
                store.gameList.items = res.data.items
                showed.value = true
                claimGift()
            } else {
                popupManager.checkQueue()
            }
        })
        .catch(() => {
            popupManager.checkQueue()
        })
    // }
}

onMounted(() => {
    // if (!store.newUser) {
    //     return popupManager.checkQueue()
    // }
    if (store.token) {
        getGift()
    } else {
        popupManager.checkQueue()
    }
})
</script>
<style lang="scss" scoped>
.dialog {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 718px;
    transform: translate3d(-50%, -50%, 0);
    font-family: MicrosoftYaHei;
    text-align: center;

    .title {
        font-size: 72px;
        font-weight: bold;
        line-height: 68px;
        text-align: center;
        color: #fff050;
    }
    .boom {
        position: relative;
        margin: 65px auto 0;
        width: 355px;
        height: 310px;
        img {
            width: 100%;
            height: 100%;
            animation: scale 1s ease infinite alternate;
        }

        &::before {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 525px;
            height: 518px;
            content: '';
            margin-left: -262.5px;
            margin-top: -235px;
            background: url('@/assets/img/dialog/welcome-gift/sun.png') no-repeat;
            background-size: 100% 100%;
            z-index: -1;
            animation: rotate 2.5s linear infinite;
        }
    }
    .num {
        border-radius: 25px;
        margin-top: 30px;
        font-size: 32px;
        font-weight: bold;
        color: #fff;
        text-align: center;

        :deep(span) {
            color: #ffeb0c;
        }
    }
    .tip {
        padding-left: 40px;
        margin-top: 34px;
        font-size: 24px;
        font-weight: bold;
        line-height: 44px;
        color: #668292;
        text-align: left;

        :deep(span) {
            color: #ffeb0c;
        }
    }
    .btn {
        @apply flex justify-center items-center;
        margin: 48px auto 0;
        width: 565px;
        height: 92px;
        background-image: linear-gradient(90deg, #26f087 0%, #62ec7c 40%, #9de871 100%);
        border-radius: 38px;
        color: #000000;
        font-size: 40px;
        font-weight: bold;
    }
}

@keyframes rotate {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}
@keyframes scale {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.05);
    }
}
</style>
