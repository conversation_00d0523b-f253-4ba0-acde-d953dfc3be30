<template>
    <Component v-if="currentPopup" :key="Date.now()" :is="currentPopup.component" v-bind="currentPopup.params" @close="closePopup" />
</template>

<script setup lang="ts">
import popupManager from '@/utils/PopupManager'
// import BonusDialog from '@/components/dialog/bonusDialog.vue'
import ChargeGift from '@/components/dialog/chargeGift.vue'
import { useBaseStore } from '@/stores'
import eventBus, { EVENT_KEY } from '@/utils/bus'
import BindPhone from '@/components/dialog/BindDialog.vue'
import BindInviter from '@/components/dialog/BindInviter.vue'
import GetBonus from '@/components/dialog/getBounus.vue'
import WelcomeGift from '@/components/dialog/welcomeGift.vue'
import WelcomeSignup from '@/components/dialog/welcomeSignup.vue'
import Benefit from '@/components/dialog/benefit.vue'
import Congratulations from '@/components/dialog/congratulations.vue'
import Sign from '@/components/dialog/sign.vue'
import { checkOrder } from '@/api/wallet/index'
import loseReturn from './loseReturn.vue'
import LuckySpin from '@/views/luckyspin/index.vue'
import Announcement from '@/components/dialog/announcement.vue'
import Invitepop from '../invitewheel/invitepop.vue'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})

const store = useBaseStore()

const eventMap = {
    paymentpresent: ChargeGift,
    '/bindPhone': BindPhone,
    '/bindInviter': BindInviter,
    benefit: Benefit,
    congratulations: Congratulations,
    firstpayment: ChargeGift,
    onlinesign: Sign,
    ladderloserebate: loseReturn,
    adduppayment: LuckySpin,
    announcement: Announcement,
    invitepop: Invitepop,
}

const notLoginList = [WelcomeSignup]
const loginList = [WelcomeGift, GetBonus, ChargeGift, Sign, Announcement]

const addQuee = () => {
    popupManager.clearQueue()
    let componentList = []
    if (!store.token) {
        componentList = [...notLoginList]
    } else {
        componentList = [...loginList]
    }
    componentList.forEach((component) => {
        if (component) {
            popupManager.addPopup(markRaw(component))
        }
    })
    popupManager.startCheck()
}
watch(
    () => store.wallet?.currency,
    (val, oldVal) => {
        if (val === oldVal || props.type !== 0) {
            return
        }
        addQuee()
    }
)

const currentPopup = popupManager.currentPopup

function closePopup() {
    // popupManager.closeCurrentPopup()
}
const handleClickEvents = (item) => {
    const com = eventMap[item.param]
    if (com) {
        popupManager.insertPopup(markRaw(com), { type: item.type || 1, msg: item.msg })
        // if (!popupManager.isChecking) {
        popupManager.checkQueue()
        // }
    }
}
const handleChargeFirst = (order) => {
    handleClickEvents({
        param: 'congratulations',
        msg: order.item,
        type: order.type,
    })
    if (!Array.isArray(order.item) && order.item.usefor === 'firstpayment') {
        eventBus.emit('chargeGift', false)
    }
}
const checkChargeFirst = () => {
    if (store.depositOrder) {
        checkOrder({
            uid: store.userInfo.uid,
            orderId: store.depositOrder,
        }).then((res) => {
            if (res.code === 200) {
                handleChargeFirst(res.info)
            }
        })
        store.depositOrder = ''
    }
}
const handleVisible = () => {
    if (document.visibilityState === 'visible') {
        // 页面变为可见状态时执行的操作
        checkChargeFirst()
    }
}

onActivated(() => {
    console.log('CheckDialog   onActivated')

    if (popupManager.queue.length) {
        popupManager.clearQueue()
    }
    if (props.type !== 0) {
        return
    }
    addQuee()
})
onBeforeMount(() => {
    console.log('CheckDialog   onBeforeMount', store.depositOrder)
    eventBus.on('activity', handleClickEvents)
    eventBus.on(EVENT_KEY.PAYMENT, handleChargeFirst)
    addQuee()
    window.addEventListener('visibilitychange', handleVisible)
    if (store.depositOrder) {
        checkChargeFirst()
    }
})
onBeforeUnmount(() => {
    eventBus.off('activity', handleClickEvents)
    eventBus.off(EVENT_KEY.PAYMENT, handleChargeFirst)
    window.removeEventListener('visibilitychange', handleVisible)
})
</script>

<style scoped></style>
