<template>
    <van-overlay :show="showed" v-bind="$attrs" @click="handleClose" :z-index="300">
        <div class="dialog" @click.stop>
            <div class="dialog-content">
                <div class="cash-num">x{{ info.awardgold }}</div>
                <div class="cash_tip">
                    <span v-if="info.status === 3">{{ $t('relief_unbind_phone') }}</span>
                    <span v-else-if="info.status === 1 && info.count <= info.bshowcount">{{ $t('relief_notimes_1', { count: info.count }) }}</span>
                    <span v-else-if="info.status === 2">{{ $t('relief_notimes_2') }}</span>
                </div>
                <div v-if="info.status === 1" class="cash-btn" @click="receiveCash">{{ $t('Claim') }}</div>
                <div v-else-if="info.status === 2" class="cash-btn" @click="handleRecharge">{{ $t('Recharge') }}</div>
                <div v-else-if="info.status === 3" class="cash-btn" @click="handleBind">{{ $t('Bind') }}</div>
            </div>

            <div v-if="showClose" class="close-icon" @click="handleClose"></div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="cash-dialog">
import { useRoute, useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { Acticity } from '@/stores/types'
import { useI18n } from 'vue-i18n'
// import dayjs from 'dayjs'
// 救济金
defineProps({
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
})
const showed = ref(false)
const info = ref<Acticity>({})
const route = useRoute()
const store = useBaseStore()
const i18n = useI18n()
const router = useRouter()

const emits = defineEmits(['close', 'update:modelValue'])

const handleClose = () => {
    showed.value = false
    emits('close')
}
const receiveCash = async () => {
    try {
        const res = await store.ReceiveRewards({ id: 'reliefaward' })
        if (res.code === 200) {
            showed.value = false
            showToast(i18n.t('Successful_collection'))
        }
    } catch (e) {
        console.log(e)
    }
}
const handleRecharge = () => {
    showed.value = false
    router.push({
        path: '/wallets',
        query: {
            back: 1,
        },
    })
}
const handleBind = () => {
    showed.value = false
    router.push('/bindPhone')
}
watch(
    () => route.path,
    (to, from) => {
        if (from === '/iframe' || from === '/rechargeRebate') {
            checkRelief()
        }
    },
    {
        immediate: true,
    }
)
watch(show, () => {
    show.value && getChannel()
})
async function checkRelief() {
    const res = await store.getSignEvent({ id: 'reliefaward' }, false, false)
    const status = res?.acticity?.status
    if (res?.code === 200 && status !== 0) {
        // && (!store.loginTime || (store.loginTime && dayjs().isAfter(dayjs(store.loginTime), 'day')))
        if (status === 4) {
            store.loginTime = Date.now()
            // router.push({
            //     path: '/rechargeRebate',
            //     query: {
            //         id: 'paymentrebate',
            //     },
            // })
        } else {
            showed.value = true
            info.value = res.acticity
        }
    }
}
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 742px;
    max-height: 900px;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-content {
    height: 873px;
    padding-top: 380px;
    background: url('@/assets/img/cash_dialog/cash_bg.png') no-repeat;
    background-size: 100% 100%;

    .cash-num {
        @apply flex justify-center items-center gap-[46px];
        margin-bottom: 90px;
        font-family: MicrosoftYaHeiHeavy;
        font-size: 104px;
        color: #ffff00;

        &::before {
            display: inline-block;
            content: '';
            width: 109px;
            height: 129px;

            background: url('@/assets/img/cash_dialog/cash_icon.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .tip {
        width: 610px;
        margin: 0 auto 10px;
        color: #fff;
        font-size: 26px;
        text-align: center;
    }
    .cash-btn {
        width: 610px;
        height: 104px;
        line-height: 104px;
        margin: 0 auto 0;
        background-image: linear-gradient(90deg, #fd9559 0%, #e22606 100%);
        border-radius: 52px;
        font-size: 48px;
        font-weight: Bold;
        color: #fff;
        text-align: center;
    }
}
.close-icon {
    position: absolute;
    top: -40px;
    right: 30px;
    width: 32px;
    height: 32px;
    background: url('@/assets/img/home-notice/close.png') no-repeat;
    background-size: 100% 100%;
}
.cash_tip {
    margin-bottom: 5px;
    font-size: 25px;
    color: #f9ff00;
    text-align: center;
}
</style>
