<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="3000">
        <div class="dialog" @click.stop>
            <div class="close" @click="handleClose"></div>
            <!-- 图片容器，使用flex布局实现居中 -->
            <div class="image-container">
                <div class="bg" @click="goLink">
                    <img :src="imgsrc" class="centered-image" />
                </div>
            </div>
            <van-checkbox v-if="!!noticeData && noticeData.hidetype" v-model="checked" class="custom-checkbox" @change="handleCheckboxChange">{{
                noticeData.hidetype === 2 ? $t('Never remind again') : $t('No more reminders today')
            }}</van-checkbox>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import popupManager from '@/utils/PopupManager'
import { useBaseStore } from '@/stores'
// import { useRouter } from 'vue-router'
import { Announcement } from '@/api/home/<USER>'
import dayjs from 'dayjs'
import AnnouncementCom from '@/components/dialog/announcement.vue'
import { getH5Announcement } from '@/api/home'

const store = useBaseStore()
// const router = useRouter()

const showed = defineModel()

let noticesData = []
const noticeData = ref<Announcement>({} as Announcement)

const imgsrc = computed(() => {
    return noticeData.value.picture
})

const checked = ref(false)

//检测是否有可弹公告
const checkNotice = (): boolean => {
    const now = dayjs()
    const today = dayjs().format('YYYY-MM-DD')
    const uid = store.userInfo?.uid

    for (let i = 0; i < noticesData.length; i++) {
        const data = noticesData[i]
        const startTime = dayjs(data.begintime)
        const endTime = dayjs(data.endtime)
        if (now.isBefore(startTime) || now.isAfter(endTime)) continue
        let showtype = data.showtype //1: 每天弹一次 2: 每次登录弹一次 3: 每个周期弹一次
        let showAnnouncement = store.showAnnouncement[uid] || []
        const title = data.title
        let showkey = ''
        showkey = 'notice_' + title + today
        if (showAnnouncement.includes(showkey)) continue
        showkey = 'notice_' + title + 'ever'
        if (showAnnouncement.includes(showkey)) continue
        switch (showtype) {
            case 1: //每天弹一次
                showkey = 'notice_' + title + today
                break
            case 2: //每次登录弹一次
                showkey = 'notice_' + title + 'login'
                if (store['show' + title]) continue
                store['show' + title] = true
                break
            case 3: //每个周期弹一次
                showkey = 'notice_' + title + startTime.format('YYYY-MM-DD')
                if (showAnnouncement.includes(showkey)) continue
                break
            default:
                break
        }

        store.showAnnouncement[uid] = [...showAnnouncement, showkey]
        noticeData.value = noticesData[i]
        return true
    }

    return false
}

const handleCheckboxChange = (val: boolean) => {
    const title = noticeData.value.title
    const today = dayjs().format('YYYY-MM-DD')
    const hidetype = noticeData.value.hidetype //1: 当日不弹 2:终身不弹
    const showkey = 'notice_' + title + (hidetype === 1 ? today : 'ever')
    const uid = store.userInfo?.uid
    let dyaList = store.showAnnouncement[uid]
    if (!dyaList) {
        dyaList = store.showAnnouncement[uid] = []
    }

    const isExist = dyaList.includes(showkey)
    //从dyaList中删除今天的日期
    if (!val && isExist) {
        dyaList = dyaList.filter((item) => item !== showkey)
        store.showAnnouncement[uid] = [...dyaList]
    } else {
        store.showAnnouncement[uid] = [...dyaList, showkey]
    }
}

const goLink = () => {
    const link = noticeData.value.link
    if (link) {
        if (link.startsWith('http')) {
            window.open(link, '_blank')
        }
    }
    // popupManager.checkQueue()
}
const handleClose = () => {
    showed.value = false
    popupManager.addPopup(markRaw(AnnouncementCom))
    popupManager.checkQueue()
}
const getAnnouncement = () => {
    if (store.announcementData && store.announcementData.length > 0) {
        noticesData = store.announcementData
        if (checkNotice()) {
            showed.value = true
        } else {
            popupManager.checkQueue()
        }
    } else {
        //如果没有数据，则请求接口
        getH5Announcement().then((res: any) => {
            if (res.code === 200) {
                store.setAnnouncementData(res.data)
                noticesData = res.data
                if (checkNotice()) {
                    showed.value = true
                } else {
                    popupManager.checkQueue()
                }
            } else {
                popupManager.checkQueue()
            }
        })
    }
}

onMounted(() => {
    if (store.token) {
        getAnnouncement()
    } else {
        popupManager.checkQueue()
    }
})
</script>
<style lang="scss" scoped>
.dialog {
    position: relative;
    display: flex;
    left: 50%;
    top: 50%;
    width: 750px;
    max-height: 850px;
    transform: translate3d(-50%, -50%, 0);
    font-family: Helve;
    justify-content: center;
    flex-direction: column;

    .close {
        position: absolute;
        width: 64px;
        height: 66px;
        right: 56px;
        top: 0px;
        background: url('@/assets/img/btn_closed.png') no-repeat center center;
        background-size: 44px 46px;
    }

    .image-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-grow: 1; /* 占据剩余空间 */
    }

    .centered-image {
        max-width: 100%;
        max-height: 780px; /* 限制图片最大高度 */
        object-fit: contain; /* 保持图片比例 */
    }
    .title {
        margin-bottom: 35px;
        font-family: MicrosoftYaHei;
        font-size: 32px;
        line-height: 38px;
        line-height: 34px;
        letter-spacing: -1px;
        color: #fff050;
        font-weight: bold;
    }
    .num {
        margin-top: -20px;
        color: #fff050;
        font-size: 130px;
        font-family: Helvet;

        :deep(span) {
            margin-right: 15px;
            font-family: Helvet;
        }
    }
    .btn {
        @apply flex justify-center items-center;
        margin: 0 auto;
        width: 332px;
        height: 88px;
        background-image: linear-gradient(90deg, #fffc7f 0%, #ffdc6d 100%), linear-gradient(#8b0500, #8b0500);
        background-blend-mode: normal, normal;
        border-radius: 44px;
        font-family: MicrosoftYaHei;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -1px;
        color: #000000;
    }
}

.custom-checkbox {
    @apply flex justify-center;
    margin-top: 30px;
    :deep(.van-checkbox__icon .van-icon) {
        border-radius: 8px;
    }

    :deep(.van-checkbox__icon--checked .van-icon) {
        border-color: #07f279; /* 选中边框颜色 */
        background-color: #07f279; /* 选中背景色 */
        color: rgb(15, 14, 14); /* 对勾颜色 */
    }

    :deep(.van-checkbox__label) {
        color: rgb(143, 152, 143);
        font-size: 24px;
    }
}
</style>
