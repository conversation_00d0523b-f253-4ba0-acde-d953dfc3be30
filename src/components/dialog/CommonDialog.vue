<template>
    <van-overlay :show="showed" v-bind="$attrs" @click="handleClose">
        <div class="dialog" @click.stop>
            <div class="dialog-title">
                <slot></slot>
            </div>
            <div class="dialog-btn">
                <div class="dialog-cancel" @click="cancel">{{ $t(cancleText) }}</div>
                <div class="dialog-confirm" @click="confirm">{{ $t(confirmText) }}</div>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="commonDialog">
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    cancleText: {
        type: String,
        default: () => 'cancel',
    },
    confirmText: {
        type: String,
        default: () => 'confirm',
    },
    closeOnClickOverlay: {
        type: Boolean,
        default: () => true,
    },
    // title: {
    //     type: String,
    //     default: () => '',
    // },
})

const emits = defineEmits(['update:modelValue', 'cancel', 'confirm'])
const showed = defineModel()

const handleClose = () => {
    if (!props.closeOnClickOverlay) {
        return
    }
    showed.value = false
}
const cancel = () => {
    handleClose()
    emits('cancel')
}
const confirm = () => {
    handleClose()
    emits('confirm')
}
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 684px;
    min-height: 400px;
    padding: 30px 45px 20px;
    background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
    background-blend-mode: normal, normal;
    border-radius: 28px;
    border: solid 2px #544f4c;
    // overflow: hidden;

    transform: translate3d(-50%, -50%, 0);
    background: #3a3533;
    color: #fff;

    .dialog-title {
        min-height: 230px;
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .dialog-btn {
        @apply flex justify-center gap-[75px];
        font-size: 30px;

        .dialog-cancel,
        .dialog-confirm {
            min-width: 190px;
            height: 64px;
            padding: 0 10px;
            line-height: 64px;
            text-align: center;

            border-radius: 31px;
        }
        .dialog-cancel {
            background-image: linear-gradient(150deg, #136fc6 0%, #2460c5 45%, #3550c3 100%), linear-gradient(#e59e20, #e59e20);
            background-blend-mode: normal, normal;
        }
        .dialog-confirm {
            background-image: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
            background-blend-mode: normal, normal;
        }
    }
}
</style>
