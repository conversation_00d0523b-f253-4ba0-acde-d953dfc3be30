<template>
    <van-share-sheet
        @touchmove.stop
        v-model:show="model"
        :options="showOptions"
        :title="$t('Share_to_friends_now')"
        @select="hanldeShare"
        :cancel-text="$t('Cancel')"
    />
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'

const model = defineModel<boolean>()
const emits = defineEmits(['shared'])
import { useClipboard1 } from '@/hooks/useCopy'
import { useI18n } from 'vue-i18n'
import FBIcon from '/images/facebook.png'
import Telegram from '@/assets/img/share/telegram.png'
import Viber from '@/assets/img/share/viber.png'
// import { H5Login } from '@/utils/H5Login'
import { encode } from 'js-base64'
import { getDeviceInfo } from '@/utils'

const props = defineProps({
    social: {
        type: Array,
        default: () => ['facebook', 'link', 'code'],
    },
    config: {
        type: Object,
        default: () => ({}),
    },
})

const store = useBaseStore()
const { toClipboard } = useClipboard1()

const i18n = useI18n()
const currencyPageMap = {
    PHP: 'app_ph',
    USD: 'app_us',
    TRY: 'app_tr',
}

// + `/#${route.path}?id=${props.item.id}&pageNum=${props.pageNum}`
const options = computed(() => {
    if (store.isTg) {
        return [
            {
                name: 'telegram',
                type: 'telegram',
                icon: 'https://betfugu.com/static/img/telegram.png',
            },
        ]
    }
    return [
        {
            name: 'facebook',
            type: 'facebook',
            icon: FBIcon,
        },
        {
            name: 'telegram',
            type: 'telegram',
            icon: Telegram,
        },
        {
            name: 'viber',
            type: 'viber',
            icon: Viber,
        },
        {
            name: i18n.t('Copy_link'),
            icon: 'link',
            type: 'link',
        },
        {
            name: i18n.t('Copy_invitation_code'),
            icon: 'weapp-qrcode',
            type: 'code',
        },
    ]
})
const checkSocial = computed(() => (store.isTg ? ['telegram'] : props.social))
const showOptions = computed(() => options.value.filter((item) => checkSocial.value.includes(item.type)))

// const hanldeShare = async (option) => {
//     const shareOption = props.config
//     if (!shareOption.url) {
//         const url = `${import.meta.env.MODE !== 'production' ? 'https://h5vue.wecardgame.com' : 'https://www.playtok.ph'}/#/?userId=${
//             store.userInfo.uid
//         }`
//         shareOption.url = url
//     }
const hanldeShare = async (option) => {
    const isIos = getDeviceInfo().os === 'iOS'
    const host = store.isTg
        ? import.meta.env.VITE_SHARE_TG
        : isIos
        ? import.meta.env.VITE_SHARE_HOST
        : `https://betfugu.com/${currencyPageMap[store.wallet.currency] || 'app_ph'}.html`
    const shareOption = props.config
    if (!shareOption.url && !store.isTg) {
        let url = `${host}${isIos ? '#/' : ''}?userId=${store.userInfo.uid}&currency=${store.wallet?.currency}&partner=${store.userInfo.partner}`
        shareOption.url = url
        console.log('url', url)
    }
    if (shareOption.from) {
        shareOption.url += `&sfrom=${shareOption.from}`
    }
    if (option.type === 'facebook') {
        //     H5Login.FBShare({
        //         href: shareOption.url,
        //     })
        window.open(`https://m.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareOption.url)}`)
    } else if (option.type === 'telegram') {
        if (!store.isTg || shareOption.url) {
            shareOption.url = `https://t.me/share/url?url=${shareOption.url}}`
        } else {
            const shareUrl = `${host}?` + `start=${encode('invite_' + store.tgInitData.id + '_tg')}==`
            shareOption.url = `https://t.me/share/url?url=${shareUrl}&text=${i18n.t('USDT_Plac_for_Share_Link_Telegram')}`
        }
        window.open(shareOption.url)
    } else if (option.type === 'viber') {
        window.open(`viber://forward?text=${encodeURIComponent(shareOption.url) || shareOption.text || ''} `)
    } else if (option.type === 'code') {
        toClipboard(`${store.userInfo.uid}`)
        showToast({
            message: 'copy code successful',
            zIndex: 4000,
        })
    } else {
        toClipboard(shareOption.url)
        showToast({
            message: 'copy link successful',
            zIndex: 4000,
        })
    }
    model.value = false
    emits('shared')
}
</script>
<style lang="scss" scoped></style>
