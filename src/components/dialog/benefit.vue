<template>
    <div>
        <van-overlay :show="show" @click="handleClose" :z-index="2000">
            <div :class="['dialog', { active: !info.products?.length }]" @click.stop>
                <div class="close" @click="handleClose"></div>
                <div class="benet-top">
                    <div class="benet-num">{{ store.cureencySymbol() }}{{ info?.config?.gold }}</div>
                    <div class="benet-left">
                        {{ $t('Fund_claim_time') }}<span>{{ info.left }}</span>
                    </div>

                    <div v-if="[1, 3].includes(info.status)" class="benet-btn" @click="receiveCash">{{ $t('Claim') }}</div>
                    <div v-else class="benet-btn cant-recieve">{{ $t('Claim') }}</div>
                    <p v-if="[1, 3].includes(info.status) && info?.config?.flowmult" class="tip">
                        {{
                            $t('claim_cash_times_info', {
                                number: info?.config?.flowmult,
                            })
                        }}
                    </p>
                    <p v-if="info.status === 2" class="tip">{{ $t('Fund_no_claim_times') }}</p>
                    <!-- <p v-if="info.status === 3" class="tip">{{ $t('Fund_no_phone') }}</p> -->
                    <p v-if="info.status === 5" class="tip">{{ $t('Fund_once_per_phone') }}</p>
                    <p v-if="info.status === 6" class="tip">{{ $t('Fund_claim_Req') }}</p>
                    <p v-if="info.status === 7" class="tip">{{ $t('Fund_claim_aft_game') }}</p>
                    <p v-if="info.status === 8" class="tip">{{ $t('Fund_still_ingame') }}</p>
                </div>
                <div v-if="info.products?.length" class="benet-product">
                    <div class="product-top">
                        <div class="product-title">{{ $t('Deposit_Amount') }}</div>
                        <div class="product-cutdown"><van-count-down :time="time" /></div>
                    </div>
                    <div class="product-list grid grid-cols-2">
                        <div v-for="(item, index) in info.products" :key="index" class="product-item">
                            <div class="item-img"><img :src="`//betfugu.com/static/img/playtok/benefit/coin${index + 1}.png`" /></div>
                            <div class="item-num">
                                <span>{{ store.cureencySymbol() }}</span
                                >{{ item.config.cny }}
                            </div>
                            <div class="item-tip">
                                <span class="tip-not">+{{ Math.floor(item.config.cny / info.config.recharge) }}</span>
                                <span class="tip-num">+{{ item.times }}</span>
                                <span class="tip-text">{{ $t('claim_times') }}</span>
                            </div>
                            <div class="product-btn" @click="handleDeposit(item)">{{ $t('Recharge') }}</div>
                            <div v-if="item.giftpercentage" class="product-percent">
                                <span class="percent-num">{{ item.giftpercentage }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </van-overlay>
        <ProductChannel v-model="showChannel" @recharged="recharged" />
    </div>
</template>
<script setup lang="ts">
import ProductChannel from './productChannel.vue'
import dayjs from 'dayjs'
import { useBaseStore } from '@/stores'
// import { useRoute } from 'vue-router'
// import eventBus from '@/utils/bus'
import { useI18n } from 'vue-i18n'
import { Log } from '@/api/log'
import popupManager from '@/utils/PopupManager'
import eventBus from '@/utils/bus'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})

const emits = defineEmits(['showBindDialog'])

const store = useBaseStore()
// const route = useRoute()
const i18n = useI18n()

const show = ref(false)
const showChannel = ref(false)

const info = ref({})
const time = ref(getLiftTime())

const orderParams = reactive({
    item: {},
    source: 'reliefaward',
})
provide('orderParams', orderParams)

// watch(
//     () => route.path,
//     (to, from) => {
//         if (from === '/iframe') {
//             checkRelief()
//         }
//     },
//     {
//         immediate: true,
//     }
// )
// watch(
//     () => store.userInfo,
//     () => {
//         if (route.path === '/') {
//             checkRelief()
//         }
//     },
//     {
//         immediate: true,
//     }
// )

const handleClose = () => {
    show.value = false
    popupManager.checkQueue()
}
function getLiftTime() {
    // 获取当前时间的毫秒数
    const now = dayjs()
    // 获取当天24点的时间戳
    const endOfDay = dayjs().endOf('day').valueOf()
    // 计算当前时间到当天24点的剩余毫秒数
    return endOfDay - now.valueOf()
}

const handleDeposit = (item) => {
    Log({
        event: 'reliefaward',
        content: {
            number: item.config.cny,
        },
    })
    orderParams.item = item.config
    showChannel.value = true
}
const recharged = () => {
    show.value = false
}
async function checkRelief() {
    try {
        const res = await store.getSignEvent({ id: 'reliefaward' }, false, false)
        const status = res?.acticity?.status
        if (res?.code === 200) {
            info.value = res.acticity
            show.value = true
            if ([1, 2, 3].includes(status)) {
                show.value = true
            } else if (status === 0) {
                show.value && showToast(i18n.t('Fund_no_event'))
                popupManager.checkQueue()
            } else if (props.type === 1) {
                show.value = true
            } else {
                popupManager.checkQueue()
            }
        } else {
            popupManager.checkQueue()
        }
    } catch (e) {
        popupManager.checkQueue()
    }
}
const receiveCash = async () => {
    if (info.value.status === 3) {
        showToast(i18n.t('Fund_no_phone'))

        setTimeout(() => {
            eventBus.emit('activity', {
                param: '/bindPhone',
            })
            popupManager.checkQueue()
            show.value = false
        }, 1000)
        return
    }
    try {
        const res = await store.ReceiveRewards({ id: 'reliefaward' })
        if (res.code === 200) {
            show.value = false
            showToast(i18n.t('Successful_collection'))
        }
    } catch (e) {
        console.log(e)
    }
}
onMounted(() => {
    checkRelief()
})
</script>
<style lang="scss" scoped>
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 738px;
    min-height: 800px;
    height: 1080px;
    padding: 0 34px;
    background: url('@/assets/img/benet_dialog/benet_bg.png') no-repeat;
    background-size: 100% 1080px;
    transform: translate3d(-50%, -50%, 0);
    overflow: hidden;
    &.active {
        border-radius: 60px;
        height: auto;
    }

    .close {
        position: absolute;
        top: 10px;
        right: 20px;
        width: 35px;
        height: 36px;
        background: url('@/assets/img/dialog/welcome_shut.png') no-repeat;
        background-size: 100% 100%;
    }
    .benet-top {
        padding-top: 220px;
        height: 584px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .benet-num {
            font-family: MicrosoftYaHei-Bold;
            font-size: 104px;
            font-weight: bold;
            box-shadow: inset 0px 2px 0px 0px #ffffff;
            color: #d11a9b;
        }
        .benet-left {
            position: relative;
            top: -20px;
            font-family: MicrosoftYaHei;
            font-size: 24px;
            line-height: 43px;
            color: #787878;
            opacity: 0.77;

            span {
                color: #d11a9b;
            }
        }
        .benet-btn {
            width: 480px;
            height: 88px;
            line-height: 88px;
            background-image: linear-gradient(90deg, #ffc44f 0%, #ffa72a 100%), linear-gradient(#000000, #000000);
            background-blend-mode: normal, normal;
            box-shadow: inset 0px 1px 0px 0px rgba(255, 252, 0, 0.44);
            border-radius: 44px;

            font-family: MicrosoftYaHei-Bold;
            font-size: 36px;
            color: #ffffff;
            text-align: center;

            &.cant-recieve {
                background: #999;
            }
        }
        .tip {
            display: flex;
            align-items: center;
            margin-top: 15px;
            font-family: MicrosoftYaHei;
            font-size: 22px;
            color: #fd4848;

            &::before {
                position: relative;
                top: -3px;
                content: '';
                margin-right: 10px;
                width: 26px;
                height: 24px;
                background: url('@/assets/img/benet_dialog/not.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .benet-product {
        padding-top: 20px;
        .product-top {
            display: flex;
            justify-content: space-between;
            font-family: MicrosoftYaHei;
            font-size: 30px;
            color: #fff;

            .product-title {
                flex: 1;
            }
            .product-cutdown {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                padding: 0 18px 0 55px;
                width: 206px;
                height: 44px;
                background: url('@/assets/img/benet_dialog/time.png') no-repeat;
                background-size: 100% 100%;
                :deep(.van-count-down) {
                    font-family: MicrosoftYaHei;
                    font-size: 30px;
                    color: #fff;
                }
            }
        }
        .product-list {
            @apply gap-[24px];
            margin-top: 25px;

            .product-item {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 10px;
                width: 325px;
                height: 367px;
                border-radius: 30px;
                background-color: #ffffff;

                .item-img {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding-top: 20px 80px 0;
                    height: 180px;

                    img {
                        max-width: 134px;
                    }
                }
                .item-num {
                    font-size: 36px;
                    font-weight: bold;
                    color: #111317;

                    span {
                        margin-right: 10px;
                    }
                }
                .item-tip {
                    font-family: MicrosoftYaHei;
                    font-size: 26px;
                    color: #a2a2a2;
                    .tip-not {
                        position: relative;
                        &::before {
                            position: absolute;
                            top: 50%;
                            left: 0;
                            content: '';
                            width: 40px;
                            height: 13px;
                            background: url('@/assets/img/cash_dialog/xiegang.png') no-repeat;
                            background-size: 100% 100%;
                            transform: translateY(-50%);
                        }
                    }
                    .tip-num {
                        margin: 0 5px 0 10px;
                        color: #fd4848;
                    }
                }
                .product-btn {
                    margin-top: 15px;
                    width: 223px;
                    height: 63px;
                    line-height: 63px;
                    background: linear-gradient(100deg, #f966ff 0%, #e659fe 46%, #d34bfd 100%), linear-gradient(#e59e20, #e59e20);
                    border-radius: 29px;
                    font-family: MicrosoftYaHei-Bold;
                    font-size: 28px;
                    color: #ffffff;
                    text-align: center;
                }
                .product-percent {
                    position: absolute;
                    top: -20px;
                    right: -25px;
                    width: 135px;
                    height: 135px;
                    background: url('@/assets/img/cash_dialog/percent.png') no-repeat;
                    background-size: 100% 100%;

                    .percent-num {
                        position: relative;
                        left: 50%;
                        display: inline-block;
                        font-family: San-Francisco-Text-Bold;
                        font-size: 46px;
                        line-height: 26px;
                        letter-spacing: -3px;
                        color: #fff3ee;

                        transform: translate3d(-50%, -15px, 0) rotate(20deg);
                    }
                }
            }
        }
    }
}
</style>
