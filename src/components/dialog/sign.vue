<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="3000">
        <div class="sign">
            <div class="title">
                <span>7</span>Day Gifts
                <div class="steps-wrap">
                    <div class="bar">
                        <div class="inner-bar" :style="process"></div>
                        <div class="steps" v-if="online.length">
                            <div
                                :class="['step', { finished: item.finish }, { recived: item.claimed }]"
                                v-for="(item, index) in online"
                                :key="index"
                                :style="{
                                    left: segment * (index + 1) + '%',
                                }"
                            >
                                <div class="step-icon" @click="handleRecived(item)">
                                    <div class="sun">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 72 72" fill="none">
                                            <path
                                                d="M24.8754 1.76198C28.4682 0.594596 32.2223 0 36 0V36L24.8754 1.76198Z"
                                                fill="url(#paint0_radial_525_52)"
                                            />
                                            <path
                                                d="M36 36L43.4848 0.786654C47.18 1.57207 50.7284 2.93421 54 4.82305L36 36Z"
                                                fill="url(#paint1_radial_525_52)"
                                            />
                                            <path
                                                d="M36 36L60.0887 9.24678C62.8961 11.7745 65.2881 14.7284 67.1769 18L36 36Z"
                                                fill="url(#paint2_radial_525_52)"
                                            />
                                            <path d="M36 36L70.238 24.8754C71.4054 28.4682 72 32.2223 72 36H36Z" fill="url(#paint3_radial_525_52)" />
                                            <path
                                                d="M36 36L67.177 54C69.0658 50.7284 70.4279 47.18 71.2133 43.4848L36 36Z"
                                                fill="url(#paint4_radial_525_52)"
                                            />
                                            <path
                                                d="M36 36L62.7533 60.0887C60.2255 62.896 57.2716 65.288 54.0001 67.1769L36 36Z"
                                                fill="url(#paint5_radial_525_52)"
                                            />
                                            <path d="M36 36L47.1246 70.238C43.5318 71.4054 39.7777 72 36 72V36Z" fill="url(#paint6_radial_525_52)" />
                                            <path
                                                d="M36 36L28.5152 71.2133C24.8201 70.4279 21.2716 69.0658 18 67.1769L36 36Z"
                                                fill="url(#paint7_radial_525_52)"
                                            />
                                            <path
                                                d="M36 36L11.9113 62.7532C9.10394 60.2255 6.71195 57.2716 4.82308 54L36 36Z"
                                                fill="url(#paint8_radial_525_52)"
                                            />
                                            <path d="M36 36L1.76195 47.1246C0.594596 43.5318 0 39.7777 0 36H36Z" fill="url(#paint9_radial_525_52)" />
                                            <path
                                                d="M36 36L0.786686 28.5152C1.5721 24.82 2.93424 21.2716 4.82308 18L36 36Z"
                                                fill="url(#paint10_radial_525_52)"
                                            />
                                            <path
                                                d="M36 36L9.24681 11.9113C11.7746 9.10391 14.7285 6.71191 18 4.82305L36 36Z"
                                                fill="url(#paint11_radial_525_52)"
                                            />
                                            <defs>
                                                <radialGradient
                                                    id="paint0_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint1_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint2_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint3_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint4_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint5_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint6_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint7_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint8_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint9_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint10_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                                <radialGradient
                                                    id="paint11_radial_525_52"
                                                    cx="0"
                                                    cy="0"
                                                    r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(36 36) rotate(90) scale(36)"
                                                >
                                                    <stop offset="0.075" stop-color="#FFF8A9" />
                                                    <stop offset="1" stop-color="#EAFF4F" stop-opacity="0" />
                                                </radialGradient>
                                            </defs>
                                        </svg>
                                    </div>
                                    <svg
                                        v-if="item.claimed"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="58"
                                        height="55"
                                        viewBox="0 0 58 55"
                                        fill="none"
                                    >
                                        <foreignObject x="0" y="0" width="0" height="0"
                                            ><div
                                                xmlns="http://www.w3.org/1999/xhtml"
                                                style="
                                                    backdrop-filter: blur(0px);
                                                    clip-path: url(#bgblur_0_14_168_clip_path);
                                                    height: 100%;
                                                    width: 100%;
                                                "
                                            ></div
                                        ></foreignObject>
                                        <path
                                            data-figma-bg-blur-radius="0"
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M6.19827 21.591C3.81605 20.4492 2.09703 18.7847 1.53712 17.4792C0.451124 14.9592 2.04913 10.3032 6.33713 9.17218C10.2862 8.13096 16.3981 12.892 19.5811 15.6822L20.5185 15.2683C20.6011 11.036 21.2003 3.31174 24.6301 1.09518C28.3551 -1.31082 32.8721 0.644178 34.0051 3.14218C34.5919 4.43474 34.6636 6.82808 33.9015 9.35943L37.5101 7.76616C37.8705 7.60706 38.2588 7.52051 38.6526 7.51145C39.0465 7.50239 39.4383 7.57099 39.8056 7.71335C40.173 7.8557 40.5087 8.06901 40.7936 8.3411C41.0785 8.6132 41.307 8.93875 41.4661 9.29916L43.9661 14.9602C44.2877 15.6876 44.3073 16.5129 44.0206 17.2547C43.7338 17.9965 43.1643 18.5941 42.4371 18.9162L6.71113 34.6882C6.35072 34.8473 5.9625 34.9338 5.56864 34.9429C5.17478 34.9519 4.78299 34.8833 4.41564 34.741C4.04829 34.5986 3.71258 34.3853 3.42767 34.1132C3.14276 33.8411 2.91423 33.5156 2.75513 33.1552L0.255133 27.4932C-0.0658628 26.7654 -0.0846935 25.9399 0.20278 25.1983C0.490254 24.4566 1.0605 23.8595 1.78813 23.5382L6.19827 21.591ZM13.4155 18.4045C12.1049 18.3765 10.6403 18.2949 9.09113 18.1052C6.95512 17.8442 5.23013 16.8482 5.83013 14.9562C6.17313 13.8762 7.44012 13.2362 9.31212 13.8312C10.3352 14.1594 12.7744 15.7359 14.4672 16.8299C14.857 17.0819 15.2072 17.3082 15.4914 17.4879L13.4155 18.4045ZM26.6867 12.5449C27.5488 11.5586 28.4749 10.4228 29.3781 9.15218C30.6251 7.39718 31.0481 5.45018 29.2481 4.62018C28.2191 4.14618 26.8921 4.65118 26.0711 6.43518C25.6204 7.41375 25.1393 10.2959 24.8074 12.2847C24.7324 12.7337 24.6651 13.1372 24.6074 13.463L26.6867 12.5449ZM24.4291 35.2802H8.60213C8.58294 35.2809 8.56477 35.289 8.55137 35.3028C8.53798 35.3166 8.53037 35.3349 8.53013 35.3542V52.1692C8.53013 52.9157 8.82671 53.6317 9.35463 54.1597C9.88254 54.6876 10.5985 54.9842 11.3451 54.9842H24.4451C24.4547 54.9842 24.4638 54.9804 24.4706 54.9736C24.4773 54.9669 24.4811 54.9577 24.4811 54.9482V35.3322C24.4811 35.3184 24.4757 35.3051 24.4659 35.2954C24.4562 35.2856 24.4429 35.2802 24.4291 35.2802ZM28.2491 35.2802H44.0771C44.087 35.28 44.0958 35.2818 44.1049 35.2855C44.1141 35.2891 44.1224 35.2945 44.1295 35.3014C44.1365 35.3083 44.1422 35.3165 44.146 35.3255C44.1499 35.3346 44.152 35.3443 44.1521 35.3542V52.1692C44.1521 52.9157 43.8556 53.6317 43.3276 54.1597C42.7997 54.6876 42.0837 54.9842 41.3371 54.9842H28.2331C28.2236 54.9842 28.2144 54.9804 28.2077 54.9736C28.2009 54.9669 28.1971 54.9577 28.1971 54.9482V35.3322C28.1971 35.3184 28.2026 35.3051 28.2124 35.2954C28.2221 35.2856 28.2353 35.2802 28.2491 35.2802ZM52.7581 21.8662C51.7249 19.6215 50.9699 17.259 50.5101 14.8312L50.5091 14.8302C50.5091 14.8302 49.9241 20.3152 48.5181 22.1222C46.9661 24.1332 43.8181 24.7222 43.8181 24.7222C45.4609 25.2095 46.9474 26.1183 48.1301 27.3582C49.9151 29.4142 50.5461 34.4482 50.5461 34.4482C50.5461 34.4482 51.3891 29.0652 52.5801 27.3082C53.9411 25.2932 57.4051 24.8932 57.4051 24.8932C56.462 24.7415 55.5611 24.3938 54.7607 23.8724C53.9602 23.351 53.278 22.6676 52.7581 21.8662ZM36.3531 26.9112C35.7055 25.5041 35.2323 24.0231 34.9441 22.5012H34.9421C34.9421 22.5012 34.5061 25.9542 33.6311 27.0922C32.8659 27.9204 31.8581 28.485 30.7521 28.7052C31.7808 29.011 32.7115 29.5805 33.4521 30.3572C34.5661 31.6462 34.9661 34.8022 34.9661 34.8022C34.9661 34.8022 35.4951 31.4252 36.2411 30.3252C37.0951 29.0642 39.2661 28.8112 39.2661 28.8112C38.6747 28.7157 38.1099 28.4973 37.6082 28.1701C37.1064 27.8428 36.6789 27.4139 36.3531 26.9112ZM48.4191 5.91116C47.7715 4.50409 47.2983 3.0231 47.0101 1.50116C47.0101 1.50116 46.4491 4.70716 45.5721 5.84216C44.8257 6.68499 43.8779 7.32491 42.8171 7.70216C43.8457 8.00808 44.7765 8.57757 45.5171 9.35416C46.6371 10.6482 47.0321 13.8022 47.0321 13.8022C47.0321 13.8022 47.5611 10.4252 48.3071 9.32516C49.1611 8.06416 51.3321 7.81116 51.3321 7.81116C50.7407 7.71573 50.1759 7.49734 49.6742 7.17006C49.1724 6.84279 48.7449 6.41393 48.4191 5.91116Z"
                                            fill="url(#paint0_linear_14_168)"
                                        />
                                        <defs>
                                            <clipPath id="bgblur_0_14_168_clip_path" transform="translate(0 0)">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M6.19827 21.591C3.81605 20.4492 2.09703 18.7847 1.53712 17.4792C0.451124 14.9592 2.04913 10.3032 6.33713 9.17218C10.2862 8.13096 16.3981 12.892 19.5811 15.6822L20.5185 15.2683C20.6011 11.036 21.2003 3.31174 24.6301 1.09518C28.3551 -1.31082 32.8721 0.644178 34.0051 3.14218C34.5919 4.43474 34.6636 6.82808 33.9015 9.35943L37.5101 7.76616C37.8705 7.60706 38.2588 7.52051 38.6526 7.51145C39.0465 7.50239 39.4383 7.57099 39.8056 7.71335C40.173 7.8557 40.5087 8.06901 40.7936 8.3411C41.0785 8.6132 41.307 8.93875 41.4661 9.29916L43.9661 14.9602C44.2877 15.6876 44.3073 16.5129 44.0206 17.2547C43.7338 17.9965 43.1643 18.5941 42.4371 18.9162L6.71113 34.6882C6.35072 34.8473 5.9625 34.9338 5.56864 34.9429C5.17478 34.9519 4.78299 34.8833 4.41564 34.741C4.04829 34.5986 3.71258 34.3853 3.42767 34.1132C3.14276 33.8411 2.91423 33.5156 2.75513 33.1552L0.255133 27.4932C-0.0658628 26.7654 -0.0846935 25.9399 0.20278 25.1983C0.490254 24.4566 1.0605 23.8595 1.78813 23.5382L6.19827 21.591ZM13.4155 18.4045C12.1049 18.3765 10.6403 18.2949 9.09113 18.1052C6.95512 17.8442 5.23013 16.8482 5.83013 14.9562C6.17313 13.8762 7.44012 13.2362 9.31212 13.8312C10.3352 14.1594 12.7744 15.7359 14.4672 16.8299C14.857 17.0819 15.2072 17.3082 15.4914 17.4879L13.4155 18.4045ZM26.6867 12.5449C27.5488 11.5586 28.4749 10.4228 29.3781 9.15218C30.6251 7.39718 31.0481 5.45018 29.2481 4.62018C28.2191 4.14618 26.8921 4.65118 26.0711 6.43518C25.6204 7.41375 25.1393 10.2959 24.8074 12.2847C24.7324 12.7337 24.6651 13.1372 24.6074 13.463L26.6867 12.5449ZM24.4291 35.2802H8.60213C8.58294 35.2809 8.56477 35.289 8.55137 35.3028C8.53798 35.3166 8.53037 35.3349 8.53013 35.3542V52.1692C8.53013 52.9157 8.82671 53.6317 9.35463 54.1597C9.88254 54.6876 10.5985 54.9842 11.3451 54.9842H24.4451C24.4547 54.9842 24.4638 54.9804 24.4706 54.9736C24.4773 54.9669 24.4811 54.9577 24.4811 54.9482V35.3322C24.4811 35.3184 24.4757 35.3051 24.4659 35.2954C24.4562 35.2856 24.4429 35.2802 24.4291 35.2802ZM28.2491 35.2802H44.0771C44.087 35.28 44.0958 35.2818 44.1049 35.2855C44.1141 35.2891 44.1224 35.2945 44.1295 35.3014C44.1365 35.3083 44.1422 35.3165 44.146 35.3255C44.1499 35.3346 44.152 35.3443 44.1521 35.3542V52.1692C44.1521 52.9157 43.8556 53.6317 43.3276 54.1597C42.7997 54.6876 42.0837 54.9842 41.3371 54.9842H28.2331C28.2236 54.9842 28.2144 54.9804 28.2077 54.9736C28.2009 54.9669 28.1971 54.9577 28.1971 54.9482V35.3322C28.1971 35.3184 28.2026 35.3051 28.2124 35.2954C28.2221 35.2856 28.2353 35.2802 28.2491 35.2802ZM52.7581 21.8662C51.7249 19.6215 50.9699 17.259 50.5101 14.8312L50.5091 14.8302C50.5091 14.8302 49.9241 20.3152 48.5181 22.1222C46.9661 24.1332 43.8181 24.7222 43.8181 24.7222C45.4609 25.2095 46.9474 26.1183 48.1301 27.3582C49.9151 29.4142 50.5461 34.4482 50.5461 34.4482C50.5461 34.4482 51.3891 29.0652 52.5801 27.3082C53.9411 25.2932 57.4051 24.8932 57.4051 24.8932C56.462 24.7415 55.5611 24.3938 54.7607 23.8724C53.9602 23.351 53.278 22.6676 52.7581 21.8662ZM36.3531 26.9112C35.7055 25.5041 35.2323 24.0231 34.9441 22.5012H34.9421C34.9421 22.5012 34.5061 25.9542 33.6311 27.0922C32.8659 27.9204 31.8581 28.485 30.7521 28.7052C31.7808 29.011 32.7115 29.5805 33.4521 30.3572C34.5661 31.6462 34.9661 34.8022 34.9661 34.8022C34.9661 34.8022 35.4951 31.4252 36.2411 30.3252C37.0951 29.0642 39.2661 28.8112 39.2661 28.8112C38.6747 28.7157 38.1099 28.4973 37.6082 28.1701C37.1064 27.8428 36.6789 27.4139 36.3531 26.9112ZM48.4191 5.91116C47.7715 4.50409 47.2983 3.0231 47.0101 1.50116C47.0101 1.50116 46.4491 4.70716 45.5721 5.84216C44.8257 6.68499 43.8779 7.32491 42.8171 7.70216C43.8457 8.00808 44.7765 8.57757 45.5171 9.35416C46.6371 10.6482 47.0321 13.8022 47.0321 13.8022C47.0321 13.8022 47.5611 10.4252 48.3071 9.32516C49.1611 8.06416 51.3321 7.81116 51.3321 7.81116C50.7407 7.71573 50.1759 7.49734 49.6742 7.17006C49.1724 6.84279 48.7449 6.41393 48.4191 5.91116Z"
                                                />
                                            </clipPath>
                                            <linearGradient
                                                id="paint0_linear_14_168"
                                                x1="28.7026"
                                                y1="0"
                                                x2="28.7026"
                                                y2="54.9842"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#FFFF8F" />
                                                <stop offset="1" stop-color="#EFFF77" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    <svg
                                        v-else-if="item.finish"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="46"
                                        height="46"
                                        viewBox="0 0 46 46"
                                        fill="none"
                                    >
                                        <foreignObject x="0" y="0" width="0" height="0"
                                            ><div
                                                xmlns="http://www.w3.org/1999/xhtml"
                                                style="
                                                    backdrop-filter: blur(0px);
                                                    clip-path: url(#bgblur_0_14_167_clip_path);
                                                    height: 100%;
                                                    width: 100%;
                                                "
                                            ></div
                                        ></foreignObject>
                                        <g data-figma-bg-blur-radius="0">
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M4.76 25.4651H20.587C20.5938 25.4651 20.6006 25.4665 20.6069 25.4691C20.6132 25.4717 20.6189 25.4755 20.6238 25.4803C20.6286 25.4852 20.6324 25.4909 20.635 25.4972C20.6377 25.5035 20.639 25.5103 20.639 25.5171V45.1331C20.639 45.1427 20.6352 45.1518 20.6285 45.1586C20.6217 45.1653 20.6125 45.1691 20.603 45.1691H7.503C6.75641 45.1691 6.04041 44.8725 5.5125 44.3446C4.98458 43.8167 4.688 43.1007 4.688 42.3541V25.5391C4.688 25.5198 4.69551 25.5013 4.70896 25.4875C4.72241 25.4737 4.74073 25.4656 4.76 25.4651ZM40.235 25.4651H24.407C24.3934 25.4654 24.3804 25.471 24.3709 25.4807C24.3613 25.4904 24.356 25.5035 24.356 25.5171V45.1331C24.356 45.1425 24.3597 45.1515 24.3662 45.1582C24.3727 45.1649 24.3816 45.1689 24.391 45.1691H37.491C38.2375 45.1689 38.9534 44.8722 39.4812 44.3443C40.0091 43.8165 40.3057 43.1006 40.306 42.3541V25.5391C40.306 25.52 40.2986 25.5016 40.2854 25.4878C40.2722 25.474 40.2541 25.4659 40.235 25.4651ZM3 11.3831H42.031C42.8267 11.3831 43.5897 11.6992 44.1523 12.2618C44.7149 12.8244 45.031 13.5875 45.031 14.3831V20.5711C45.031 21.3668 44.7149 22.1298 44.1523 22.6924C43.5897 23.255 42.8267 23.5711 42.031 23.5711H3C2.20435 23.5711 1.44129 23.255 0.878677 22.6924C0.316068 22.1298 0 21.3668 0 20.5711V14.3831C0 13.5875 0.316068 12.8244 0.878677 12.2618C1.44129 11.6992 2.20435 11.3831 3 11.3831Z"
                                                fill="url(#paint0_linear_14_167)"
                                            />
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M22.678 13.1661C23.901 10.3081 28.171 0.84914 32.955 0.0881402C37.334 -0.61186 40.677 3.00414 40.705 5.74714C40.735 8.68214 36.852 15.0301 29.915 15.8741C22.978 16.7181 21.722 15.3981 22.678 13.1661ZM23.235 13.1661C22.012 10.3081 17.742 0.84914 12.958 0.0881402C8.579 -0.61186 5.236 3.00414 5.208 5.74714C5.178 8.68214 9.061 15.0301 15.998 15.8741C22.935 16.7181 24.191 15.3981 23.235 13.1661ZM24.867 13.8131C24.824 13.9301 26.294 13.8311 27.894 11.4601C28.872 10.0091 31.111 6.44214 32.116 5.55514C33.588 4.25514 35.006 4.32914 35.756 5.17714C37.07 6.66514 35.893 8.27714 34.045 9.37714C29.334 12.1811 25.039 13.3451 24.867 13.8131ZM21.046 13.8131C21.09 13.9301 19.619 13.8311 18.02 11.4601C17.041 10.0091 14.802 6.44214 13.797 5.55514C12.325 4.25514 10.907 4.32914 10.157 5.17714C8.84299 6.66514 10.02 8.27714 11.868 9.37714C16.579 12.1811 20.874 13.3451 21.05 13.8131H21.046Z"
                                                fill="url(#paint1_linear_14_167)"
                                            />
                                        </g>
                                        <defs>
                                            <clipPath id="bgblur_0_14_167_clip_path" transform="translate(0 0)">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M4.76 25.4651H20.587C20.5938 25.4651 20.6006 25.4665 20.6069 25.4691C20.6132 25.4717 20.6189 25.4755 20.6238 25.4803C20.6286 25.4852 20.6324 25.4909 20.635 25.4972C20.6377 25.5035 20.639 25.5103 20.639 25.5171V45.1331C20.639 45.1427 20.6352 45.1518 20.6285 45.1586C20.6217 45.1653 20.6125 45.1691 20.603 45.1691H7.503C6.75641 45.1691 6.04041 44.8725 5.5125 44.3446C4.98458 43.8167 4.688 43.1007 4.688 42.3541V25.5391C4.688 25.5198 4.69551 25.5013 4.70896 25.4875C4.72241 25.4737 4.74073 25.4656 4.76 25.4651ZM40.235 25.4651H24.407C24.3934 25.4654 24.3804 25.471 24.3709 25.4807C24.3613 25.4904 24.356 25.5035 24.356 25.5171V45.1331C24.356 45.1425 24.3597 45.1515 24.3662 45.1582C24.3727 45.1649 24.3816 45.1689 24.391 45.1691H37.491C38.2375 45.1689 38.9534 44.8722 39.4812 44.3443C40.0091 43.8165 40.3057 43.1006 40.306 42.3541V25.5391C40.306 25.52 40.2986 25.5016 40.2854 25.4878C40.2722 25.474 40.2541 25.4659 40.235 25.4651ZM3 11.3831H42.031C42.8267 11.3831 43.5897 11.6992 44.1523 12.2618C44.7149 12.8244 45.031 13.5875 45.031 14.3831V20.5711C45.031 21.3668 44.7149 22.1298 44.1523 22.6924C43.5897 23.255 42.8267 23.5711 42.031 23.5711H3C2.20435 23.5711 1.44129 23.255 0.878677 22.6924C0.316068 22.1298 0 21.3668 0 20.5711V14.3831C0 13.5875 0.316068 12.8244 0.878677 12.2618C1.44129 11.6992 2.20435 11.3831 3 11.3831Z"
                                                />
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M22.678 13.1661C23.901 10.3081 28.171 0.84914 32.955 0.0881402C37.334 -0.61186 40.677 3.00414 40.705 5.74714C40.735 8.68214 36.852 15.0301 29.915 15.8741C22.978 16.7181 21.722 15.3981 22.678 13.1661ZM23.235 13.1661C22.012 10.3081 17.742 0.84914 12.958 0.0881402C8.579 -0.61186 5.236 3.00414 5.208 5.74714C5.178 8.68214 9.061 15.0301 15.998 15.8741C22.935 16.7181 24.191 15.3981 23.235 13.1661ZM24.867 13.8131C24.824 13.9301 26.294 13.8311 27.894 11.4601C28.872 10.0091 31.111 6.44214 32.116 5.55514C33.588 4.25514 35.006 4.32914 35.756 5.17714C37.07 6.66514 35.893 8.27714 34.045 9.37714C29.334 12.1811 25.039 13.3451 24.867 13.8131ZM21.046 13.8131C21.09 13.9301 19.619 13.8311 18.02 11.4601C17.041 10.0091 14.802 6.44214 13.797 5.55514C12.325 4.25514 10.907 4.32914 10.157 5.17714C8.84299 6.66514 10.02 8.27714 11.868 9.37714C16.579 12.1811 20.874 13.3451 21.05 13.8131H21.046Z"
                                                />
                                            </clipPath>
                                            <linearGradient
                                                id="paint0_linear_14_167"
                                                x1="22.5155"
                                                y1="0"
                                                x2="22.5155"
                                                y2="45.1691"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#FFFF8F" />
                                                <stop offset="1" stop-color="#EFFF77" />
                                            </linearGradient>
                                            <linearGradient
                                                id="paint1_linear_14_167"
                                                x1="22.5155"
                                                y1="0"
                                                x2="22.5155"
                                                y2="45.1691"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#FFFF8F" />
                                                <stop offset="1" stop-color="#EFFF77" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="46" height="46" viewBox="0 0 46 46" fill="none">
                                        <path
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M4.76 25.4651H20.587C20.5938 25.4651 20.6006 25.4665 20.6069 25.4691C20.6132 25.4717 20.6189 25.4755 20.6238 25.4803C20.6286 25.4852 20.6324 25.4909 20.635 25.4972C20.6377 25.5035 20.639 25.5103 20.639 25.5171V45.1331C20.639 45.1427 20.6352 45.1518 20.6285 45.1586C20.6217 45.1653 20.6125 45.1691 20.603 45.1691H7.503C6.75641 45.1691 6.04041 44.8725 5.5125 44.3446C4.98458 43.8167 4.688 43.1007 4.688 42.3541V25.5391C4.688 25.5198 4.69551 25.5013 4.70896 25.4875C4.72241 25.4737 4.74073 25.4656 4.76 25.4651ZM40.235 25.4651H24.407C24.3934 25.4654 24.3804 25.471 24.3709 25.4807C24.3613 25.4904 24.356 25.5035 24.356 25.5171V45.1331C24.356 45.1425 24.3597 45.1515 24.3662 45.1582C24.3727 45.1649 24.3816 45.1689 24.391 45.1691H37.491C38.2375 45.1689 38.9534 44.8722 39.4812 44.3443C40.0091 43.8165 40.3057 43.1006 40.306 42.3541V25.5391C40.306 25.52 40.2986 25.5016 40.2854 25.4878C40.2722 25.474 40.2541 25.4659 40.235 25.4651ZM3 11.3831H42.031C42.8267 11.3831 43.5897 11.6992 44.1523 12.2618C44.7149 12.8244 45.031 13.5875 45.031 14.3831V20.5711C45.031 21.3668 44.7149 22.1298 44.1523 22.6924C43.5897 23.255 42.8267 23.5711 42.031 23.5711H3C2.20435 23.5711 1.44129 23.255 0.878677 22.6924C0.316068 22.1298 0 21.3668 0 20.5711V14.3831C0 13.5875 0.316068 12.8244 0.878677 12.2618C1.44129 11.6992 2.20435 11.3831 3 11.3831Z"
                                            fill="#5AB178"
                                        />
                                        <path
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M22.678 13.1661C23.901 10.3081 28.171 0.84914 32.955 0.0881402C37.334 -0.61186 40.677 3.00414 40.705 5.74714C40.735 8.68214 36.852 15.0301 29.915 15.8741C22.978 16.7181 21.722 15.3981 22.678 13.1661ZM23.235 13.1661C22.012 10.3081 17.742 0.84914 12.958 0.0881402C8.579 -0.61186 5.236 3.00414 5.208 5.74714C5.178 8.68214 9.061 15.0301 15.998 15.8741C22.935 16.7181 24.191 15.3981 23.235 13.1661ZM24.867 13.8131C24.824 13.9301 26.294 13.8311 27.894 11.4601C28.872 10.0091 31.111 6.44214 32.116 5.55514C33.588 4.25514 35.006 4.32914 35.756 5.17714C37.07 6.66514 35.893 8.27714 34.045 9.37714C29.334 12.1811 25.039 13.3451 24.867 13.8131ZM21.046 13.8131C21.09 13.9301 19.619 13.8311 18.02 11.4601C17.041 10.0091 14.802 6.44214 13.797 5.55514C12.325 4.25514 10.907 4.32914 10.157 5.17714C8.84299 6.66514 10.02 8.27714 11.868 9.37714C16.579 12.1811 20.874 13.3451 21.05 13.8131H21.046Z"
                                            fill="#5AB178"
                                        />
                                    </svg>
                                </div>
                                <div class="step-minus">{{ item.time }}mins</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="close" @click="handleClose"></div>
            <div class="content">
                <div class="gift">
                    <div
                        v-for="(item, index) in signin"
                        :key="index"
                        :class="['gift-item', { active: item.finish && !item.claimed }, { claimed: item.claimed }]"
                        @click="handleGiftClick(item)"
                    >
                        <div class="mask">
                            <svg xmlns="http://www.w3.org/2000/svg" width="63" height="63" viewBox="0 0 63 63" fill="none">
                                <g filter="url(#filter0_d_137_144)">
                                    <path
                                        d="M31.5 11.8334C27.9951 11.8329 24.5537 12.7691 21.532 14.5451C18.5104 16.321 16.0182 18.8723 14.3135 21.9347C12.6089 24.9972 11.7535 28.4595 11.8361 31.9635C11.9187 35.4674 12.9362 38.8856 14.7833 41.8644C15.1272 42.4185 15.2369 43.0867 15.0883 43.7217C14.9396 44.3568 14.5447 44.9068 13.9905 45.2507C13.4363 45.5946 12.7682 45.7043 12.1331 45.5556C11.4981 45.407 10.9481 45.0121 10.6042 44.4579C8.18725 40.5685 6.9097 36.0792 6.91665 31.5C6.91665 17.9226 17.9226 6.91669 31.5 6.91669C45.0774 6.91669 56.0833 17.9226 56.0833 31.5C56.0833 45.0774 45.0774 56.0834 31.5 56.0834C26.693 56.0897 21.9906 54.6807 17.9792 52.032C17.698 51.8598 17.4543 51.6329 17.2626 51.3646C17.0709 51.0963 16.9352 50.7922 16.8634 50.4703C16.7917 50.1485 16.7854 49.8155 16.845 49.4913C16.9046 49.167 17.0288 48.858 17.2102 48.5826C17.3917 48.3073 17.6267 48.0713 17.9012 47.8887C18.1757 47.7061 18.4842 47.5805 18.8082 47.5196C19.1323 47.4586 19.4653 47.4634 19.7874 47.5338C20.1095 47.6041 20.4142 47.7385 20.6833 47.9291C23.2436 49.6138 26.1622 50.6767 29.2063 51.0332C32.2503 51.3897 35.3355 51.0298 38.2158 49.9822C41.096 48.9347 43.6916 47.2284 45.7954 44.9997C47.8991 42.7709 49.4529 40.0813 50.3327 37.1454C51.2125 34.2096 51.3939 31.1087 50.8625 28.0903C50.3312 25.0719 49.1017 22.2194 47.2722 19.7605C45.4427 17.3016 43.0639 15.3043 40.3255 13.9279C37.587 12.5516 34.5648 11.8343 31.5 11.8334ZM44.3005 27.0922C44.7483 26.6286 44.9961 26.0076 44.9905 25.363C44.9849 24.7185 44.7264 24.1019 44.2706 23.6461C43.8148 23.1903 43.1982 22.9318 42.5536 22.9262C41.9091 22.9206 41.2881 23.1683 40.8244 23.6161L29.0417 35.3989L22.1755 28.5328C21.7119 28.085 21.0909 27.8372 20.4463 27.8428C19.8018 27.8484 19.1852 28.107 18.7294 28.5628C18.2736 29.0186 18.0151 29.6351 18.0095 30.2797C18.0039 30.9243 18.2516 31.5452 18.6994 32.0089L27.3036 40.6131C27.7646 41.0739 28.3898 41.3328 29.0417 41.3328C29.6935 41.3328 30.3187 41.0739 30.7797 40.6131L44.3005 27.0922Z"
                                        fill="url(#paint0_linear_137_144)"
                                    />
                                </g>
                                <defs>
                                    <filter
                                        id="filter0_d_137_144"
                                        x="0.916626"
                                        y="0.916687"
                                        width="61.1667"
                                        height="61.1667"
                                        filterUnits="userSpaceOnUse"
                                        color-interpolation-filters="sRGB"
                                    >
                                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                        <feColorMatrix
                                            in="SourceAlpha"
                                            type="matrix"
                                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                            result="hardAlpha"
                                        />
                                        <feOffset />
                                        <feGaussianBlur stdDeviation="3" />
                                        <feComposite in2="hardAlpha" operator="out" />
                                        <feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.933333 0 0 0 0 0.52549 0 0 0 0.6 0" />
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_137_144" />
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_137_144" result="shape" />
                                    </filter>
                                    <linearGradient
                                        id="paint0_linear_137_144"
                                        x1="6.91663"
                                        y1="31.5"
                                        x2="56.0833"
                                        y2="31.5"
                                        gradientUnits="userSpaceOnUse"
                                    >
                                        <stop stop-color="#2AEE88" />
                                        <stop offset="1" stop-color="#9AE871" />
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <div class="day-icon">
                            Day <span>{{ item.time }}</span>
                        </div>
                        <div class="pic-wrap">
                            <img v-if="getGiftType(item) === 'bonus'" :src="BonusPic" />
                            <img v-else-if="getGiftType(item) === 'gold'" :src="GoldPic" />
                            <img v-else :src="getRewardItem(item).img" />
                            <div class="sun">
                                <svg xmlns="http://www.w3.org/2000/svg" width="163" height="163" viewBox="0 0 163 163" fill="none">
                                    <mask id="mask0_256_58" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="163" height="163">
                                        <circle cx="81.5" cy="81.5" r="81.5" fill="url(#paint0_radial_256_58)" />
                                    </mask>
                                    <g mask="url(#mask0_256_58)">
                                        <path
                                            d="M56.6241 4.93998C64.658 2.32958 73.0527 1 81.5 1V81.5L56.6241 4.93998Z"
                                            fill="url(#paint1_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L98.2368 2.75905C106.5 4.51532 114.434 7.56122 121.75 11.7849L81.5 81.5Z"
                                            fill="url(#paint2_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L135.365 21.6768C141.643 27.3292 146.991 33.9344 151.215 41.25L81.5 81.5Z"
                                            fill="url(#paint3_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L158.06 56.6241C160.67 64.658 162 73.0527 162 81.5H81.5Z"
                                            fill="url(#paint4_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L151.215 121.75C155.439 114.434 158.485 106.5 160.241 98.2369L81.5 81.5Z"
                                            fill="url(#paint5_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L141.323 135.365C135.671 141.643 129.066 146.991 121.75 151.215L81.5 81.5Z"
                                            fill="url(#paint6_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L106.376 158.06C98.342 160.67 89.9474 162 81.5 162V81.5Z"
                                            fill="url(#paint7_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L64.7632 160.241C56.5004 158.485 48.5656 155.439 41.25 151.215L81.5 81.5Z"
                                            fill="url(#paint8_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L27.635 141.323C21.3574 135.671 16.0087 129.066 11.7849 121.75L81.5 81.5Z"
                                            fill="url(#paint9_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L4.93991 106.376C2.32958 98.342 1 89.9473 1 81.5H81.5Z"
                                            fill="url(#paint10_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L2.75912 64.7631C4.5154 56.5003 7.5613 48.5655 11.7849 41.2499L81.5 81.5Z"
                                            fill="url(#paint11_radial_256_58)"
                                        />
                                        <path
                                            d="M81.5 81.5L21.6769 27.6349C27.3293 21.3573 33.9345 16.0086 41.2501 11.7849L81.5 81.5Z"
                                            fill="url(#paint12_radial_256_58)"
                                        />
                                    </g>
                                    <defs>
                                        <radialGradient
                                            id="paint0_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(81.5)"
                                        >
                                            <stop offset="0.595092" />
                                            <stop offset="1" stop-opacity="0" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint1_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint2_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint3_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint4_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint5_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint6_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint7_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint8_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint9_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint10_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint11_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                        <radialGradient
                                            id="paint12_radial_256_58"
                                            cx="0"
                                            cy="0"
                                            r="1"
                                            gradientUnits="userSpaceOnUse"
                                            gradientTransform="translate(81.5 81.5) rotate(90) scale(80.5)"
                                        >
                                            <stop offset="0.075" stop-color="#FFFCD8" />
                                            <stop offset="1" stop-color="#F7FF89" />
                                        </radialGradient>
                                    </defs>
                                </svg>
                            </div>
                        </div>
                        <div class="text">
                            {{ item.name }}<span>{{ store.cureencySymbol() }}{{ item.worth }}</span>
                        </div>
                    </div>
                </div>
                <div :class="['btn', { disabled: btnType === 2 }]" @click="handleSign">
                    {{ $t(btnType === 3 ? 'dps_to_claim' : btnType === 2 ? 'claim_tmr' : 'Claim') }}
                </div>
                <div v-if="todaySign.rewardType === 'product' ? todaySign.product.flowmult : flowmult" class="question" @click="handleTipClick">
                    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05887 27.9411 0 18 0C8.05887 0 0 8.05887 0 18C0 27.9411 8.05887 36 18 36ZM13.3395 7.32049C11.9185 8.69331 11.1839 10.6201 11.1357 13.1008C11.1357 13.1008 11.2728 14.7942 13.2853 14.7444C15.2978 14.6946 15.4348 13.1008 15.4348 13.1008C15.4589 11.9447 15.7479 11.0897 16.3019 10.5358C16.8558 9.98183 17.6386 9.70486 18.6501 9.70486C19.5653 9.70486 20.2758 9.92162 20.7816 10.3551C21.2874 10.7646 21.5402 11.4149 21.5402 12.306C21.5402 12.9563 21.4078 13.5222 21.1428 14.0039C20.902 14.4615 20.5769 14.8951 20.1674 15.3045C19.7821 15.6899 19.3606 16.0872 18.903 16.4967C18.4695 16.9061 18.048 17.3637 17.6386 17.8695C17.2532 18.3753 16.9281 18.9653 16.6631 19.6397C16.4223 20.3141 16.3019 21.1329 16.3019 22.0963C16.3019 22.0963 16.3802 23.7898 18.3792 23.7761C20.3781 23.7625 20.4564 22.0963 20.4564 22.0963C20.4564 21.3256 20.6009 20.6633 20.89 20.1094C21.2031 19.5313 21.6005 19.0135 22.0821 18.5559C22.5638 18.0742 23.0696 17.6046 23.5995 17.147C24.1293 16.6894 24.6351 16.2077 25.1168 15.7019C25.5985 15.172 25.9838 14.5699 26.2728 13.8956C26.5859 13.1971 26.7425 12.3903 26.7425 11.4751C26.7425 10.1263 26.4294 8.99436 25.8032 8.07915C25.177 7.16394 24.2979 6.46549 23.1659 5.9838C22.034 5.50211 20.6852 5.26127 19.1198 5.26127C16.6872 5.26127 14.7605 5.94768 13.3395 7.32049ZM20.2758 26.0703C19.77 25.5404 19.1077 25.2755 18.2888 25.2755C17.494 25.2755 16.8317 25.5404 16.3019 26.0703C15.7961 26.576 15.5432 27.2384 15.5432 28.0572C15.5432 28.8761 15.7961 29.5505 16.3019 30.0803C16.8317 30.6102 17.494 30.8751 18.2888 30.8751C19.1077 30.8751 19.77 30.6102 20.2758 30.0803C20.8057 29.5505 21.0706 28.8761 21.0706 28.0572C21.0706 27.2384 20.8057 26.576 20.2758 26.0703Z"
                            fill="white"
                        />
                    </svg>
                </div>
                <div v-if="showTip" ref="tipRoot" class="question-tip" v-html="signTip"></div>
            </div>
        </div>
    </van-overlay>
    <ProductChannel v-model="showChannel" />
    <signaward v-model="signawardinfo.isshow" v-if="signawardinfo.isshow" :type="signawardinfo.type" :msg="signawardinfo.items" />
</template>
<script setup lang="ts">
import ProductChannel from './productChannel.vue'
import signaward from './signaward.vue'
import popupManager from '@/utils/PopupManager'
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import eventBus, { EVENT_KEY } from '@/utils/bus'
import BonusPic from '@/assets/img/dialog/sign/bonus.png'
import GoldPic from '@/assets/img/dialog/sign/cash.png'
import dayjs from 'dayjs'
import { Log } from '@/api/log'
import { useClickAway } from '@vant/use'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})
const showed = defineModel()
const store = useBaseStore()
const i18n = useI18n()

const orderParams = reactive({
    item: {},
    source: 'onlinesign',
})
provide('orderParams', orderParams)

const showChannel = ref(false)
const online = ref([])
const onlineTime = ref(0)
const signin = ref([])
const items = ref({})
const flowmult = ref(0)
const showTip = ref(false)
const tipRoot = ref(null)
const signawardinfo = ref({
    isshow: false,
    type: 2,
    items: {},
})

const segment = computed(() => {
    if (!online.value.length) {
        return 0
    }
    return 100 / online.value.length
})

const process = computed(() => {
    if (!online.value.length || !onlineTime.value) {
        return {
            width: '0%',
        }
    }
    const last = online.value[online.value.length - 1]
    if (onlineTime.value >= last.time) {
        return {
            width: '100%',
        }
    }
    let finishIdx = online.value.findIndex((item) => onlineTime.value <= item.time)

    const prevMinus = finishIdx ? online.value[finishIdx - 1].time : 0
    const remian = onlineTime.value - prevMinus
    const curTotal = online.value[finishIdx].time - prevMinus
    return {
        width: `${Math.floor(finishIdx * segment.value + (remian / curTotal) * segment.value)}%`,
    }
})

const todaySign = computed(() => {
    const list = [...signin.value]
    return list.reverse().find((item) => item.finish) || {}
})
const signTip = computed(() => {
    const products = signin.value.filter((item) => item.rewardType === 'product')

    const str = products.reduce((prev, cur) => {
        return (
            prev +
            i18n.t('7day_cash_info', {
                current_type: store.cureencySymbol(),
                current_number: cur.product.worth.gold,
                time: cur.product.flowmult,
            }) +
            '</br>'
        )
    }, '')
    return str + i18n.t('7day_cash_info1', { time: flowmult.value })
})
// 0 置灰 1 可免费领取 2 当日已领取 3 需要充值领取
const btnType = computed(() => {
    if (!Object.keys(todaySign.value).length) {
        return 0
    }
    const { finish, claimed, rewardType } = todaySign.value
    return claimed ? 2 : finish ? (rewardType === 'reward' ? 1 : 3) : 0
})
watch(todaySign, () => {
    orderParams.item = todaySign.value.product
})
useClickAway(tipRoot, (e) => {
    if (!e.target.classList.contains('question')) {
        handleTipClick()
    }
})
const handleTipClick = () => {
    showTip.value = !showTip.value
}
const getRewardItem = (item) => {
    const rewardItem = item.reward[0] || {}
    const { id } = rewardItem
    return item.rewardType === 'reward'
        ? ['gold', 'bonus'].includes(id)
            ? {
                  img: id === 'gold' ? GoldPic : BonusPic,
                  ...rewardItem,
                  ...item,
                  name: item.name,
              }
            : { ...(items.value[id] || {}), ...item, name: item.name }
        : item.product
}
// bonus  gold  product
const getGiftType = (item) => {
    const isRward = item.rewardType === 'reward'
    const gifts = getRewardItem(item)

    if (isRward) {
        if (['bonus', 'gold'].includes(gifts.id)) {
            return gifts.id
        }
    } else {
        return gifts.gifts?.gold ? 'gold' : 'bonus'
    }
    return 'product'
}

const handleRecived = (item) => {
    if (item.claimed) {
        return
    }
    reciveGifts(item, 'online')
}
const handleGiftClick = (item) => {
    const { finish, claimed, rewardType } = item
    if (claimed) {
        return
    }
    if (finish) {
        if (rewardType === 'reward') {
            reciveGifts(item, 'signin')
        } else {
            showed.value = false
            showChannel.value = true
        }
    }
}
const reciveGifts = async (item, type = 'online') => {
    try {
        const res = await store.ReceiveRewards({ id: 'onlinesign', content: { type, time: item.time } })
        if (res.code === 200) {
            // showSuccessToast(i18n.t('Successful_collection'))
            const gift =
                type === 'online'
                    ? item.reward.map((itd) => {
                          const id = itd.id
                          if (['gold', 'bonus'].includes(id)) {
                              return {
                                  img: id === 'gold' ? GoldPic : BonusPic,
                                  ...itd,
                                  ...item,
                                  name: item.name,
                              }
                          }
                          return {
                              ...(items.value[id] || {}),
                              ...itd,
                              name: item.name,
                          }
                      })
                    : getRewardItem(item)
            // eventBus.emit(EVENT_KEY.PAYMENT, { item: gift, type: 2 })
            signawardinfo.value = {
                isshow: true,
                type: 2,
                items: gift,
            }
            getSignEvent(false)
            eventBus.emit('freeGametimes')
            // showed.value = false
        } else {
            showFailToast(i18n.t('message_claim_fail'))
        }
    } catch (e) {
        console.log(e)
    }
}
const handleSign = () => {
    if (btnType.value === 1) {
        reciveGifts(todaySign.value, 'signin')
        // 领取免费礼物
    } else if (btnType.value === 3) {
        Log({
            event: 'onlinesign_deposit',
        })
        showed.value = false
        showChannel.value = true
    } else {
        handleClose()
    }
}
const handleClose = () => {
    showed.value = false
    popupManager.checkQueue()
}
const getSignEvent = async (isclose = true) => {
    try {
        const res = await store.getSignEvent(
            {
                id: 'onlinesign',
            },
            false,
            false
        )

        if (res.code === 200) {
            const {
                status,
                online: onlineData,
                onlineTime: onlineTimeData,
                signin: signinData,
                items: itemsData,
                flowmult: flowmultData,
            } = res.acticity

            const today = dayjs().format('YYYY-MM-DD')
            const uid = store.userInfo?.uid
            let dayList = store.signDate[uid]
            if (!dayList) {
                dayList = store.signDate[uid] = []
            }
            const isExist = dayList.includes(today)
            console.log('isExis-------t', isExist)
            if (props.type !== 1 && isExist && isclose) {
                popupManager.checkQueue()
            } else if (
                !isclose ||
                (status === 1 &&
                    (store.newUser ||
                        props.type === 1 ||
                        onlineData.some((item) => item.finish && !item.claimed) ||
                        signinData.some((item) => item.finish && !item.claimed) ||
                        !isExist))
            ) {
                online.value = onlineData
                onlineTime.value = onlineTimeData
                signin.value = signinData
                items.value = itemsData
                flowmult.value = flowmultData
                showed.value = true
                if (!isExist) {
                    store.signDate[uid] = [...dayList, today]
                }
            } else {
                if (signinData.every((item) => item.finish && item.claimed)) {
                    eventBus.emit('sign', false)
                }
                popupManager.checkQueue()
            }
            eventBus.emit('sign', true)
        } else {
            eventBus.emit('sign', false)
            popupManager.checkQueue()
        }
    } catch (e) {
        eventBus.emit('sign', false)
        popupManager.checkQueue()
    }
}
onBeforeMount(() => {
    getSignEvent()
})
</script>
<style lang="scss" scoped>
.sign {
    position: absolute;
    top: 55%;
    left: 50%;
    width: 706px;
    font-family: 'Microsoft YaHei';
    border-radius: 60px;
    color: #000;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

    transform: translate3d(-50%, -50%, 0);
    .close {
        position: absolute;
        right: 15px;
        top: -40px;
        width: 35px;
        height: 35px;
        background: url('@/assets/img/close.png') no-repeat;
        background-size: 100% 100%;
    }

    .title {
        position: relative;
        z-index: 2;
        padding: 70px 0 0 32px;
        height: 330px;
        font-family: 'Helvet';
        color: #2a2d3d;
        font-size: 80px;
        font-weight: 900;
        background: url('@/assets/img/dialog/sign/sign_bg.png') no-repeat left top;
        background-size: 706px 330px;

        span {
            font-size: 160px;
        }
        .steps-wrap {
            padding-top: 60px;
            padding-right: 70px;
            height: 120px;

            .bar {
                position: relative;
                height: 7px;
                border-radius: 60px;
                background: #161824;
            }
            .inner-bar {
                max-width: 100%;
                height: 7px;
                border-radius: 60px;
                width: 0;
                background: #fffd58;
            }
            .steps {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 7px;

                .step {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    width: 17px;
                    height: 17px;
                    background: #161824;
                    border-radius: 50%;

                    transform: translate3d(-50%, -50%, 0);

                    .step-minus {
                        position: absolute;
                        left: 50%;
                        bottom: -50px;
                        font-size: 24px;
                        font-weight: 400;
                        color: #9195a9;
                        transform: translateX(-50%);
                    }
                    .step-icon {
                        position: absolute;
                        top: -60px;
                        left: 50%;
                        width: 45px;
                        height: 45px;
                        transform: translateX(-50%);
                        pointer-events: none;

                        svg {
                            width: 100%;
                            height: 100%;
                        }
                        .sun {
                            opacity: 0;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            width: 72px;
                            height: 72px;
                            margin-left: -36px;
                            margin-top: -36px;
                            // transform: translate3d(-50%, -50%, 0);
                            z-index: -1;
                            animation: rotate 2.5s linear infinite;

                            svg {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    &.finished {
                        @apply flex justify-center items-center;
                        &::before {
                            display: inline-block;
                            content: '';
                            width: 13px;
                            height: 13px;
                            background: #fffd58;
                            border-radius: 50%;
                        }
                        .step-icon {
                            // position: relative;
                            pointer-events: auto;

                            .sun {
                                opacity: 1;
                            }
                        }
                    }
                    &.recived {
                        .step-icon {
                            top: -68px;
                            width: 57px;
                            height: 54px;
                            .sun {
                                opacity: 0;
                            }
                        }
                    }
                }
            }
        }
    }
    .content {
        position: relative;
        top: -103px;
        min-height: 400px;
        padding: 250px 24px 53px 16px;
        background: #2a2d3d;
        border-radius: 60px;

        .gift {
            @apply flex flex-wrap gap-[20px];

            .gift-item {
                @apply flex flex-col relative;
                margin-bottom: 10px;
                width: 150px;
                height: 193px;
                border-radius: 21px;
                background: #fff;

                .day-icon {
                    @apply flex justify-center;
                    position: absolute;
                    top: -12px;
                    left: -7px;
                    content: '';
                    width: 98px;
                    height: 74px;
                    line-height: 55px;
                    background: url('@/assets/img/dialog/sign/day.png') no-repeat;
                    background-size: 100% 100%;
                    z-index: 10;

                    font-family: 'Helvet';
                    font-size: 24px;
                    font-weight: 900;
                    color: #fff;
                    span {
                        margin-left: 9px;
                        line-height: 50px;
                        font-size: 32px;
                        color: #fffd58;
                    }
                }
                &:last-child {
                    width: 320px;
                    border-radius: 21px;
                }
                &.active {
                    background: var(--Linear, linear-gradient(90deg, #fffc7f 0%, #ffdc6d 100%));

                    .day-icon {
                        background-image: url('@/assets/img/dialog/sign/day1.png');
                    }
                    .pic-wrap {
                        .sun {
                            opacity: 1;
                            animation: rotate 2.5s linear infinite;
                        }
                    }
                }
                .pic-wrap {
                    @apply flex justify-center items-center relative flex-1;
                    padding-top: 20px;
                    img {
                        position: relative;
                        width: 120px;
                        // height: 100px;
                        z-index: 1;
                    }
                    .sun {
                        position: absolute;
                        width: 140px;
                        height: 140px;
                        opacity: 0;

                        svg {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .text {
                    color: #2a2d3d;
                    font-size: 20px;
                    font-weight: 700;
                    text-align: center;
                    span {
                        margin-left: 5px;
                        color: #ff6c00;
                    }
                }
                .mask {
                    display: none;
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 21px;
                    background: rgba(0, 0, 0, 0.6);
                    z-index: 8;

                    svg {
                        position: absolute;
                        right: 5px;
                        bottom: 12px;
                        width: 50px;
                        height: 50px;
                    }
                }
                &.claimed {
                    .mask {
                        display: block;
                    }
                }
            }
        }

        .btn {
            @apply flex justify-center items-center;
            margin: 53px auto 0;
            width: 512px;
            height: 96px;
            font-size: 48px;
            font-weight: 400;
            border-radius: 100px;
            background: var(--Linear, linear-gradient(90deg, #2aee88 0%, #9ae871 100%));

            &.disabled {
                background: #67686a;
                color: #fff;
                cursor: not-allowed;
            }
        }

        .question {
            position: absolute;
            right: 40px;
            bottom: 80px;
            width: 36px;
            height: 36px;

            svg {
                width: 100%;
                height: 100%;
                pointer-events: none;
            }
        }
        .question-tip {
            position: absolute;
            left: 50%;
            bottom: 0;
            padding: 15px 25px;
            width: 600px;
            min-height: 128px;
            border-radius: 30px 0px 30px 30px;
            background: #fff;
            color: #434445;
            text-align: center;
            font-family: 'Microsoft YaHei';
            font-size: 26px;
            font-weight: 400;
            transform: translate3D(-50%, 80%, 0);
        }
    }
}
</style>
