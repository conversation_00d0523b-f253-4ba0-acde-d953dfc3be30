<template>
    <div>
        <van-overlay :show="showSetting" z-index="3001">
            <div class="dialog" @click.stop>
                <div class="choose-item">
                    <div class="choose-tip">{{ $t('First_pop_up_language') }}</div>
                    <div class="choose-select" @click="showLang = true">
                        {{ store.languageList.find((item) => item.value === initChoose.lang)?.show }}
                    </div>
                </div>
                <div class="choose-item">
                    <div class="choose-tip">{{ $t('First_pop_up_currency') }}</div>
                    <div class="choose-select" @click="showCurrency = true">
                        <span class="font-bold">{{ currentCurrency.clientcurrency }} </span> {{ currentCurrency.currency }}
                    </div>
                </div>
                <div class="tip">{{ $t('First_pop_up_currency_info') }}</div>
                <div class="choose-btn" @click="submitSetting">{{ $t('Ok') }}</div>
            </div>
        </van-overlay>
        <Language v-model="showLang" @confirm="onConfirm" />
        <Currency v-model="showCurrency" @confirm="onSelect" :default="initChoose.currency" :type="2" />
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { initLangAndCurrency } from '@/api/home'
import { useBaseStore } from '@/stores'
import Language from '@/components/newHome/language.vue'
import Currency from '@/components/wallet/currencyDialog.vue'
import { setLocaleLang } from '@/language'

const store = useBaseStore()
const { userInfo, token } = storeToRefs(store)

const i18n = useI18n()

const showSetting = ref(false)
const showLang = ref(false)
const showCurrency = ref(false)

const initChoose = reactive({
    lang: i18n.locale.value,
    currency: store.defaultCurrency.currency,
})

const currentCurrency = computed(() => {
    console.log(store.currencyList, ' store.currencyList')
    console.log(initChoose.currency, 'initChoose.currency')
    return store.currencyList.find((item) => item.currency === initChoose.currency)
})

const langCofirm = computed(() => {
    return token.value && Object.keys(userInfo.value).length && (!userInfo.value?.guideLanguage || !userInfo.value?.guideCurrency)
})
watch(
    langCofirm,
    () => {
        if (langCofirm.value && !showSetting.value) {
            showSetting.value = true
        }
    },
    {
        immediate: true,
    }
)

// 语言选择
const onConfirm = (lang) => {
    initChoose.lang = lang
}
// 货币选择
const onSelect = (currency) => {
    initChoose.currency = currency
}
const reward = async () => {
    const res = await store.ReceiveRewards({
        id: 'bindtelephone',
    })
    if (res.code === 200) {
        store.getProfile()
    }
}

const submitSetting = () => {
    initLangAndCurrency({ language: initChoose.lang, currency: initChoose.currency }).then((res) => {
        if (res.code === 200) {
            store.setInit(res.data)
            // i18n.locale.value = initChoose.lang
            setLocaleLang(initChoose.lang)
            showSetting.value = false
            if (store.userInfo?.telephone) {
                reward()
            }
        }
    })
}
</script>
<style lang="scss" scoped>
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 766px;
    padding: 83px 50px 0;
    background: url('@/assets/img/dialog/language_set.png') no-repeat;
    background-size: 100% 100%;
    transform: translate3d(-50%, -50%, 0);

    .choose-item {
        padding-top: 65px;

        .choose-tip {
            font-family: MicrosoftYaHei-Bold;
            font-size: 36px;
            font-weight: bold;
            color: #fff;
        }
        .choose-select {
            position: relative;
            margin-top: 10px;
            width: 640px;
            height: 99px;
            padding-left: 24px;
            padding-right: 49px;
            line-height: 99px;
            border-radius: 20px;
            font-family: MicrosoftYaHei;
            font-size: 32px;
            background-color: #ffffff;

            &::after {
                position: absolute;
                top: 50%;
                right: 17px;
                content: '';
                width: 32px;
                height: 19px;

                transform: translateY(-50%);
                background: url('@/assets/img/dialog/arrow.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .tip {
        margin-top: 5px;
        font-family: MicrosoftYaHei;
        font-size: 24px;
        line-height: 43px;
        color: #75d7f0;
        text-align: center;
    }
    .choose-btn {
        margin-top: 42px;
        width: 610px;
        height: 104px;
        line-height: 104px;
        background-image: linear-gradient(150deg, #ffc44f 0%, #ffb944 40%, #ffad38 100%);
        border-radius: 52px;
        text-align: center;
        font-family: MicrosoftYaHei-Bold;
        font-size: 48px;
        font-weight: bold;
        color: #ffffff;
    }
}
</style>
