<template>
    <van-overlay :show="showed" :lock-scroll="lockScroll" v-bind="$attrs" @click="handleOverClose" z-index="3001">
        <div class="dialog" @click.stop>
            <div class="dialog-head">
                <div class="dialog-title">
                    <p>{{ $t(title) }}</p>
                </div>
                <div v-if="showClose" class="close-icon" @click="handleClose"></div>
            </div>
            <div class="dialog-content">
                <slot></slot>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup name="commonDialog">
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: () => false,
    },
    showClose: {
        type: Boolean,
        default: () => true,
    },
    lockScroll: {
        type: Boolean,
        default: () => false,
    },
    lockBodyScroll: {
        type: Boolean,
        default: () => true,
    },
    title: {
        type: String,
        default: () => '',
    },
    closeOnClickOverlay: {
        type: Boolean,
        default: () => true,
    },
})

const emits = defineEmits(['close', 'update:modelValue'])
const showed = defineModel()
const handleOverClose = () => {
    if (!props.closeOnClickOverlay) {
        return
    }
    handleClose()
}
const handleClose = () => {
    showed.value = false
    emits('close')
}

const bodyScrollType = computed(() => (props.lockBodyScroll ? 'hidden' : 'auto'))
</script>
<style scoped lang="scss">
.dialog {
    @apply flex flex-col;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 684px;
    padding: 20px;
    background: #09101d;
    border-radius: 30px;
    border: solid 2px #1c75b4;

    transform: translate3d(-50%, -50%, 0);
}
.dialog-head {
    display: flex;
    font-size: 30px;
    font-weight: bold;

    .dialog-title {
        flex: 1;
        p {
            width: 365px;
            margin: 0 auto;
            text-align: center;
            text-align: center;
            color: #fff;
        }
    }

    .close-icon {
        width: 34px;
        height: 34px;
        background: url('@/assets/img/wallets/close.png') no-repeat;
        background-size: 100% 100%;
    }
}
.dialog-content {
    flex: 1;
    overflow: v-bind(bodyScrollType);
}
</style>
