<template>
    <van-popup class="reseller-pop" v-model:show="show" position="bottom" close-on-popstate round :z-index="3001" @closed="handleClose">
        <div class="reseller">
            <div class="title">Reseller</div>
            <div class="reseller-content">
                <div class="reseller-list">
                    <div class="reseller-item" v-for="(item, index) in resellerList" :key="index">
                        <div class="reseller-idx">{{ index + 1 }}</div>
                        <div class="reseller-avatar">
                            <img :src="item.avatar" />
                            <div :class="['online-status', { active: item.online == 1 }]"></div>
                        </div>
                        <div class="reseller-name elipsis">{{ decodeName(item.nickname) }}</div>
                        <div class="reseller-btn" @click="goChart(item)">{{ $t('Recharge') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { decodeName } from '@/utils'
import { useRouter } from 'vue-router'
import { getResellers, getSelectreseller } from '@/api/wallet'
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'

const show = defineModel()

const router = useRouter()
const store = useBaseStore()
const i18n = useI18n()

const resellerList = ref([])
const handleClose = () => {}
const isSending = ref(false)

const goChart = async (item) => {
    if (isSending.value) {
        return
    }
    isSending.value = true
    try {
        const res = await getSelectreseller({
            uid: store.userInfo?.uid,
            reseller: item.uid,
        })
        if (res.code === 200 && res.channel) {
            router.push({
                path: '/chat',
                query: {
                    nickname: decodeName(item.nickname),
                    userID: item.uid,
                    isRs: 1,
                },
            })
        } else {
            showFailToast(i18n.t('error_info'))
        }
    } catch (e) {
        showFailToast(i18n.t('error_info'))
    }
    isSending.value = false
}
const getReseller = async () => {
    try {
        const res = await getResellers(store.userInfo?.uid)
        if (res.code === 200) {
            resellerList.value = res.resellers
        }
    } catch (e) {
        console.log(e)
    }
}
onMounted(() => {
    getReseller()
})
</script>
<style lang="scss" scoped>
.reseller-pop {
    margin-left: 20px;
    width: 710px;
    height: 70%;
    background-image: linear-gradient(#2a2d3d, #2a2d3d), linear-gradient(#2a2d3b, #2a2d3b);
    background-blend-mode: normal, normal;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    overflow-y: inherit;
    font-family: MicrosoftYaHei;
    color: #ffffff;
}
.reseller {
    @apply flex flex-col h-full;
    padding: 0 25px;

    .title {
        @apply flex items-center justify-center;
        margin: 0 auto;
        position: relative;
        top: -34px;
        width: 472px;
        height: 111px;
        padding-bottom: 12px;
        background: url('@/assets/img/wallets/bind_title.png') no-repeat;
        background-size: 100% 100%;
        font-size: 40px;
        font-weight: bold;
    }

    .reseller-content {
        height: calc(100% - 111px);
        padding-bottom: 52px;
        background-image: linear-gradient(#1c1b28, #1c1b28), linear-gradient(#2a2d3b, #2a2d3b);
        background-blend-mode: normal, normal;
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;

        .reseller-list {
            padding: 27px 26px 0 22px;
            height: 100%;
            overflow-y: scroll;

            .reseller-item {
                @apply flex items-center gap-[23px];
                height: 114px;

                .reseller-idx {
                    width: 40px;
                    font-size: 30px;
                }
                .reseller-avatar {
                    position: relative;
                    width: 88px;
                    height: 88px;
                    border-radius: 50%;
                    background-color: #000000;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 50%;
                    }
                    .online-status {
                        position: absolute;
                        right: 6px;
                        bottom: 0;
                        width: 16px;
                        height: 16px;
                        background: #939393;

                        border: 1px solid #fff;
                        border-radius: 50%;

                        &.active {
                            background: #10c014;
                        }
                    }
                }
                .reseller-name {
                    flex: 1;
                    font-size: 24px;
                }
                .reseller-btn {
                    @apply flex justify-center items-center;
                    width: 181px;
                    height: 52px;
                    background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#2d3343, #2d3343);
                    background-blend-mode: normal, normal;
                    border-radius: 23px;
                    font-size: 24px;
                }
            }
        }
    }
}
</style>
