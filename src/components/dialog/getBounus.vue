<template>
    <van-overlay :show="showed" v-bind="$attrs" :z-index="3000">
        <div class="dialog" @click.stop>
            <div class="title">{{ $t('congratulations') }}</div>
            <div v-if="isNotRecharged" class="close" @click="handleClose"></div>
            <div class="bonus"><img src="@/assets/img/dialog/bonus/bouns.png" /></div>
            <div
                class="tip"
                v-html="
                    $t('gift_bonus_1', {
                        current_type: store.cureencySymbol(),
                        number: store.bonus,
                    })
                "
            ></div>
            <div class="not-rechage" v-if="isNotRecharged" v-html="$t('bonus_to_cash_info')"></div>
            <div class="btn" @click="handleConfirm">{{ $t(isNotRecharged ? 'unlock_vip_1' : 'Claim') }}</div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import popupManager from '@/utils/PopupManager'
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/bus'

const store = useBaseStore()

const i18n = useI18n()

const showed = defineModel()
// 没充值
const isNotRecharged = computed(() => {
    return !store.vipInfo.recharge || store.vipInfo.rechargeMax !== 0
})

const handleConfirm = () => {
    showed.value = false
    store.bonus = 0
    if (isNotRecharged.value) {
        return eventBus.emit('activity', {
            param: 'firstpayment',
        })
    }
    eventBus.emit('bloomAnimate', { type: 2 })
    showSuccessToast(i18n.t('message_claim_success'))
    popupManager.checkQueue()
}
const handleClose = () => {
    showed.value = false
    store.bonus = 0
    eventBus.emit('bloomAnimate', { type: 2 })
    popupManager.checkQueue()
}

onMounted(() => {
    if (store.bonus) {
        showed.value = true
    } else {
        popupManager.checkQueue()
    }
})
</script>
<style lang="scss" scoped>
.dialog {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 750px;
    transform: translate3d(-50%, -50%, 0);
    font-family: MicrosoftYaHei;

    .title {
        font-size: 72px;
        font-weight: bold;
        line-height: 38px;
        text-align: center;
        color: #fff050;
    }
    .bonus {
        position: relative;
        margin: 65px auto 0;
        width: 231px;
        height: 250px;
        img {
            width: 100%;
            height: 100%;

            animation: bounce 1.5s linear infinite alternate;
        }

        &::before {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 525px;
            height: 518px;
            content: '';
            margin-left: -262.5px;
            margin-top: -235px;
            background: url('@/assets/img/dialog/welcome-gift/sun.png') no-repeat;
            background-size: 100% 100%;
            z-index: -1;
            animation: rotate 2.5s linear infinite;
        }
    }
    .tip {
        margin-bottom: 35px;
        font-size: 32px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        :deep(span) {
            color: #ffeb0c;
        }
    }
    .not-rechage {
        width: 412px;
        margin: 0 auto 35px;
        color: #fff050;
        text-align: center;
        font-family: 'Microsoft YaHei';
        font-size: 36px;
        font-style: normal;
        font-weight: 700;
        :deep(span) {
            color: #ff6c00;
        }
    }
    .btn {
        @apply flex justify-center items-center;
        margin: 0 auto;
        width: 565px;
        height: 92px;
        background-image: linear-gradient(90deg, #26f087 0%, #62ec7c 40%, #9de871 100%);
        border-radius: 38px;
        font-size: 40px;
        color: #000000;
        font-weight: 600;
    }
    .close {
        position: absolute;
        top: -80px;
        right: 90px;
        width: 44px;
        height: 44px;
        background: url('@/assets/img/close.png') no-repeat;
        background-size: 100% 100%;
    }
}
@keyframes rotate {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}
@keyframes bounce {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}
</style>
