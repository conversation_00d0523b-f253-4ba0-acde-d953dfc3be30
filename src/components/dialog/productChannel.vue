<template>
    <div>
        <van-popup class="channel-pop" v-model:show="show" position="bottom" closeable close-on-popstate round :z-index="3001" @closed="handleClose">
            <div class="channel">
                <div class="channel-top">
                    <p class="channel-title">{{ $t('wallet_total_amount') }}</p>
                    <p
                        class="channel-acount"
                        v-html="
                            $t(getBonus ? 'wallet_detail_info1' : 'wallet_detail_info2', {
                                current_type: store.cureencySymbol(),
                                current_number1: product.cny + (product.gifts?.gold || 0),
                                current_number2: getBonus,
                            })
                        "
                    ></p>
                </div>
                <div class="channel-area">
                    <div class="channel-title">{{ $t('Wallet_deposit_method') }}</div>
                    <div class="channel-list">
                        <div
                            :class="['channel-item', { cantChoose: item.status === 0 }, { active: chooseChl === item?.channel }]"
                            v-for="item in takeEffectChannel"
                            :key="item?.channel"
                            @click="changeChannel(item)"
                        >
                            <div class="channel-left">
                                <div class="pay-wrap">
                                    <img :src="item.payicon" />
                                </div>
                                <div v-if="$t(item?.channel)" class="icon-wrap">
                                    <img :src="item.icon" />
                                    <p class="channel-text" v-html="$t(item?.channel)"></p>
                                </div>
                            </div>
                            <div class="channel-right">
                                <div v-if="item.status === 0" class="maintenance"></div>
                                <div class="channel-check" v-else></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="channel-btn" @click="confirm">{{ $t('Deposit') }} {{ store.cureencySymbol() + product.cny }}</div>
            </div>
        </van-popup>
        <van-overlay class="flex flex-col items-center justify-center" :show="showLoading" :z-index="6000">
            <div><van-loading type="spinner" /></div>
            <div class="loading-tip">{{ $t('Order_is_being_gener') }}</div>
        </van-overlay>
        <Reseller v-if="showReseller" v-model="showReseller" />
    </div>
</template>
<script setup lang="ts">
import { getAllChannels, createPaysoOrder } from '@/api/wallet'
import { useBaseStore } from '@/stores'
import { getDeviceInfo } from '@/utils'
import webApp from '@twa-dev/sdk'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import popupManager from '@/utils/PopupManager'
import { useI18n } from 'vue-i18n'
import Reseller from '@/components/dialog/reseller.vue'
import { Log } from '@/api/log'

const store = useBaseStore()
const router = useRouter()

const show = defineModel()
const emit = defineEmits(['recharged', 'sendChannel'])
const i18n = useI18n()

const chooseChl = ref('')
const showLoading = ref(false)
const orderParams = inject('orderParams')
const showReseller = ref(false)

const product = computed(() => orderParams.item || {})
const vipProduct = computed(() => {
    return orderParams.source === 'wallet' && store.curVipConfig?.depositBonus
})

const takeEffectChannel = computed(() => {
    const id = product.value?.id
    return id ? store.depositeChannel.filter((item) => item.productList.includes(id)) : store.depositeChannel
})

const uid = computed(() => {
    return store.userInfo.uid
})
const getBonus = computed(() => {
    let bonus = product.value?.gifts.bonus || 0
    const cny = product.value?.cny
    if (vipProduct.value) {
        bonus = bonus + Number((cny * store.curVipConfig?.depositBonus).toFixed(4))
    }
    return bonus || 0
})
const isDialog = computed(() => {
    return ['onlinesign', 'firstpayment'].includes(orderParams.source)
})

watch(show, () => {
    show.value && getChannel()
})
watch(takeEffectChannel, () => {
    if (!takeEffectChannel.value.some((item) => item.channel == chooseChl.value)) {
        chooseChl.value = takeEffectChannel.value[0]?.channel
    }
})
watch(
    () => store.wallet?.currency,
    (oldVal, newVal) => {
        if (oldVal === newVal) {
            return
        }
        getChannel()
    }
)

const getChannel = async () => {
    if (!uid.value || !store.token) {
        return
    }
    const res = await getAllChannels(uid.value, false)
    if (res.code === 200) {
        store.depositeChannel = store.userInfo?.isReseller ? res.result.filter((item) => item.channel !== 'reseller') : res.result
        emit('sendChannel', res.result)
    }
}

const changeChannel = (item) => {
    if (item.status === 0) {
        return
    }
    chooseChl.value = item?.channel
}
const confirm = async () => {
    if (takeEffectChannel.value.every((item) => item.status === 0)) {
        showFailToast({
            message: i18n.t('wallet_deposit_tier_unavailable'),
            className: 'tip-error',
        })
        return
    }
    if (chooseChl.value === 'reseller') {
        return (showReseller.value = true)
    }
    if (orderParams.source === 'onlinesign') {
        Log({
            event: 'onlinesign_pay',
        })
    }

    showLoading.value = true
    try {
        const order = await createPaysoOrder({
            uid: store.userInfo.uid,
            productId: product.value.id,
            channel: chooseChl.value,
            osType: getDeviceInfo().os,
            tradeType: 'H5',
            redirectUrl: location.href,
            source: product.value?.usefor || '',
            bflowmult: !!orderParams?.bflowmult,
        })
        if (order.code === 200) {
            const url = order.body.guideUrl
            if (isDialog) {
                store.depositOrder = order.body.orderId
            }

            if (chooseChl.value === 'wkqrpay') {
                router.push({
                    path: '/payGuide',
                    query: {
                        u: encodeURIComponent(url),
                        coin: product.value.cny,
                        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    },
                })
                show.value = false
                if (!isDialog) {
                    popupManager.checkQueue()
                }
                return
            }
            if (store.isTg) {
                if (url) {
                    webApp.openLink(url)
                }
                show.value = false
                emit('recharged')
                if (!isDialog) {
                    popupManager.checkQueue()
                }
                return
            }
            if (url) {
                // location.href = url
                showDialog({
                    title: i18n.t('Confirmation'),
                    message: i18n.t('wallet_deposit_confirm'),
                    confirmButtonText: 'Go',
                }).then(() => {
                    // ✅ 同步跳转（通常不会被拦截）
                    // window.location.href = url
                    window.open(url, '_blank')
                })
            }
            show.value = false
            handleClose()
            emit('recharged')
            if (!isDialog) {
                popupManager.checkQueue()
            }
        }
    } catch (e) {
        console.log(e)
    } finally {
        showLoading.value = false
    }
}
const handleClose = () => {
    if (!isDialog) {
        popupManager.checkQueue()
    }
}

onBeforeMount(() => {
    getChannel()
})
</script>
<style lang="scss" scoped>
.channel-pop {
    margin-left: 20px;
    width: 710px;
    height: 1020px;
    padding: 0 30px;
    background-color: #2a2d3d;
    font-family: MicrosoftYaHei;
    color: #ffffff;

    .channel {
        .channel-top {
            padding-bottom: 20px;

            .channel-title {
                margin: 40px 0 15px;
                line-height: 30px;
                font-size: 36px;
                font-weight: bold;
                font-weight: 700;
                text-align: center;
            }
            .channel-acount {
                color: #fff;
                font-family: 'Microsoft YaHei';
                font-size: 32px;
                font-style: normal;
                font-weight: 600;
                text-align: center;
                line-height: 80px;

                :deep(span) {
                    color: #10c580;
                }
            }
        }
        .channel-area {
            padding-top: 40px;
            height: 600px;
            background-color: #1c1b28;
            border-radius: 10px;
            overflow-y: auto;
            .channel-title {
                @apply flex items-center justify-center gap-[20px];
                margin-bottom: 23px;
                font-size: 30px;
                text-align: center;

                &::before,
                &::after {
                    content: '';
                    display: inline-block;
                    width: 160px;
                    height: 4px;
                    background: url('@/assets/img/wallets/channel_line.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            .channel-list {
                padding: 0 20px;
                .channel-item {
                    display: flex;
                    margin-bottom: 30px;
                    height: 100px;
                    background-color: #3e4456;
                    border-radius: 20px;

                    .channel-left {
                        position: relative;
                        display: flex;
                        align-items: center;
                        flex: 1;

                        .pay-wrap {
                            @apply flex items-center;
                            padding-left: 18px;
                            margin-right: 10px;
                            width: 286px;
                            height: 100%;

                            img {
                                height: 60px;
                            }
                        }
                        .icon-wrap {
                            flex: 1;
                            .channel-text {
                                position: absolute;
                                left: 290px;
                                top: 50%;
                                padding-left: 18px;
                                font-size: 28px;
                                line-height: 30px;
                                color: #ffffff;

                                transform: translateY(-50%);
                            }
                            img {
                                height: 60px;
                            }
                        }
                    }
                    &.cantChoose {
                        .channel-left {
                            opacity: 0.3;
                        }
                    }
                    .channel-right {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 68px;
                        padding-right: 20px;

                        .maintenance {
                            width: 100%;
                            height: 48px;

                            background: url('@/assets/img/wallets/maintenance.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .channel-check {
                            width: 36px;
                            height: 36px;
                            background-color: #2a2d3d;
                            border-radius: 50%;
                        }
                    }

                    &.active {
                        .channel-check {
                            position: relative;
                            background-color: #10c580;

                            &::before {
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                content: '';
                                width: 27px;
                                height: 21px;
                                transform: translate3d(-50%, -50%, 0);
                                background: url('@/assets/img/wallets/channel_active.png') no-repeat;
                                background-size: 100% 100%;
                            }
                        }
                    }
                }
            }
        }
        .channel-btn {
            margin: 50px auto 0;
            width: 500px;
            height: 92px;
            line-height: 92px;
            border-radius: 100px;
            background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%);
            font-size: 36px;
            text-align: center;
            color: #000;
            font-size: 44px;
            font-weight: 600;
            font-family: 'Microsoft YaHei';
        }
    }
}
.loading-tip {
    margin-top: 10px;
    font-size: 27px;
    color: #fff;
}
</style>
<style lang="scss">
.tip-error {
    transform: translateY(100%) !important;
}
</style>
