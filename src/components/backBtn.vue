<template>
    <div class="back" @click="router.back()"></div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
</script>
<style scoped lang="scss">
.back {
    min-width: 60px;
    position: absolute;
    top: calc(50px + var(--safe-height) + constant(safe-area-inset-top));
    top: calc(50px + var(--safe-height) + env(safe-area-inset-top));
    left: 30px;
    width: 40px;
    height: 40px;
    background: url('@/assets/img/header/back.png') no-repeat center center;
    background-size: 22px 40px;
    z-index: 300;
}
</style>
