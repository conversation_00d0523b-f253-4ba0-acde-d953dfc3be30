<template>
    <div @touchmove.stop @click.stop>
        <section class="series">
            <div class="series-name">
                <div class="name" @click="showSeriasIdx">
                    <span class="elipsis">{{
                        $t('reels_video_name', { video_name: item.name, number1: item.serialindex, number2: item.maxsize })
                    }}</span>
                </div>
            </div>
            <div v-if="item.maxsize !== item.serialindex" class="series-next-btn" @click="changeNext">{{ $t('reels_next_ep') }}</div>
        </section>
        <van-popup class="series-popup" v-model:show="show" position="bottom">
            <div class="popup-content">
                <div class="title">
                    {{ item.name }}
                    <div class="close" @click="show = false"></div>
                </div>
                <div class="pop-tab-wrap">
                    <div class="tab">
                        <div
                            v-for="(tab, index) in tabs"
                            :key="index"
                            class="tab-item"
                            :class="{ active: index === currentTabIndex }"
                            @click="selectTab(index)"
                        >
                            {{ tab.label }}
                        </div>
                    </div>
                    <div class="tab-content">
                        <ul class="series-list">
                            <li
                                class="series-number"
                                v-for="episode in currentTabEpisodes"
                                :key="episode"
                                :class="{ 'current-episode': episode === current, lock: item.paymin <= episode && checnkLocked(episode) }"
                                @click="changeSerias(episode)"
                            >
                                {{ episode }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="tsx">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
import * as api from '@/api/video'
import { useRouter } from 'vue-router'
import { checkLogin } from '@/utils'

const props = defineProps({
    min: {
        type: Number,
        default: 1,
    },
    item: {
        type: Object,
        default: () => {
            return {}
        },
    },
    isActive: {
        type: Boolean,
        default: () => {
            return false
        },
    },
})
const emits = defineEmits(['seriesChange'])
const i18n = useI18n()
const store = useBaseStore()
const router = useRouter()

const unlocklist = ref([])

const show = defineModel()
const division = 30
const current = ref(props.item.serialindex || 1)

const tabs = computed(() => {
    const tabList = []
    for (let start = props.min; start <= props.item.maxsize; start += division) {
        const end = Math.min(start + division - 1, props.item.maxsize)
        tabList.push({ label: ` ${start}-${end}`, start, end })
    }
    return tabList
})

watch(
    () => props.isActive,
    (val) => {
        if (val) {
            getVideoPayByDrama()
        }
    }
)
const currentTabIndex = ref(0)
const checnkLocked = (episode) => {
    const page = Math.ceil(episode / 30) - 1

    if (props.item.paymin <= episode && !(unlocklist.value[page] & (1 << (episode - 30 * page)))) {
        return true
    }
    return false
}

const changeNext = () => {
    checkLogin().then(() => {
        if (checnkLocked(current.value + 1)) {
            unLock(current.value + 1)
            return
        }

        current.value = current.value + 1
        getVideoDetail(current.value)
    })
}
const showSeriasIdx = () => {
    checkLogin().then(() => {
        show.value = true
    })
}

const currentTabEpisodes = computed(() => {
    const currentTab = tabs.value[currentTabIndex.value]
    const episodes = []
    for (let i = currentTab.start; i <= currentTab.end; i++) {
        episodes.push(i)
    }
    return episodes
})

function selectTab(index: number) {
    currentTabIndex.value = index
}
const changeSerias = (episode: number) => {
    if (checnkLocked(episode)) {
        unLock(episode)
        return
    }

    current.value = episode
    getVideoDetail(episode)
}
const unLock = (episode) => {
    getVideoPayAmountByDrama(episode).then((res) => {
        if (res.code === 200) {
            const isLast = res.amount === res.all
            const i18nParam = {
                current_type: store.curSymbol[store.wallet?.currency],
            }
            if (isLast) {
                i18nParam['current_number'] = res.amount
            } else {
                i18nParam['current_number1'] = res.amount
                i18nParam['current_number2'] = res.all
            }
            showDialog({
                title: <div class="close" onClick={() => closeDialog()}></div>,
                message: i18n.t(isLast ? 'reels_unlock_one' : 'reels_unlock_all', i18nParam),
                className: 'common-dialog video-lock',
                showCancelButton: !isLast,
                confirmButtonText: i18n.t(isLast ? 'Confirm' : 'reels_all'),
                cancelButtonText: i18n.t('reels_current'),
                closeOnClickOverlay: true,
                allowHtml: true,
            })
                .then(() => {
                    console.log(isLast, 'hjkl;')
                    payVideoPayDrama(isLast ? episode : 0).then((code) => {
                        if (code === 200) {
                            getVideoDetail(episode)
                        }
                    })
                })
                .catch(() => {
                    if (!isLast) {
                        payVideoPayDrama(episode).then((code) => {
                            if (code === 200) {
                                getVideoDetail(episode)
                            }
                        })
                    }
                })
        }
    })
}
const getVideoDetail = async (index) => {
    try {
        const res = await api.getVideoDramaIndex({
            dramaid: props.item.dramaid,
            index,
        })
        if (res.code === 200) {
            show.value = false
            emits('seriesChange', {
                video: Object.assign({}, res.video || {}),
                self: {
                    [res.video.id]: res.self,
                },
            })
        }
    } catch (e) {
        console.log(e)
    }
}

const getVideoPayByDrama = async () => {
    try {
        const res = await api.getVideoPayByDrama({
            dramaid: props.item.dramaid,
        })
        if (res.code === 200) {
            unlocklist.value = res.unlock
        }
    } catch (e) {
        console.log(e)
    }
}
const getVideoPayAmountByDrama = (index) => {
    return api.getVideoPayAmountByDrama({
        dramaid: props.item.dramaid,
        index,
    })
}
const payVideoPayDrama = (index) => {
    return api
        .payVideoPayDrama({
            dramaid: props.item.dramaid,
            index,
        })
        .then((res) => {
            if (res.code === 403) {
                showToast(i18n.t('reels_balance_low'))
                router.push({
                    path: '/wallets',
                    query: {
                        back: 1,
                    },
                })
            }
            return res.code
        })
}

onMounted(() => {
    // 初始化时根据 current 找到对应的 tab
    const initialTabIndex = tabs.value.findIndex((tab) => current.value >= tab.start && current.value <= tab.end)
    if (initialTabIndex !== -1) {
        currentTabIndex.value = initialTabIndex
    }
})
</script>
<style lang="scss" scoped>
.series {
    @apply flex justify-between items-center;
    margin-bottom: 18px;
    height: 44px;
    font-size: 22px;
    font-family: MicrosoftYaHei;
    color: #fff;

    .series-name {
        .name {
            @apply flex items-center;
            max-width: 500px;
            margin-right: 30px;
            padding: 0 14px;
            height: 44px;
            line-height: 44px;
            border-radius: 22px;
            background: rgba(255, 255, 255, 0.15);

            &::before {
                margin-right: 7px;
                display: inline-block;
                content: '';
                width: 26px;
                height: 24px;
                background: url('@/assets/img/video/expand.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .series-next-btn {
        @apply flex items-center;
        min-width: 161px;
        height: 44px;
        padding: 0 11px 0 20px;
        border-radius: 22px;
        background: rgba(255, 255, 255, 0.15);

        &::after {
            margin-left: 9px;
            display: inline-block;
            content: '';
            width: 26px;
            height: 24px;
            background: url('@/assets/img/video/next.png') no-repeat;
            background-size: 100% 100%;
        }
    }
}
.van-overlay {
    background: rgba(0, 0, 0, 0);
}
.series-popup {
    padding: 43px 35px 33px;
    background: linear-gradient(#1c1b28, #1c1b28), linear-gradient(#2a2d3d, #2a2d3d);
    border-radius: 40px 40px 0 0;
    font-family: MicrosoftYaHei;
    color: #fff;

    .popup-content {
        .title {
            position: relative;
            margin-bottom: 29px;
            font-size: 26px;
            font-weight: bold;
        }
        .pop-tab-wrap {
            .tab {
                display: flex;
                .tab-item {
                    position: relative;
                    margin-right: 36px;
                    font-size: 24px;
                    color: #668292;
                    // color: #2fccff;
                    font-weight: bold;

                    &::before,
                    &::after {
                        position: absolute;
                        content: '';
                    }
                    &::after {
                        top: 50%;
                        right: -18px;
                        width: 3px;
                        height: 19px;
                        background: rgba(255, 255, 255, 0.35);

                        transform: translateY(-50%);
                    }
                    &.active {
                        color: #2fccff; /* 高亮选中的 tab */

                        &::before {
                            position: absolute;
                            bottom: 0;
                            left: 50%;
                            width: 50px;
                            height: 2px;
                            background: linear-gradient(#2fccff, #2fccff), linear-gradient(#000000, #000000);
                            border-radius: 1px;
                            transform: translateX(-50%);
                        }
                    }
                    &:last-child {
                        &::after {
                            display: none;
                        }
                    }
                }
            }
            .tab-content {
                margin-top: 20px;

                .series-list {
                    @apply flex flex-wrap gap-[12px];

                    .series-number {
                        @apply flex justify-center items-center;
                        position: relative;
                        width: 101px;
                        height: 72px;
                        background-color: #000000;
                        border-radius: 15px;
                        font-size: 28px;
                        font-weight: bold;
                        &.current-episode {
                            color: #2fccff; /* 高亮当前级数 */
                        }
                        &.lock {
                            &::after {
                                position: absolute;
                                top: 5px;
                                right: 5px;
                                content: '';
                                width: 18px;
                                height: 23px;
                                background: url('@/assets/img/video/lock.png') no-repeat;
                                background-size: 100% 100%;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
<style lang="scss">
.video-lock .van-dialog__header {
    height: 0px;
    padding: 0;
}
</style>
