<template>
    <video
        :class="['video-dom player', { 'small-video': isMorQQ }]"
        :poster="item.videopic"
        ref="videoEl"
        x5-video-player-type="h5"
        :x5-video-player-fullscreen="false"
        controls360="no"
        raw-controls
        :webkit-playsinline="true"
        :x5-playsinline="true"
        :playsinline="true"
        :fullscreen="false"
        :controls="false"
        :loop="true"
        hardware-accelerated
        :id="`player${position.index}`"
    >
        <p>{{ $t('您的浏览器不支持视频。') }}</p>
        <!-- preload="metadata" -->
    </video>
    <van-loading v-if="loading" class="video-loading" type="spinner" size="30" />
    <teleport v-if="showTel" :to="`#bar-wrap${position.index}`">
        <div class="proggres-container" :id="`#proggres-container${props.position.index}`"></div>
    </teleport>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
// import { isMOrQQBrowser } from '@/utils'
// import '@videojs/http-streaming'
// import videojs from 'video.js'
import { loadVideoJsOnce } from '@/utils/useVideoJsLoader'
import CryptoJS from 'crypto-js'
import { Log } from '@/api/log'
import { SlideItemPlayStatus } from '@/enums'

const props = defineProps({
    item: {
        type: Object,
        default: () => {
            return {}
        },
    },
    autoplay: {
        type: Boolean,
        default: () => false,
    },
    position: {
        type: Object,
        default: () => {
            return {
                index: 0,
            }
        },
    },
    isActive: {
        type: Boolean,
        default: () => {
            return false
        },
    },
    state: {
        type: Object,
        default: () => {
            return {}
        },
    },
})

const store = useBaseStore()

const isMorQQ = ref(false)
const playerIns = ref(null)
const showTel = ref(false)
const loading = ref(true)
let retryCount = 0
const isPlaying = computed(() => {
    return props.isActive && props.state.status === SlideItemPlayStatus.Play
})
const videoEl = ref(null)

watch(
    () => isPlaying.value,
    (val) => {
        if (!val) {
            !playerIns.value?.paused() && playerIns.value.pause()
        } else {
            playerIns.value?.paused() && playerIns.value.play()
        }
    }
)

const sourceUrl = computed(() => {
    const { videolink, serialindex } = props.item
    if (videolink.includes('.mp4')) {
        return [
            {
                src: videolink,
                type: 'video/mp4',
            },
        ]
    }
    const linkList = videolink.split('/')
    const idx = linkList[linkList.length - 1]
    const name = CryptoJS.MD5(CryptoJS.enc.Utf8.parse(`${idx}_${serialindex}`)).toString(CryptoJS.enc.Hex)
    return [
        {
            src: `${videolink}/${name}/dash/stream.mpd`,
            type: 'application/dash+xml',
        },
        {
            src: `${videolink}/${name}/hls/master.m3u8`,
            type: 'application/x-mpegURL',
        },
        {
            src: `${videolink}/${name}/mp4/master.mp4`,
            type: 'video/mp4',
        },
    ]
})

watch(
    () => store.isMuted,
    () => {
        playerIns.value?.muted(store.isMuted)
    }
)
// watch(
//     () => sourceUrl.value,
//     () => {
//         if (playerIns.value) {
//             playerIns.value.pause()
//             console.log(sourceUrl.value, 'sourceUrl.value')
//             playerIns.value.reset() // 清除海报、字幕等状态

//             playerIns.value.src(sourceUrl.value[0])
//             playerIns.value.ready(() => {
//                 console.log('readyreadyready')
//                 nextTick(() => {
//                     playerIns.value.play()
//                 })
//             })

//             // console.log(playerIns.value, 'playerIns.value', playerIns.value.paused())
//             // playerIns.value.reset()
//             // playerIns.value.play()
//             // initVideo()
//         }
//     }
// )
const initVideo = async () => {
    dispose()
    let isPlayReport = false
    let isPlayEnd = false
    try {
        const videojs = await loadVideoJsOnce()
        const player = (playerIns.value = videojs(`player${props.position.index}`, {
            controls: true,
            muted: store.isMuted,
            html5: {
                vhs: {
                    overrideNative: true,
                    withCredentials: false,
                    limitRenditionByPlayerDimensions: true,
                    maxPlaylistRetries: 3,
                    playlistExclusionDuration: 10,
                    plugins: {
                        httpSourceSelector: { default: 'auto' },
                    },
                },
            },
            controlBar: {
                children: {
                    playToggle: false,
                    volumePanel: false,
                    currentTimeDisplay: false,
                    timeDivider: false,
                    durationDisplay: false,
                    progressControl: true,
                    remainingTimeDisplay: false,
                    customControlSpacer: false,
                    chaptersButton: false,
                    subtitlesButton: false,
                    captionsButton: false,
                    fullscreenToggle: false,
                },
            },
            sources: sourceUrl.value,
        }))
        const progressControl = player.getChild('ControlBar').getChild('progressControl')
        nextTick(() => {
            const bar = document.getElementById(`#proggres-container${props.position.index}`)
            const progressEl = progressControl.el()
            bar.appendChild(progressEl)
        })

        player.on('ready', () => {
            if (isPlaying.value) {
                player.play().catch((e) => {
                    if (e.message === 'NotAllowedError') {
                        isPlaying.value && player.play()
                    }
                })
            }
            player.tech().on('retryplaylist', () => {
                retryCount++
                console.log(retryCount > 3 && loading.value, 1)
                if (retryCount > 3 && loading.value) {
                    const duration = player.duration()
                    const current = player.currentTime()
                    if (duration - current < 3) {
                        console.log(duration - current < 3, 2)
                        player.currentTime(player.duration())
                    }
                    if (loading.value && player.currentTime() === player.duration()) {
                        console.log(loading.value && player.currentTime() === player.duration() < 3, 3)
                        player.currentTime(0)
                        retryCount = 0
                        return
                    }
                }
            })
        })

        // 错误处理：自动切换备用源
        player.on('error', (event) => {
            console.log(event, '2')
        })
        // player.on('pause', () => {
        //     loading.value = false
        //     console.log('paused video')
        //     // emits('paused')
        // })
        player.on('canplay', () => {
            loading.value = false
            if (isPlaying.value && player.paused()) {
                player.play()
            }
            // if (!isPlaying.value && !player.paused()) {
            //     player.pause()
            // }
            // else {
            //     //
            // }
        })
        // player.on('canplaythrough', () => {
        //     console.log('视频可以流畅播放')
        //     loading.value = false
        // })
        // player.on('play', () => {
        //     loading.value = false
        // })
        player.on('waiting', (e) => {
            console.log('waiting', e, player.currentTime(), player.duration())
            loading.value = true
        })
        player.on('playing', () => {
            loading.value = false
            console.log('playing')
        })
        player.on('timeupdate', () => {
            // 播放次数
            if (!isPlayReport && player.currentTime() > 5) {
                isPlayReport = true
                Log({
                    logname: 'videoplay',
                    event: 'play-count',
                    content: {
                        id: props.item.id,
                        uid: store.userInfo.uid,
                    },
                })
            }
            // 完播
            if (!isPlayEnd && player.duration()) {
                const percent = player.currentTime() / player.duration()
                if (percent >= 0.9) {
                    isPlayEnd = true
                    reportPlayEnd(percent)
                }
            }
        })
    } catch (e) {
        console.log(e)
    }
}
function reportPlayEnd(progress) {
    Log({
        logname: 'videoplay',
        event: 'play-end',
        content: {
            id: props.item.id,
            uid: store.userInfo.uid,
            progress,
            isEnd: true,
        },
    })
}
const dispose = () => {
    if (playerIns.value) {
        playerIns.value.pause()
        playerIns.value.dispose()
        playerIns.value = null
    }
}
defineExpose({
    play: () => {
        return playerIns.value?.play() || Promise.resolve()
    },
    pause: () => {
        return playerIns.value?.pause() || Promise.resolve()
    },
    inst: playerIns.value,
})
onMounted(() => {
    nextTick(() => {
        showTel.value = true
        initVideo()
    })
})
onBeforeUnmount(() => {
    dispose()
})
</script>
<style lang="scss" scoped>
.player {
    width: 100%;
    height: 100%;
    :deep(div) {
        display: none !important;
    }
    :deep(button) {
        display: none !important;
    }
    video {
        width: 100%;
        height: 100%;
    }
}
/* 自定义进度条容器样式 */
.proggres-container {
    width: 686px;
    height: 6px;
    // margin-top: 10px;
    // position: relative;

    :deep(.vjs-control-text) {
        display: none;
    }
    :deep(.vjs-progress-control) {
        width: 686px;
        height: 6px;
        background: rgba(255, 255, 255, 0.25);
        border-radius: 2px;
    }
    /* 覆盖默认进度条样式 */
    :deep(.vjs-progress-holder) {
        margin: 0;
        height: 100%;
    }
    :deep(.vjs-play-progress) {
        position: relative;
        height: 100%;
        border-radius: 2px;
        background-color: #fff;

        &::after {
            content: '';
            position: absolute;
            top: -15px;
            right: -16px;
            display: inline-block;
            width: 36px;
            height: 36px;
            background: red;
            background: url('@/assets/img/video/bar-btn.png') no-repeat;
            background-size: 100% 100%;
            z-index: 1;
        }
    }
}
</style>
