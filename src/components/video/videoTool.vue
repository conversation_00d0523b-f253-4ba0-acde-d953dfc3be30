<template>
    <div class="tool" @pointerup.stop @click.stop>
        <!-- <div class="tool-item tool-avatar"><img :src="item.conf.picture" /></div> -->
        <div class="tool-item tool-thumb" @click="hanldeThumb">
            <div :class="['tool-icon', { active: config?.like }]">
                <img v-if="!config?.like" class="icon" src="@/assets/img/foru/thumb.png" />
                <img v-else class="icon icon-active" src="@/assets/img/video/thumb_a.png" />
            </div>
            <p class="num">{{ dramaConfig?.like }}</p>
        </div>
        <div class="tool-item tool-like" @click="handleFavorite">
            <div :class="['tool-icon', { active: config?.favorite }]">
                <img v-if="!config?.favorite" class="icon" src="@/assets/img/foru/like.png" />
                <img v-else class="icon icon-active" src="@/assets/img/video/like_a.png" />
            </div>
            <p class="num">{{ dramaConfig.favorite }}</p>
        </div>
        <div class="tool-item tool-share" @click="checkShare">
            <div class="tool-icon"><img class="icon" src="@/assets/img/foru/share.png" /></div>
            <p class="num">{{ dramaConfig?.retweet }}</p>
        </div>
        <div v-if="item.type === 2" class="tool-item tool-serias" @click="goVideoDetail">
            <div class="tool-icon"><img class="icon" src="@/assets/img/video/expand.png" /></div>
        </div>
        <ShareDialog v-model="showShare" @shared="handleShared" />
    </div>
</template>
<script setup lang="ts" name="video-tool">
import * as service from '@/api'
import bus, { EVENT_KEY } from '@/utils/bus'
import { checkLogin } from '@/utils'
import ShareDialog from '@/components/dialog/shareDialog.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'

// store.dramaStatus

const props = defineProps({
    item: {
        type: Object,
        default: () => {
            return {}
        },
    },
    position: {
        type: Object,
        default: () => {
            return {}
        },
    },
    videoConfig: {
        type: Object,
        default: () => {
            return {}
        },
    },
})
const emits = defineEmits(['showSeries'])

const store = useBaseStore()
const { dramaStatus } = storeToRefs(store)

const dramaConfig = computed(() => {
    const { type, dramaid, like, favorite, retweet } = props.item
    if (type !== 2) {
        return props.item
    }
    if (!store.dramaStatus[dramaid]) {
        store.dramaStatus[dramaid] = { like, favorite, retweet }
    }

    return store.dramaStatus[dramaid]
})

const config = computed(() => {
    const { dramaid, id } = props.item
    const item = props.videoConfig[id]
    return (props.videoConfig[dramaid] || item) as { like: number; favorite: number }
})
const showShare = ref(false)
const thumbing = ref(false)
const favoriting = ref(false)
// 点赞
const hanldeThumb = async () => {
    checkLogin().then(async () => {
        if (thumbing.value) {
            return
        }
        thumbing.value = true
        const like = Boolean(config.value.like)
        const id = props.item.id
        try {
            const res = await service.doThumb({ id, like: !like })
            if (res.code === 200) {
                bus.emit(EVENT_KEY.TOOL_CLICK_BROADCAST, {
                    key: 'like',
                    flag: Number(!like),
                    id,
                    index: props.position.index,
                    type: props.item.type,
                    dramaid: props.item.dramaid,
                })
            }
        } catch (e) {
            console.log(e)
        }
        thumbing.value = false
    })
}
const handleFavorite = async () => {
    checkLogin().then(async () => {
        if (favoriting.value) {
            return
        }
        favoriting.value = true
        const favorite = Boolean(config.value.favorite)
        const id = props.item.id
        try {
            const res = await service.doFavorite({ id, favorite: !favorite })
            if (res.code === 200) {
                bus.emit(EVENT_KEY.TOOL_CLICK_BROADCAST, {
                    key: 'favorite',
                    flag: Number(!favorite),
                    id,
                    index: props.position.index,
                    type: props.item.type,
                    dramaid: props.item.dramaid,
                })
            }
        } catch (e) {
            console.log(e)
        }
        favoriting.value = false
    })
}
const checkShare = () => {
    checkLogin().then(() => {
        showShare.value = true
    })
}
const goVideoDetail = () => {
    checkLogin().then(() => {
        emits('showSeries')
    })
}
const handleShared = () => {
    const id = props.item.id
    service.doRetweet(id)
    bus.emit(EVENT_KEY.TOOL_CLICK_BROADCAST, {
        key: 'retweet',
        flag: true,
        id,
        index: props.position.index,
        type: props.item.type,
        dramaid: props.item.dramaid,
    })
}
function click({ uniqueId, index, type }) {
    if (props.position.uniqueId === uniqueId && props.position.index === index) {
        if (type === EVENT_KEY.ITEM_LIKE) {
            if (!config.value.like) {
                hanldeThumb()
            }
        }
    }
}
onMounted(() => {
    bus.on(EVENT_KEY.SINGLE_CLICK_BROADCAST, click)
})
onBeforeMount(() => {
    bus.off(EVENT_KEY.SINGLE_CLICK_BROADCAST, click)
})
</script>
<style scoped lang="scss">
.tool {
    @apply flex flex-col;
    position: absolute;
    right: 0;
    bottom: 290px;
    width: 88px;

    .tool-item {
        @apply flex flex-col items-center justify-center gap-[10px];
        margin-bottom: 27px;

        .tool-icon {
            position: relative;
            width: 56px;
            height: 56px;
            &.active {
                // .icon-active {

                // }
                animation: beat 0.3s linear forwards;
            }
        }

        .icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 56px;
            height: 56px;
        }
        .num {
            font-size: 22px;
            line-height: 32px;
            color: #ffffff;
        }
        // .icon-active {
        //     transform: translate(-50%, -50%) scale(0);
        // }
    }
    .tool-avatar {
        margin: 0 auto 27px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
@keyframes beat {
    0% {
        transform: scale(0);
    }
    70% {
        transform: scale(1);
    }
    80% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}
</style>
