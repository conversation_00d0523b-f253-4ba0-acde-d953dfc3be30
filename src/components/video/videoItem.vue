<template>
    <div class="video-item">
        <Video ref="videoEl" :item="item" :position="position" :isActive="isActive" :autoplay="autoplay" :state="state" />
        <div class="video-float">
            <div class="head-list" v-if="userTotal.length">
                <div v-for="(item, index) in userTotal" :key="item" :style="{ left: -10 + -8 * index + 'px' }" class="list-avatar">
                    <img :src="item" />
                </div>
                {{ userTotal.length > 99 ? '99+' : userTotal.length }}
            </div>
            <Tool :position="position" :item="item" @show-series="showSeries = !showSeries" v-bind="$attrs" />
            <div v-if="!isPlaying" class="play-icon"><img src="@/assets/img/video/play_btn.png" /></div>
            <div v-if="item.gametag" class="enter-tip" @pointerup.stop>
                <div v-enter="leave" ref="joinUser" class="jion-user" v-for="item in enterList" :key="item">
                    {{ $t('{nickname}_enter_game', { nickname: decodeName(item.nickname) }) }}
                </div>
            </div>
            <div class="video-foot" @pointerup.stop>
                <div v-if="item.gametag" class="foot-game">
                    <div class="foot-img">
                        <img :src="item.conf.picture" />
                    </div>
                    <div class="foot-content">
                        <p class="foot-title">{{ item.conf.name }}</p>
                        <van-text-ellipsis class="foot-des" rows="2" :content="item.conf.desc" />
                        <div class="foot-btn" @click="goGamePage(item.conf, item)">{{ $t('Play') }}</div>
                    </div>
                </div>
                <div v-if="item.type !== 2 && item.name" class="foot-video-name">{{ item.name }}</div>
                <SeriesList v-if="item.type === 2" v-model="showSeries" :item="item" :isActive="isActive" @series-Change="seriesChange" />

                <div :id="`bar-wrap${position.index}`"></div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="videos-item">
import Tool from './videoTool.vue'
import { useBaseStore } from '@/stores'
import { SlideItemPlayStatus } from '@/enums'
import bus, { EVENT_KEY } from '@/utils/bus'
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'
import { getVideoMsg } from '@/api'
import { decodeName, checkLogin } from '@/utils'
import { useRouter } from 'vue-router'
import Video from './video.vue'
import SeriesList from '@/components/video/seriesList.vue'

const router = useRouter()

const store = useBaseStore()
const { gameSubscibe } = useGame()

const props = defineProps({
    item: {
        type: Object,
        default: () => {
            return {}
        },
    },
    position: {
        type: Object,
        default: () => {
            return {}
        },
    },
    isActive: {
        type: Boolean,
        default: () => {
            return false
        },
    },
    autoplay: {
        type: Boolean,
        default: () => false,
    },
})
const emits = defineEmits(['seriesChange'])

const state = reactive({
    status: props.isActive ? SlideItemPlayStatus.Play : SlideItemPlayStatus.Pause,
})
const isPlaying = computed(() => {
    return state.status === SlideItemPlayStatus.Play
})
const videoEl = ref()

const showSeries = ref(false)

watch(
    () => props.isActive,
    (val) => {
        val ? play() : pause()
    }
)

const enterList = ref([])
const joinUser = ref(null)
const userList = ref([])
const userTotal = ref([])
let timer = null

const doEnter = () => {
    let index = 0
    timer && clearInterval(timer)
    timer = setInterval(() => {
        enterList.value.push({ ...userList.value[index % userList.value.length] })
        index++
    }, 1000)
}

const leave = () => {
    enterList.value.shift()
}
const goGamePage = async (game) => {
    checkLogin().then(async () => {
        const url = await gameSubscibe(game.gametag, 'video')
        if (url) {
            router.push({
                path: '/iframe',
                query: {
                    u: encodeURIComponent(url),
                    from: 'video',
                },
            })
        }
    })
}

const pause = () => {
    state.status = SlideItemPlayStatus.Pause
    videoEl.value?.pause()
}
const play = () => {
    state.status = SlideItemPlayStatus.Play
    videoEl.value.play().catch((e) => {
        console.log(e, '播放失败')
        if (e.message === 'NotAllowedError') {
            store.isMuted = true
            return videoEl.value.play()
        }
        state.status = SlideItemPlayStatus.Pause
    })
}
function click({ uniqueId, index, type }) {
    if (props.position.uniqueId === uniqueId && props.position.index === index) {
        if (type === EVENT_KEY.ITEM_TOGGLE) {
            if (state.status === SlideItemPlayStatus.Play) {
                pause()
            } else {
                play()
            }
        }
        if (type === EVENT_KEY.ITEM_STOP) {
            videoEl.value.inst.currentTime = 0
            pause()
        }
        if (type === EVENT_KEY.ITEM_PLAY) {
            videoEl.value.inst.currentTime = 0
            play()
        }
    }
}
const getGameMsg = async () => {
    const { conf, id } = props.item
    try {
        const res = await getVideoMsg({
            id,
            gameid: conf.gametag,
        })
        if (res.code === 200) {
            userList.value = res.data.message
            userTotal.value = res.data.avators
            if (userList.value.length) {
                doEnter()
            }
        }
    } catch (e) {
        console.log(e)
    }
}
watch(
    () => props.isActive,
    (val) => {
        if (val && props.item.gametag) {
            getGameMsg()
        }
    },
    {
        immediate: true,
    }
)
const seriesChange = (videoConfig) => {
    emits('seriesChange', videoConfig, props.position.index)
}

onMounted(() => {
    bus.on(EVENT_KEY.SINGLE_CLICK_BROADCAST, click)
    // listenEvent()
    window.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
            if (userList.value.length) {
                doEnter()
            }
        } else {
            timer && clearInterval(timer)
        }
    })
})

onBeforeUnmount(() => {
    bus.off(EVENT_KEY.SINGLE_CLICK_BROADCAST, click)
    timer && clearInterval(timer)
})
onActivated(() => {
    if (userList.value.length) {
        doEnter()
    }
})
onDeactivated(() => {
    timer && clearInterval(timer)
    enterList.value = []
})
</script>
<style scoped lang="scss">
$footHeight: 270px;
.video-dom {
    width: 100%;
    height: 100%;
}
.enter-tip {
    position: absolute;
    bottom: $footHeight;
    left: 0;
    pointer-events: none;
}
.video-item {
    @apply relative h-full;
    object-fit: cover;
    background-size: 100%;
    background-repeat: 100% 100%;

    .video-float {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // will-change:auto

        .play-icon {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100px;
            height: 100px;
            margin: auto auto;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .video-foot {
        @apply absolute flex flex-col box-border;
        width: 730px;
        padding: 23px 21px 21px 17px;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0 auto;
        // height: $footHeight;
        border-radius: 40px 40px 0 0;
        background-color: rgba(0, 0, 0, 0.8);

        .foot-game {
            @apply flex;
            margin-bottom: 15px;
            .foot-img {
                width: 220px;
                height: 200px;
                margin-right: 20px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            .foot-content {
                flex: 1;
                color: #fff;

                .foot-title {
                    font-weight: bold;
                    font-size: 36px;
                }
                .foot-des {
                    font-size: 24px;
                    height: 70px;
                }
                .foot-btn {
                    width: 440px;
                    height: 76px;
                    line-height: 76px;
                    margin: 13px auto 0;
                    background: linear-gradient(150deg, #ffc44f 0%, #ffb63d 40%, #ffa72a 100%), linear-gradient(#41aefe, #41aefe);
                    border-radius: 38px;
                    font-size: 36px;
                    font-weight: bold;
                    text-align: center;
                }
            }
        }
        .foot-video-name {
            margin-bottom: 11px;
            font-size: 22px;
            font-weight: bold;
            color: #fff;
        }
    }
    .enter-tip {
        box-sizing: border-box;
        width: 100%;
        height: 126px;
        .jion-user {
            display: flex;
            gap: 10px;
            position: absolute;
            bottom: 14px;
            left: 20px;
            font-size: 24px;
            line-height: 40px;
            color: #ffffff;
            animation: user-joined-anim 3s linear;

            // .join-avatar {
            //     width: 36px;
            //     height: 36px;
            //     background: red;
            //     border-radius: 50%;
            // }

            @keyframes user-joined-anim {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-140px);
                }
            }
        }
    }
    .head-list {
        position: absolute;
        right: 20px;
        top: 110px;
        width: 110px;
        height: 58px;
        line-height: 58px;
        padding-left: 50px;
        padding-right: 10px;
        text-align: right;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        background-color: #302e2c;
        border-radius: 29px;
        z-index: 20;
        text-align: center;

        .list-avatar {
            position: absolute;
            width: 58px;
            height: 58px;
            border-radius: 50%;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}
:deep(.video-loading) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
}
.small-video {
    position: relative;
    top: 200px;
    height: calc(100% - $footHeight - 200px);
    padding: 0 100px;
    margin: 0 auto;
}
</style>
