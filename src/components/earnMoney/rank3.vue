<template>
    <div class="rank-top3">
        <div :class="['rank', `rank${index}`]" v-for="(item, index) in data" :key="index">
            <div class="top-avatar">
                <div class="avatar">
                    <img :src="item.avatar" />
                </div>
            </div>
            <van-text-ellipsis class="rank-name" :content="encryptUsername(decodeName(item.nickname))" />
            <div class="rank-gold">
                <span>{{ store.curSymbol[store.wallet?.currency] }} </span> {{ formatNumber(item.score || 0) }}
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { formatNumber } from '@/utils/toolsValidate'
import { decodeName, encryptUsername } from '@/utils'
import { RankItem } from '@/api/earnCash/types'
import { useBaseStore } from '@/stores'

const store = useBaseStore()

withDefaults(
    defineProps<{
        data?: RankItem[]
    }>(),
    {
        data: () => [],
    }
)
</script>
<style lang="scss" scoped></style>
