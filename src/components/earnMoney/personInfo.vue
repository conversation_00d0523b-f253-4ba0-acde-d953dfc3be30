<template>
    <div class="person-top">
        <div class="person-avatar"><img v-if="userInfo.avatar" :src="userInfo.avatar" /></div>
        <div class="person-center">
            <div class="name elipsis">
                {{ decodeName(userInfo.nickname) || '****' }}
            </div>
            <van-rate class="person-rate" v-model="rate" disabled :icon="Rate" :void-icon="Brate" />
        </div>
        <div class="person-btn" @click="hanldeInvite">{{ $t('Invite_to_earn') }}</div>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { decodeName, checkLogin } from '@/utils'
import Rate from '@/assets/img/earn-cash/rate.png'
import Brate from '@/assets/img/earn-cash/brate.png'

const rate = defineModel({})

const emit = defineEmits(['invite'])

const store = useBaseStore()
const { userInfo } = storeToRefs(store)

const hanldeInvite = () => {
    checkLogin().then(() => {
        emit('invite')
    })
}
</script>
<style lang="scss" scoped>
.person-top {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 30px;

    .person-avatar {
        position: relative;
        width: 118px;
        height: 118px;

        img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        &::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            width: 120px;
            height: 120px;
            background: url('@/assets/img/earn-cash/avatar.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .person-center {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        overflow: hidden;
        // padding-top: 20px;
        .name {
            margin-bottom: 6px;
            font-size: 36px;
            font-weight: bold;
            line-height: 36px;
            color: #ffffff;
        }
        .person-rate {
            height: 40px;
        }
    }
    .person-btn {
        justify-self: flex-end;
        align-self: center;
        width: 220px;
        height: 68px;
        line-height: 68px;
        // background-color: #3ccaff;
        border-radius: 34px;
        border: solid 3px #ffffff;
        font-size: 24px;
        font-weight: bold;
        letter-spacing: -1px;
        color: #ffffff;
        text-align: center;
    }
}
</style>
