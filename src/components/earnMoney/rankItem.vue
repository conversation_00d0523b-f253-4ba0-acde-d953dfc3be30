<template>
    <div class="rank-peple">
        <div class="rank-num">
            <img v-for="(item, index) in `${person.index > 99 ? 99 : person.index}`.split('')" :key="index" :src="getImg(`${item}.png`)" />
            <img v-if="person.index > 99" src="@/assets/img/earn-cash/jia.png" />
        </div>
        <div class="rank-avatar"><img :src="person.avatar" /></div>
        <div class="rank-name elipsis">
            {{ Object.keys(self).length ? decodeName(person.nickname) || '****' : encryptUsername(decodeName(person.nickname)) }}
        </div>
        <div class="rank-gold">
            <span class="mr-[5px]">{{ curSymbol[wallet?.currency] }} </span> {{ formatNumber(person.score == -1 ? 0 : person.score) }}
        </div>
    </div>
</template>
<script lang="ts" setup>
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { decodeName, encryptUsername } from '@/utils'
import { formatNumber } from '@/utils/toolsValidate'

const { userInfo, wallet, curSymbol } = storeToRefs(useBaseStore())

const props = defineProps({
    item: {
        type: Object,
        default: () => ({}),
    },
    self: {
        type: Object,
        default: () => ({}),
    },
    isSelf: {
        type: Boolean,
        default: () => false,
    },
    index: {
        type: Number,
        default: () => 0,
    },
})
const path = '../../assets/img/earn-cash/'
const file = import.meta.glob('../../assets/img/earn-cash/*', { eager: true })

const getImg = (name) => {
    return file[path + name]?.default
}
const person = computed<{
    nickname: string
    avatar: string
    index: number
    uid: number
    score: number
}>(() => {
    if (props.isSelf) {
        const index = props.self.index
        return {
            score: +props.self.score,
            index: ~index ? index + 1 : 100,
            avatar: userInfo.value.avatar,
            nickname: userInfo.value.nickname,
            uid: userInfo.value.uid,
        }
    }
    const { avatar, nickname, uid, scores } = props.item
    return {
        score: scores,
        avatar,
        nickname,
        uid,
        index: props.index + 4,
    }
})
</script>
<style lang="scss" scoped>
.rank-peple {
    display: flex;
    align-items: center;
    // gap: 15px;
    margin: 20px 0;
    height: 88px;
    padding-right: 34px;
    background: url('@/assets/img/earn-cash/rank_other.png') no-repeat;
    background-size: 100% 100%;
    font-size: 28px;
    color: #fff;

    .rank-num {
        display: flex;
        justify-content: center;
        width: 75px;
        text-align: center;
        img {
            width: 26px;
            height: 30px;
        }
    }
    .rank-avatar {
        margin: 0 15px;
        width: 60px;
        height: 60px;
        background-color: #5431bf;
        border: solid 4px rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        overflow: hidden;
    }
    .rank-name {
        // flex: 1;
        width: 58%;
        margin: 10px;
    }
    .rank-gold {
        display: flex;
        align-items: center;
        // width: 120px;
        // gap: 10px;
        // &::before {
        //     display: inline-block;
        //     content: '';
        //     width: 40px;
        //     height: 40px;
        //     margin-right: 10px;
        //     background: url('@/assets/img/earn-cash/rank_gold.png') no-repeat;
        //     background-size: 100% 100%;
        // }
    }
}
</style>
