<template>
    <van-overlay :show="visible" @click="handleClose" z-index="3001" :lock-scroll="false">
        <div class="dialog" @click.stop>
            <div class="close" @click="handleClose"></div>
            <div class="player-content">
                <div class="title">{{ $t('Redemption_Record') }}</div>
                <div class="list">
                    <div class="sub-title">
                        <div class="sub-title-wrap">
                            <div class="title-text">{{ $t('Time') }}</div>
                            <div class="title-text">{{ $t('Redemption') }}</div>
                        </div>
                    </div>
                    <div class="list-scroll">
                        <van-list
                            v-model:loading="list.loading"
                            :finished="list.finished"
                            :loading-text="$t('Loading_please_wait')"
                            :finished-text="$t('No_more')"
                            @load="onLoad"
                        >
                            <div class="list-item" v-for="(item, index) in list.data" :key="index">
                                <div>{{ dayjs(item._source.time).format('YY/MM/DD HH:mm') }}</div>
                                <div class="text-red-500">{{ item._source.context.currency }} {{ item._source.context.count }}</div>
                            </div>
                        </van-list>
                    </div>
                </div>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup>
import { usePagation } from '@/hooks/usePagation'
import { getBeanexchangelogs } from '@/api/earnCash/index'
import dayjs from 'dayjs'

const visible = ref(true)
const emits = defineEmits(['onClose'])

//总收益榜
const { list, start } = usePagation()

const handleClose = () => {
    emits('onClose')
}
const onLoad = async () => {
    list.loading = true
    try {
        const res = await getBeanexchangelogs({ from: start.value, size: list.pageSize })
        if (res.code === 200) {
            const newList = res.data
            if (newList.length < list.pageSize) {
                list.finished = true
            }
            list.data = [...list.data, ...newList]
        }
    } finally {
        list.loading = false
        list.pageNum++
    }
}
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.player-content {
    .sub-title-wrap {
        display: flex;
        gap: 23px;
        // width: 427px;
        height: 100%;
        margin: 0 auto;
        line-height: $subTitle;
        font-size: 28px;
        text-align: center;

        .title-text {
            flex: 1;
            text-indent: 36px;
        }
    }

    .list-item {
        gap: 14px;
        padding: 0 $pad;

        div {
            flex: 1;
            text-align: center;
        }
    }
}
</style>
