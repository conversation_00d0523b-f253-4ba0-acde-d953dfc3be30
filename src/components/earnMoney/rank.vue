<template>
    <van-overlay class="rank-overlay" :show="visible" @click="handleClose" z-index="3001" :lock-scroll="false">
        <div class="rank" @click.stop>
            <div class="rank-head">
                <div class="rank-title"><img src="@/assets/img/earn-cash/rank_title.png" /></div>
                <div class="rank-close" @click="handleClose"></div>
            </div>
            <div class="rank-content">
                <div :class="['rank-tab', { 'rank-tab1': active === 2 }]">
                    <div :class="[`tab-item`, { active: active === item.type }]" v-for="item in tab" :key="item.type" @click="chanTab(item)">
                        {{ $t(item.name) }}
                    </div>
                </div>
                <LiftRank v-show="active === 1" :isActive="active === 1" />
                <MonthRank v-show="active === 2" :isActive="active === 2" />
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup>
import LiftRank from './lifeRank.vue'
import MonthRank from './monthRank.vue'

const visible = ref(true)
const active = ref(1)
const emits = defineEmits(['onClose'])

const tab = [
    {
        name: 'Lifetime_Rank',
        type: 1,
    },
    {
        name: 'Monthly_Rank',
        type: 2,
    },
]
const chanTab = (item) => {
    active.value = item.type
}

const handleClose = () => {
    emits('onClose')
}
</script>
<style lang="scss" scoped>
$titleHeight: 44px;
$tabheight: 160px;
$tansH: 40px;
$rankTopH: 210px;
$rankPad: 10px;
$mt: 10px;
.rank-overlay {
    background: rgba(0, 0, 0, 1);
}
.rank {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 700px;
    height: 70vh;
    transform: translate3d(-50%, -50%, 0);
    background: url('@/assets/img/earn-cash/rank_bg.png') no-repeat;
    background-size: 100% 100%;

    .rank-head {
        .rank-title {
            position: relative;
            top: -20px;
            width: 505px;
            height: $titleHeight;
            margin: 0 auto;

            &::before {
                content: '';
                position: absolute;
                z-index: -1;
                width: 108px;
                height: 150px;
                top: -40px;
                left: -70px;
                background: url('@/assets/img/earn-cash/rank_ward.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .rank-close {
        position: absolute;
        width: 120px;
        height: 61px;
        right: -16px;
        top: -16px;
        background: url('@/assets/img/earn-cash/rank_close.png') no-repeat right center;
        background-size: 61px 100%;
    }
    .rank-content {
        padding: 0 16px;
        height: calc(70vh - $titleHeight - 20px);

        .rank-tab {
            position: relative;
            display: flex;
            height: $tabheight;
            font-size: 32px;
            font-weight: bold;
            color: #b171ff;
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 241px;
                z-index: -1;
                background: url('@/assets/img/earn-cash/tab_bg.png') no-repeat;
                background-size: 100% 100%;
            }

            &.rank-tab1 {
                &::before {
                    transform: rotateY(180deg);
                }
            }

            .tab-item {
                flex: 1;
                height: 80px;
                line-height: 80px;
                text-align: center;

                &.active {
                    color: #fff;
                }
            }
        }
        .rank-list {
            position: relative;
            top: -$tansH;
            height: calc(100% - $tabheight + $tansH);
            margin-bottom: $rankPad;

            $selfHeight: 108px;

            :deep(.rank-scroll) {
                // - $rankTopH - $mt
                height: calc(100% - $rankPad - $selfHeight);
                overflow: scroll;
            }
            :deep(.rank-top3) {
                @apply grid grid-cols-3;
                margin-bottom: 10px;
                padding-top: 42px;

                .rank {
                    position: relative;
                    width: 220px;
                    height: $rankTopH;
                    padding: 115px 10px 0;
                    border-radius: 20px;
                    background-image: linear-gradient(150deg, #ffcc44 0%, rgba(255, 204, 68, 0.7) 40%, rgba(255, 204, 68, 0.4) 100%);

                    .top-avatar {
                        position: absolute;
                        top: -42px;
                        left: 50%;
                        width: 120px;
                        height: 140px;

                        transform: translateX(-50%);
                        &::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: url('@/assets/img/earn-cash/first.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .avatar {
                            width: 120px;
                            height: 120px;
                            border-radius: 50%;
                            overflow: hidden;
                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                                object-fit: cover;
                            }
                        }
                    }
                    .rank-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #ffffff;
                        text-align: center;
                        margin-bottom: 5px;
                    }
                    .rank-gold {
                        display: flex;
                        justify-content: center;
                        gap: 10px;
                        height: 40px;
                        background-color: rgba(0, 0, 0, 0.2);
                        border-radius: 20px;
                        font-size: 26px;
                        font-weight: bold;
                        color: #fff;
                        text-align: center;

                        // &::before {
                        //     content: '';
                        //     display: inline-block;
                        //     width: 40px;
                        //     height: 40px;
                        //     background: url('@/assets/img/earn-cash/rank_gold.png') no-repeat;
                        //     background-size: 100% 100%;
                        // }
                    }
                }
                .rank1 {
                    background-image: linear-gradient(150deg, #98e0ff 0%, rgba(152, 224, 255, 0.63) 40%, rgba(152, 224, 255, 0.25) 100%),
                        linear-gradient(#000000, #000000);
                    .top-avatar {
                        &::after {
                            background-image: url('@/assets/img/earn-cash/second.png');
                        }
                    }
                }
                .rank2 {
                    background-image: linear-gradient(150deg, #c56f5b 0%, rgba(197, 111, 91, 0.7) 40%, rgba(197, 111, 91, 0.4) 100%),
                        linear-gradient(#c56446, #c56446);
                    .top-avatar {
                        &::after {
                            background-image: url('@/assets/img/earn-cash/third.png');
                        }
                    }
                }
            }
            .rank-peple {
                display: flex;
                align-items: center;
                gap: 15px;
                height: 88px;
                padding-right: 34px;
                background: url('@/assets/img/earn-cash/rank_other.png') no-repeat;
                background-size: 100% 100%;
                font-size: 28px;
                color: #fff;

                .rank-num {
                    display: flex;
                    justify-content: center;
                    width: 75px;
                    text-align: center;
                    img {
                        width: 26px;
                        height: 30px;
                    }
                }
                .rank-avatar {
                    width: 60px;
                    height: 60px;
                    background-color: #5431bf;
                    border: solid 4px rgba(255, 255, 255, 0.4);
                    border-radius: 50%;
                    overflow: hidden;
                }
                .rank-name {
                    flex: 1;
                }
                .rank-gold {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    // &::before {
                    //     display: inline-block;
                    //     content: '';
                    //     width: 40px;
                    //     height: 40px;
                    //     background: url('@/assets/img/earn-cash/rank_gold.png') no-repeat;
                    //     background-size: 100% 100%;
                    // }
                }
            }
            :deep(.rank-self) {
                position: absolute;
                bottom: -15px;
                left: 0;
                width: 100%;
                height: 108px;
                background-image: url('@/assets/img/earn-cash/rank_self.png');
            }
        }
    }
}
</style>
