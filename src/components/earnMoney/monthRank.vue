<template>
    <div class="rank-list">
        <div class="rank-scroll">
            <Rank3 v-if="rankTop.length" :data="rankTop" />
            <van-list
                v-model:loading="list.loading"
                :finished="list.finished"
                :loading-text="$t('Loading_please_wait')"
                :finished-text="$t('No_more')"
                @load="onLoad"
                :immediate-check="false"
            >
                <RankItem v-for="(item, index) in ranList" :key="index" :item="item" :index="index" />
            </van-list>
        </div>
        <RankItem class="rank-self" :isSelf="true" :self="selfData" />
    </div>
</template>
<script lang="ts" setup>
import { getPpopulars, getNotLoginPpopulars } from '@/api/earnCash'
import { usePagation } from '@/hooks/usePagation'
import RankItem from './rankItem.vue'
import Rank3 from './rank3.vue'
import { useBaseStore } from '@/stores'

const props = defineProps({
    isActive: {
        type: Boolean,
        default: false,
    },
})
const store = useBaseStore()

//总收益榜
const { list, start, end, combileList } = usePagation()
list.loading = true
const selfData = ref({})
const rankTop = computed(() => list.data.slice(0, 3).map((item) => ({ ...item, score: item.scores })))
const ranList = computed(() => list.data.slice(3, 50))

watch(
    () => props.isActive,
    (val) => {
        val && (list.loading = false)
    }
)

const onLoad = async () => {
    list.loading = true
    const getData = store.token ? getPpopulars : getNotLoginPpopulars
    try {
        const res = await getData({ start: start.value, stop: end.value, type: 'month' })
        if (res.code === 200) {
            const newList = combileList(res.data.populars.members, res.data.populars.scores, 'uid', 'scores')
            selfData.value = res.data.self
            if (newList.length < list.pageSize) {
                list.finished = true
            }
            list.data = [...list.data, ...newList]
            if (list.data.length >= 50) {
                list.finished = true
            }
        }
    } finally {
        list.loading = false
        list.pageNum++
    }
}
</script>

<style scoped lang="scss"></style>
