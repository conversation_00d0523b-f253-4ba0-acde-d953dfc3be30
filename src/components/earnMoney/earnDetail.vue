<template>
    <van-list
        v-model:loading="list.loading"
        :finished="list.finished"
        :loading-text="$t('Loading_please_wait')"
        :finished-text="$t('No_more')"
        @load="onLoad"
    >
        <div class="list-item" v-for="(item, index) in list.data" :key="index">
            <div>{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div>{{ item.context.uid }}</div>
            <div>{{ item.context.game }}</div>
            <div>{{ item.context.currency === 'PHP' ? '₱' : '$' }} {{ formatNumber(item.context.income) }}</div>
        </div>
    </van-list>
</template>
<script lang="ts" setup>
import { usePagation } from '@/hooks/usePagation'
import { getContribution } from '@/api/earnCash'
import { formatNumber } from '@/utils/toolsValidate'
import dayjs from 'dayjs'

const { list, start } = usePagation()

const onLoad = async () => {
    list.loading = true
    try {
        const res = await getContribution({ from: start.value, size: list.pageSize })
        if (res.code === 200) {
            const newList = res.data.map((item) => ({ ...item._source }))
            if (newList.length < list.pageSize) {
                list.finished = true
            }
            list.data = [...list.data, ...newList]
        }
    } finally {
        list.loading = false
        list.pageNum++
    }
}
</script>
<style lang="scss" scoped>
@use './common.scss' as *;
.list-item {
    div {
        flex: 1;
        text-align: center;
        &:last-child {
            color: #ff3232;
        }
    }
}
</style>
