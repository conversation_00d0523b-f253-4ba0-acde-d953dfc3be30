<template>
    <van-overlay :show="visible" @click="handleClose" z-index="3001" :lock-scroll="false">
        <div class="dialog" @click.stop>
            <div class="close" @click="handleClose"></div>
            <div class="player-content">
                <div class="title">{{ $t('T1_Players') }}</div>
                <div class="list">
                    <div class="sub-title">
                        <div class="sub-title-wrap">
                            <div class="title-text">{{ $t('ID') }}</div>
                            <div class="title-text">{{ $t('Bind Time') }}</div>
                        </div>
                    </div>
                    <div class="list-scroll">
                        <van-list
                            v-model:loading="list.loading"
                            :finished="list.finished"
                            :loading-text="$t('Loading_please_wait')"
                            :finished-text="$t('No_more')"
                            @load="onLoad"
                        >
                            <div class="list-item" v-for="(item, index) in list.data" :key="index">
                                <div class="list-avatar"><img :src="item._source.avatar" /></div>
                                <div class="list-id">
                                    <p class="id-top">{{ decodeName(item._source.nickname) }}</p>
                                    <p>ID:{{ item._source.uid }}</p>
                                </div>
                                <div class="list-time">{{ dayjs(item._source.time).format('YY/MM/DD HH:mm') }}</div>
                                <div class="list-chat"></div>
                            </div>
                        </van-list>
                    </div>
                </div>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup>
import { usePagation } from '@/hooks/usePagation'
import { getPromoterInvite } from '@/api/earnCash/index'
import dayjs from 'dayjs'
import { decodeName } from '@/utils'

const visible = ref(true)
const emits = defineEmits(['onClose'])

//总收益榜
const { list, start } = usePagation()

const handleClose = () => {
    emits('onClose')
}
const onLoad = async () => {
    list.loading = true
    try {
        const res = await getPromoterInvite({ from: start.value, size: list.pageSize })
        if (res.code === 200) {
            const newList = res.data
            if (newList.length < list.pageSize) {
                list.finished = true
            }
            list.data = [...list.data, ...newList]
        }
    } finally {
        list.loading = false
        list.pageNum++
    }
}
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.player-content {
    .sub-title-wrap {
        display: flex;
        gap: 23px;
        width: 427px;
        height: 100%;
        margin: 0 auto;
        line-height: $subTitle;
        font-size: 28px;

        .title-text {
            flex: 1;
            text-indent: 36px;
        }
    }
    .list-avatar {
        width: 77px;
        height: 77px;
        border-radius: 50%;
        overflow: hidden;
        background-color: #3a3533;
        border: solid 4px rgba(255, 255, 255, 0.2);
        img {
            width: 100%;
            height: 100%;
        }
    }
    .list-item {
        gap: 14px;
    }
    .list-id {
        padding-right: 100px;
    }

    .list-chat {
        width: 34px;
        height: 30px;
    }
}
</style>
