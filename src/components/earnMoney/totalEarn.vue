<template>
    <van-overlay :show="visible" @click="handleClose" z-index="3001" :lock-scroll="false">
        <div class="dialog" @click.stop>
            <div class="close" @click="handleClose"></div>
            <div class="player-content">
                <div class="title">
                    <div :class="['tab', { active: active === item.type }]" v-for="item in tab" :key="item.type" @click="changeTab(item)">
                        {{ $t(item.name) }}
                    </div>
                </div>
                <div class="list">
                    <div :class="['sub-title', `sub-title${active}`]">
                        <div v-for="item in titleConfig[active]" :key="item">{{ item }}</div>
                    </div>
                    <div class="list-scroll">
                        <EarnDetail v-show="active === 1" />
                        <Contribution v-show="active === 2" :isActive="active === 2" />
                    </div>
                </div>
            </div>
        </div>
    </van-overlay>
</template>
<script lang="ts" setup>
import EarnDetail from './earnDetail.vue'
import Contribution from './contribution.vue'
const visible = ref(true)
const emits = defineEmits(['onClose'])

const titleConfig = {
    1: ['Time', 'PlayerID', 'Game', 'Contribution'],
    2: ['Player Info', 'Last log-in', 'My Revenue'],
}

const tab = [
    {
        name: 'Earned_Details',
        type: 1,
    },
    {
        name: 'Top_100_Contribution',
        type: 2,
    },
]
const active = ref(1)

const changeTab = (item) => {
    active.value = item.type
}
const handleClose = () => {
    emits('onClose')
}
</script>
<style lang="scss" scoped>
@use './common.scss' as *;

.player-content {
    .title {
        display: flex;
        padding-top: 14px;
        font-size: 24px;
        .tab {
            position: relative;
            width: 298px;
            height: 80px;
            line-height: 80px;
            background: url('@/assets/img/earn-cash/t_tab.png') no-repeat;
            background-size: 100% 100%;

            &.active {
                top: -3px;
                height: 86px;
                line-height: 86px;
                background-image: url('@/assets/img/earn-cash/t_tab_a.png');
                z-index: 1;
            }
            &:last-child {
                left: -12px;
            }
        }
    }
    .sub-title {
        display: flex;
        gap: 10px;
        padding: 0 20px;
        line-height: 72px;
        text-align: center;
        div {
            flex: 1;
        }
        &.sub-title2 {
            div:first-child {
                width: 260px;
                flex: none;
                text-align: left;
            }
        }
    }
}
</style>
