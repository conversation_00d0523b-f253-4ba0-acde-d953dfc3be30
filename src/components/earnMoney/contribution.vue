<template>
    <van-list
        v-model:loading="list.loading"
        :finished="list.finished"
        :loading-text="$t('Loading_please_wait')"
        :finished-text="$t('No_more')"
        @load="onLoad"
    >
        <div class="list-item" v-for="(item, index) in list.data" :key="index">
            <div class="person">
                <div class="avatar"><img :src="item.avatar" /></div>
                <div class="person-content">
                    <div class="name">{{ decodeName(item.nickname) }}</div>
                    <div>ID:{{ item.uid }}</div>
                </div>
            </div>
            <div>{{ item.timestamp ? dayjs(item.timestamp).format('MM/DD HH:mm') : '-' }}</div>
            <div>+₱ {{ formatNumber(item.scores / 10000) }} <span @click="goChat(item)"></span></div>
        </div>
    </van-list>
</template>
<script lang="ts" setup>
import { usePagation } from '@/hooks/usePagation'
import { getToppromoter } from '@/api/earnCash'
import { decodeName, getChannel } from '@/utils'
import dayjs from 'dayjs'
import { formatNumber } from '@/utils/toolsValidate'
import { useBaseStore } from '@/stores'
import { useRouter } from 'vue-router'

const props = defineProps({
    isActive: {
        type: Boolean,
        default: () => false,
    },
})
const store = useBaseStore()
const router = useRouter()

const { list, start, end, combileList } = usePagation()
list.loading = true
const goChat = (item) => {
    const channel = getChannel(item.uid, store.userInfo.uid)
    store.$patch((state) => {
        state.unReadCount[channel] = 0
    })
    router.push({
        path: '/chat',
        query: {
            nickname: decodeName(item.nickname),
            userID: item.uid,
        },
    })
}

const onLoad = async () => {
    list.loading = true
    try {
        const res = await getToppromoter({ start: start.value, stop: end.value })
        if (res.code === 200) {
            const newList = combileList(res.data.results.members, res.data.results.scores, 'uid', 'scores')
            if (newList.length < list.pageSize) {
                list.finished = true
            }
            list.data = [...list.data, ...newList]
        }
    } finally {
        list.loading = false
        list.pageNum++
    }
}
watch(
    () => props.isActive,
    (val) => {
        val && (list.loading = false)
    }
)
</script>
<style lang="scss" scoped>
@use './common.scss' as *;
.list-item {
    div {
        flex: 1;
        text-align: center;
        &.person {
            display: inline-block;
            width: 260px;
            flex: none;
            text-align: left;
        }
    }
    .person-content {
        display: inline-block;
        margin-left: 10px;
        .name {
            margin-bottom: 5px;
        }
    }
    .avatar {
        position: relative;
        top: 5px;
        display: inline-block;
        width: 76px;
        height: 76px;
        border-radius: 50%;
        background-color: #3a3533;
        border: solid 4px rgba(255, 255, 255, 0.2);
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
