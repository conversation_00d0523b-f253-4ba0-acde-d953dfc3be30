<template>
    <van-pull-refresh
        class="scroll-wrapper"
        v-model="state.refreshing"
        :disabled="disableRefresh"
        :pulling-text="$t('Refresh_after_release')"
        :loosing-text="$t('Refresh_after_release')"
        @refresh="onRefresh"
    >
        <template #loading>
            <Loading :is-full-screen="false" />
        </template>
        <van-list
            v-model:loading="state.loading"
            :finished="state.finished"
            @load="onLoad"
            :finished-text="!showFinishText ? '' : state.finished && state.page === 1 ? '' : $t('No_more')"
            :error-text="$t('加载列表失败，点击重新加载')"
            :loading-text="$t('Loading_please_wait')"
        >
            <slot></slot>
        </van-list>
    </van-pull-refresh>
</template>
<script setup lang="ts" name="ScrollList">
import Loading from './Loading.vue'

const props = defineProps({
    disableRefresh: {
        type: Boolean,
        default: () => true,
    },
    api: {
        type: Function,
        default() {
            return () => void 0
        },
    },
    showFinishText: {
        type: Boolean,
        default: () => true,
    },
})
const emits = defineEmits(['onChange'])
// 状态
const state = reactive({
    loading: false,
    finished: false,
    refreshing: false,
    page: 1,
    pageSize: 10,
})
// 上拉加载数据
const onLoad = async (loading = true) => {
    state.loading = true
    const newList = await props.api({ ...state }, loading)
    state.loading = false
    if (newList.length < state.pageSize) {
        state.finished = true
    }
    emits('onChange', [...newList], {
        reset: state.page === 1,
        ...state,
    })
    if (state.finished) {
        return
    }
    state.page++
}
// 刷新
const onRefresh = async () => {
    state.finished = false
    state.loading = true
    state.page = 1
    await onLoad()
    state.refreshing = false
}

defineExpose({
    getList: onLoad,
    refresh: onRefresh,
})
</script>
<style lang="scss" scoped>
// .scroll-wrapper {
//     width: 100%;
//     height: 100%;
//     // touch-action: pan-y;
//     overflow: auto;
// }
</style>
