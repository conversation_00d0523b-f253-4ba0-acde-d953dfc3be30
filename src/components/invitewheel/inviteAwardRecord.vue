<template>
    <van-popup class="invite-record-pop" v-model:show="recordshow" :duration="0.2" @closed="onClosed">
        <div class="menu-title">
            <button class="menu-close" @click="onClosed"></button>
            <div class="nav-title">{{ $t('invite_wheel_withdrawal_history') }}</div>
        </div>
        <RecordList />
    </van-popup>
</template>
<script setup lang="ts">
import RecordList from './recordList.vue'

import { useBaseStore } from '@/stores'
const recordshow = defineModel<boolean>()
const store = useBaseStore()

const records = ref([])

const onClosed = () => {
    recordshow.value = false
}

const getRecords = async () => {
    records.value = store.redfissionInfo.redrecords
    console.log('redrecords----', records.value)
}

onMounted(() => {
    getRecords()
})
</script>
<style lang="scss" scoped>
.invite-record-pop {
    width: 100%;
    height: 100%;
    max-width: 100%;
    background-color: #202222;
    display: flex;
    justify-content: center;

    .menu-title {
        @apply flex justify-center absolute;
        width: 100%;
        height: var(--menu-header);
        z-index: 1000;
        background-color: rgba(50, 55, 56, 1);
        color: #fff;
        display: flex;
        text-align: center;
        align-items: flex-end;
        padding-bottom: 30px;
        button {
            width: 55px;
            height: 55px;
            position: absolute;
        }

        button::after {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            right: -15px;
            bottom: -15px;
        }

        .menu-close {
            position: absolute;
            left: 25px;
            border-radius: 10px;
            background: url('@/assets/img/icon/rounded-left.svg') no-repeat center center;
            background-size: 70% 70%;
            background-color: rgb(70, 79, 80, 0.5);
            color: #fff;
        }

        .nav-title {
            font-size: 30px;
            font-weight: 450;
            font-weight: bolder;
        }
    }
}
</style>
