<template>
    <component :is="currentPopupComponent" :isShowOverlay="false" />
    <van-popup class="invite-reject" v-model:show="showreject" round @click-overlay="onClickReject"
        ><div v-html="$t('invite_wheel_reject')"></div>
        <button class="btn-box green" @click="onClickOverlay">{{ $t('invite_wheel_reject_btn_txt') }}</button>
    </van-popup>
</template>
<script setup lang="ts">
import eventBus from '@/utils/bus'
import { useBaseStore } from '@/stores'
import inviteboxPopup from './inviteboxPopup.vue'
import invitewheelPopup from './invitewheelPopup.vue'
import PopupManager from '@/utils/PopupManager'
import { RedfissionInfo } from '@/stores/types'
import InviteAwardPop from './inviteAwardPop.vue'
import bus from '@/utils/bus'

const popupType = ref('reject')

const currentPopupComponent = computed(() => {
    const popupMap = {
        box: inviteboxPopup,
        spin: invitewheelPopup,
        award: InviteAwardPop,
        reject: null,
    }
    return popupMap[popupType.value]
})

const store = useBaseStore()
const showreject = ref(false)

const onClickOverlay = () => {
    PopupManager.checkQueue()
}

const onClickReject = () => {
    showreject.value = false
    popupType.value = 'box'
}

const getEventInfo = async (isloading: boolean = false) => {
    try {
        const res = await store.getSignEvent(
            {
                id: 'redfission',
            },
            false,
            isloading
        )
        if (res.code === 200) {
            const acticity = <RedfissionInfo>res.acticity
            store.redfissionInfo = acticity
            bus.emit('redfissionable', res.acticity.status === 1)
            return
        }
    } catch (e) {
        console.log(e)
    }
    bus.emit('redfissionable', false)
}

const checkRedfission = async () => {
    if (!store.userInfo.uid) return
    const redfissionInfo = store.redfissionInfo
    if (!redfissionInfo) {
        await getEventInfo(true)
    }
    popupType.value = 'reject'

    if (redfissionInfo && (redfissionInfo.status === 1 || redfissionInfo.show === 1)) {
        const user = redfissionInfo.user
        console.log('redfissionInfo----', redfissionInfo)
        showreject.value = false
        if (user) {
            const pick = user?.pick || 0
            const examine = user?.examine || 0
            if (user.cheat === 1) {
                popupType.value = 'reject'
                showreject.value = true
            } else if (pick == 1) {
                popupType.value = 'box'
            } else if (examine == 2) {
                popupType.value = 'award'
            } else {
                popupType.value = 'spin'
            }
        } else {
            popupType.value = 'box'
        }
    } else {
        PopupManager.closeCurrentPopup()
    }
}

onBeforeMount(() => {
    eventBus.on('refreshRedfissionInfo', getEventInfo)
})
onBeforeUnmount(() => {
    eventBus.off('refreshRedfissionInfo', getEventInfo)
})

onMounted(async () => {
    await getEventInfo(true)
    await checkRedfission()
})
</script>
<style lang="scss" scoped>
.invite-reject {
    background-color: #232626;
    color: rgba(255, 255, 255, 0.95);
    padding: 45px;
    width: 90%;
    height: auto;
    max-height: 60%;
    font-size: medium;
    max-width: fit-content;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 40px;

    .btn-box {
        flex: 1;
        height: 65px;
        font-size: 28px;
        font-weight: bold;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        color: #000;
        font-weight: 800;
        align-items: center;
        display: flex;
        justify-content: center;
    }

    .green {
        background-image: linear-gradient(90deg, #24ee89, #9fe871);
        box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
    }
}
</style>
