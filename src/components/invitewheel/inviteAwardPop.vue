<template>
    <van-popup class="cashout-pop" v-model:show="showaward" close-on-popstate round :z-index="3001" :close-on-click-overlay="false">
        <img src="@/assets/img/invitewheel/cash.png" />
        <div class="close" @click="handleClose"></div>
        <div class="award-info">
            <div class="cashout-title">{{ $t('invite_wheel_award_won') }}</div>
            <div class="cashout-gold">{{ cashoutgold }}</div>
            <div class="award-describe">
                <div class="award-describe-cell">
                    <span>{{ $t('Type') }}: </span> <span>{{ $t('Cash') }} </span>
                </div>
                <div class="award-describe-cell">
                    <span>{{ $t('wallet_bet_amount_left') }}: </span> <span class="text-green">{{ betamount }} </span>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn-box green" @click="onClickAction">{{ btntxt }}</button>
        </div>
    </van-popup>
    <signaward v-model="claimawardinfo.isshow" v-if="claimawardinfo.isshow" :type="claimawardinfo.type" :msg="claimawardinfo.items" z-index="3002" />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
import GoldPic from '@/assets/img/dialog/sign/cash.png'
import PopupManager from '@/utils/PopupManager'
import signaward from '@/components/dialog/signaward.vue'
const store = useBaseStore()
const showaward = defineModel<boolean>()
const i18n = useI18n()

const examine = ref(0)
const pick = ref(0)

const props = defineProps({
    isShowOverlay: {
        type: Boolean,
        default: () => true,
    },
})

const claimawardinfo = ref({
    isshow: false,
    type: 2,
    items: {},
})

const cashoutgold = computed(() => {
    const rednum = store.redfissionInfo?.user?.rednum
    return store.curSymbol[store.wallet?.currency] + rednum.toFixed(2)
})

const betamount = computed(() => {
    const rednum = store.redfissionInfo?.user?.rednum
    const flowmult = store.redfissionInfo?.Configs?.flowmult || 1
    return store.curSymbol[store.wallet?.currency] + (rednum * flowmult).toFixed(2)
})

const btntxt = computed(() => {
    return i18n.t('Claim')
})

const onClickAction = () => {
    console.log('onClickAction')

    if (pick.value == 1) {
        showSuccessToast(i18n.t('message_claim_success'))
        return
    }
    claimReward()
}

const claimReward = async (isloading: boolean = false) => {
    try {
        const res = await store.ReceiveRewards(
            {
                id: 'redfission',
            },
            isloading
        )

        if (res.code === 200) {
            console.log('redfission examine', res)
            pick.value = 1
            PopupManager.checkQueue()
            claimawardinfo.value = {
                isshow: true,
                type: 2,
                items: {
                    img: GoldPic,
                    name: 'Cash',
                    worth: store.curSymbol[store.wallet?.currency] + res?.awards?.gold.toFixed(2),
                },
            }
            return
        }
    } catch (e) {
        console.log(e)
    }

    showFailToast(i18n.t('message_claim_fail'))
}

const handleClose = () => {
    showaward.value = false
    if (!props.isShowOverlay) {
        PopupManager.checkQueue()
    }
}

onMounted(() => {
    showaward.value = true
    examine.value = store.redfissionInfo?.user?.examine || 0
    pick.value = store.redfissionInfo?.user?.pick || 0
})
</script>
<style lang="scss" scoped>
.text-green {
    color: #25d276;
}
.cashout-pop {
    width: 550px;
    height: 500px;
    padding: 30px;
    max-width: 100%;
    overflow: visible;
    background-color: #2c3031;

    img {
        position: absolute;
        width: 40%;
        right: 50%;
        transform: translate(50%, -50%);
    }

    .close {
        position: absolute;
        right: 25px;
        top: 25px;
        width: 46px;
        height: 46px;
        border-radius: 10px;
        background: url('@/assets/img/close.png') no-repeat center center;
        background-color: #ffffff1a;
        background-size: 13px 13px;
    }
    .award-info {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 240px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .cashout-title {
            font-size: 62px;
            font-family: MicrosoftYaHei;
            font-weight: bold;
            color: #ffffff;
        }
        .cashout-gold {
            font-size: 38px;
            font-family: MicrosoftYaHei;
            font-weight: bold;
            color: #25d276;
        }

        .award-describe {
            padding: 30px;
            width: 100%;
            height: 100px;
            font-size: 22px;
            font-family: MicrosoftYaHei;
            font-weight: 400;
            color: #ffffffbb;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .award-describe-cell {
                line-height: 40px;
                width: 100%;
                display: flex;
                justify-content: space-between;
            }
        }
    }

    .action-buttons {
        position: relative;
        align-items: center;
        height: 100px;
        display: flex;
        top: 80%;
        align-items: center;
        justify-content: center;

        .btn-box {
            height: 70px;
            width: 100%;
            font-size: 20px;
            font-weight: bold;
            border-radius: 12px;
            cursor: pointer;
            color: #000;
            font-weight: 800;
            align-items: center;
            display: flex;
            justify-content: center;
        }

        .green {
            background-image: linear-gradient(90deg, #24ee89, #9fe871);
            box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
        }
    }
}
</style>
