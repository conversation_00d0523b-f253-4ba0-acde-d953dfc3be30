<template>
    <van-popup
        class="cashout-pop"
        v-model:show="show"
        close-on-popstate
        round
        :z-index="3001"
        @closed="handleClose"
        :close-on-click-overlay="false"
        :overlay-style="{ backgroundColor: 'transparent' }"
    >
        <img src="@/assets/img/invitewheel/coin_bg.avif" />
        <div class="close" @click="handleClose"></div>
        <div class="cashout-gold">{{ cashoutgold }}</div>
        <div class="bar">
            <div class="fill" :style="{ width: progress }"></div>
        </div>
        <div class="cashout-progress">
            Progress <span>{{ progress }}</span>
        </div>
        <div class="action-buttons">
            <button class="btn-box green" @click="onClickAction">{{ btntxt }}</button>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
const store = useBaseStore()
const show = defineModel<boolean>()
const i18n = useI18n()

const emits = defineEmits(['invitewheel:showshare'])

const examine = ref(0)

const progress = computed(() => {
    const rednum = store.redfissionInfo?.user?.rednum
    const needRealReward = store.redfissionInfo?.user?.needRealReward
    if (rednum == needRealReward) return '100%'
    return ((rednum / needRealReward) * 100).toFixed(2) + '%'
})

const cashoutgold = computed(() => {
    const rednum = store.redfissionInfo?.user?.rednum
    return store.curSymbol[store.wallet?.currency] + rednum.toFixed(2)
})

const btntxt = computed(() => {
    const canexamine = store.redfissionInfo?.user?.canexamine
    if (progress.value == '100%' && canexamine == 1) {
        return examine.value == 1 || examine.value == 2 ? i18n.t('under_review') : i18n.t('Claim')
    }
    return i18n.t('invite_wheel_cashout_share_btn_txt')
})

const onClickAction = () => {
    if (progress.value == '100%') {
        if (examine.value == 1 || examine.value == 2) return
        claimReward(true)
    } else {
        emits('invitewheel:showshare')
    }
}

const claimReward = async (isloading: boolean = false) => {
    try {
        const res = await store.actionEvent(
            {
                id: 'redfission',
                method: 'examine',
            },
            false,
            isloading
        )

        if (res.code === 200) {
            console.log('redfission examine', res)
            if (store.redfissionInfo?.user) {
                store.redfissionInfo.user.examine = res.examine
            }
            examine.value = res.examine || 0
            showSuccessToast(i18n.t('Application_submit'))
            return
        }
    } catch (e) {
        console.log(e)
    }

    showFailToast(i18n.t('message_claim_fail'))
}

const handleClose = () => {
    console.log('handleClose')
    show.value = false
}

onMounted(() => {
    examine.value = store.redfissionInfo?.user?.examine || 0
})
</script>
<style lang="scss" scoped>
.cashout-pop {
    width: 550px;
    height: 500px;
    padding: 30px;
    max-width: 100%;
    background-color: #2c3031;

    img {
        position: absolute;
        right: 0%;
        top: 0%;
        width: 100%;
    }

    .close {
        position: absolute;
        right: 25px;
        top: 25px;
        width: 46px;
        height: 46px;
        border-radius: 10px;
        background: url('@/assets/img/close.png') no-repeat center center;
        background-color: #ffffff1a;
        background-size: 13px 13px;
    }

    .cashout-gold {
        position: absolute;
        top: 55%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 36px;
        font-family: MicrosoftYaHei;
        font-weight: bold;
        color: #25d276;
    }

    .bar {
        flex: 1;
        position: relative;
        top: 66%;
        height: 14px;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 16px;
        overflow: hidden;

        .fill {
            height: 100%;
            border-radius: 16px 0 0 16px;
            background: linear-gradient(90deg, #089099, #01ff1a);
        }
    }

    .cashout-progress {
        position: relative;
        top: 70%;
        font-size: 26px;
        font-family: MicrosoftYaHei;
        font-weight: bold;
        color: #bec7ca;

        span {
            color: #f3df00;
        }
    }

    .action-buttons {
        position: relative;
        align-items: center;
        height: 90px;
        display: flex;
        top: 72%;
        align-items: center;
        justify-content: center;

        .btn-box {
            height: 60px;
            width: 100%;
            font-size: 23px;
            font-weight: bold;
            border-radius: 12px;
            cursor: pointer;
            color: #000;
            font-weight: 800;
            align-items: center;
            display: flex;
            justify-content: center;
        }

        .green {
            background-image: linear-gradient(90deg, #24ee89, #9fe871);
            box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
        }
    }
}
</style>
