<template>
    <van-overlay :show="boxshow" :lock-scroll="false" z-index="3001" :close-on-click-overlay="false">
        <div class="chest-game-container">
            <div class="close" @click="handleClose"></div>
            <img src="@/assets/img/invitewheel/title.avif" />

            <div class="chests-container">
                <div
                    v-for="(chest, index) in chests"
                    :key="index"
                    class="chest glowing"
                    :class="{ opened: chest.opened, selected: selectedIndex === index, shake: selectedIndex != -1 }"
                    @click="onClickOpenChest(index)"
                >
                    <img v-if="chest.opened" class="rotating-bg" src="@/assets/img/invitewheel/box5.avif" />
                    <div class="chest-opened" v-if="chest.opened">
                        <img class="chest-image" src="@/assets/img/invitewheel/box3.avif" />
                    </div>

                    <img v-if="!chest.opened" src="@/assets/img/invitewheel/box2.avif" class="chest-image" />
                    <span v-if="selectedIndex != -1" class="chest-text">{{ store.curSymbol[store.wallet?.currency] }} {{ chest.reward }} </span>
                </div>
            </div>

            <div class="action-buttons" v-if="selectedReward != 0">
                <button class="btn-box green" @click="clickreceived">{{ $t('invite_wheel_get_btn_txt') }}</button>
            </div>
        </div>
    </van-overlay>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import PopupManager from '@/utils/PopupManager'
import bus from '@/utils/bus'

const boxshow = ref(true)

const store = useBaseStore()

const chests = ref([
    { opened: false, reward: 0 },
    { opened: false, reward: 0 },
    { opened: false, reward: 0 },
    { opened: false, reward: 0 },
])
const selectedIndex = ref(-1)
const selectedReward = ref(0)

const handleClose = function () {
    PopupManager.checkQueue()
}
const onClickOpenChest = function (index) {
    if (selectedIndex.value !== -1) return
    console.log('onClickOpenChest', index, chests.value[index].opened)

    openRedChest()
    selectedIndex.value = index
    return
}

const openChest = function () {
    const redfissionInfo = store.redfissionInfo
    const adjustedArray = adjustArrayOrder(redfissionInfo.randomInit, redfissionInfo.user.initrednum, selectedIndex.value)
    for (let i = 0; i < adjustedArray.length && i < chests.value.length; i++) {
        chests.value[i].reward = adjustedArray[i]
    }
    chests.value[selectedIndex.value].opened = true
    selectedReward.value = chests.value[selectedIndex.value].reward
}

const openRedChest = async function (isloading: boolean = false) {
    const redfissionInfo = store.redfissionInfo
    if (redfissionInfo.status !== 1) return

    try {
        const res = await store.actionEvent(
            {
                id: 'redfission',
                method: 'openred',
            },
            false,
            isloading
        )

        if (res.code === 200) {
            console.log('redfission openred', res)
            //打开宝箱
            const { randomInit, rankList, redrecords, Configs, user } = res
            store.redfissionInfo = { Configs, status: undefined, user, rankList, redrecords, show: 1, randomInit }
            openChest()
        } else {
            selectedIndex.value = -1
        }
    } catch (e) {
        selectedIndex.value = -1
        console.log(e)
    }
}

const clickreceived = function () {
    // PopupManager.checkQueue()
    selectedIndex.value = -1
    bus.emit('activity', {
        param: 'invitepop',
    })
}

onMounted(() => {})

function adjustArrayOrder(randomInit: number[], initrednum: number, selectedIndex: number): number[] {
    const newArray = [...randomInit]
    const currentIndex = newArray.indexOf(initrednum)
    if (currentIndex === -1) {
        console.warn(`值 ${initrednum} 不在数组中`)
        return newArray
    }
    if (selectedIndex < 0 || selectedIndex >= newArray.length) {
        console.warn(`索引 ${selectedIndex} 超出数组范围`)
        return newArray
    }
    if (currentIndex === selectedIndex) {
        return newArray
    }
    newArray.splice(currentIndex, 1)
    newArray.splice(selectedIndex, 0, initrednum)
    return newArray
}
</script>
<style lang="scss" scoped>
.close {
    position: absolute;
    right: 15px;
    top: -60px;
    width: 35px;
    height: 35px;
    background: url('@/assets/img/close.png') no-repeat;
    background-size: 100% 100%;
}

.chest-game-container {
    text-align: center;
    font-family: Arial, sans-serif;
    padding: 20px;
    border-radius: 10px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 700px;
    height: 900px;
    transform: translate3d(-50%, -50%, 0);
}

.chests-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 30px;
    margin: 30px auto;
    max-width: 640px;
}

.chest {
    width: 300px;
    height: 300px;
    aspect-ratio: 1/1;
    border-radius: 5px;
    position: relative;
    cursor: pointer;
    transition: all 0.1s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    scale: 1;

    :deep(span) {
        position: absolute;
        top: 105%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 38px;
        font-weight: bold;
        background: linear-gradient(to bottom, #fff 20%, #efcc02 40%, #e4b20d 60%, #f6f6f4 80%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.3); /* 金属光泽增强 */
    }
}

.chest-opened {
    position: absolute;
    top: -40px;
}
.rotating-bg {
    scale: 0.8;
    top: -36px;
    left: -8px;
    // position: absolute;
    animation: rotateAnimation 8s linear infinite;
    transform-origin: center center;
}

@keyframes rotateAnimation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes gentleShake {
    0%,
    95% {
        transform: rotate(0deg) translateX(0);
    }
    96% {
        transform: rotate(-1deg) translateX(-2px);
    }
    97% {
        transform: rotate(1deg) translateX(2px);
    }
    98% {
        transform: rotate(-0.5deg) translateX(-1px);
    }
    99% {
        transform: rotate(0.5deg) translateX(1px);
    }
    100% {
        transform: rotate(0deg) translateX(0);
    }
}

.chest:not(.shake) .chest-image {
    animation: gentleShake 3s infinite;
    transform-origin: center bottom;
}

.chest.selected.opened .chest-image {
    scale: 1.15;
    margin-top: 20px;
}

/* 蓝色外发光效果 */
.chest.glowing {
    filter: drop-shadow(0 0 15px rgba(0, 136, 255, 0.8));
}

.chest.opened {
    filter: drop-shadow(0 0 28px rgba(138, 43, 226, 1));
}

.action-buttons {
    display: flex;
    align-items: center;
    height: 110px;
    display: flex;
    margin-top: 80px;
    align-items: center;
    justify-content: center;

    .btn-box {
        // flex: 1;
        height: 80px;
        width: 480px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 12px;
        cursor: pointer;
        color: #000;
        font-weight: 800;
        align-items: center;
        display: flex;
        justify-content: center;
        :deep(img) {
            width: 36px;
            height: 36px;
            margin-right: 10px;
        }
    }

    .green {
        background-image: linear-gradient(90deg, #24ee89, #9fe871);
        box-shadow: 0 0 12px rgba(35, 238, 136, 0.3), inset 0 -4px #1dca6a;
    }
}
</style>
