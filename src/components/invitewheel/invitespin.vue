<template>
    <div class="turntable-container">
        <div class="turntable" :style="turntableStyle" @transitionend="onTransitionEnd">
            <div class="turntable-bg"></div>
            <div v-for="(item, index) in props.prizes" :key="index" class="prize-item" :style="getPrizeStyle(index)">
                <img class="prize-image" :src="item.pic" :alt="item.pic" />
                <div class="prize-text">
                    <div class="prize-text-content">{{ getPrizeName(item) }}</div>
                </div>
            </div>
        </div>

        <div class="pointer"></div>
        <div class="invite-flygold" v-if="showflygold"><img src="@/assets/img/invitewheel/gold.webp" /></div>
        <div class="center-circle" @click="onClickCircle">
            <!-- <div v-if="props.freeTimes <= 0"><span>Invite</span></div>
            <div v-else> -->
            <span class="center-circle-txt">{{ 'X' + props.freeTimes }}</span>
            <div class="arrow">Free spin</div>
            <!-- </div> -->
        </div>

        <!-- <div class="controls">
            <button @click="startRotate" :disabled="isRotating">开始抽奖</button>
            <div v-if="result" class="result">恭喜获得: {{ result }}</div>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
const store = useBaseStore()
// const path = '../../assets/img/invitewheel/'
// const file = import.meta.glob('../../assets/img/invitewheel/*', { eager: true })

interface Prize {
    pic: string
    min: number
    max: number
    id: number
}

const props = defineProps<{
    prizes: Prize[]
    freeTimes: number
}>()

const emits = defineEmits(['invitewheel:rotate-end', 'invitewheel:rotate-start'])
const rotateDegrees = ref(0) // 当前旋转角度
const isRotating = ref(false) // 是否正在旋转
const result = ref('') // 抽奖结果
const rotateTime = ref(4) // 旋转时间(秒)
const shouldNormalize = ref(false)

const showflygold = ref(false)

const getPrizeName = (prize) => {
    const { min, max } = prize
    const currency = store.curSymbol[store.wallet?.currency]
    return min === max ? currency + min.toFixed(2) : `${currency}${min.toFixed(2)} - ${max.toFixed(2)}`
}

const onClickCircle = function () {
    if (isRotating.value) return
    emits('invitewheel:rotate-start')
}

const startRotate = function (targetIndex: number = 1) {
    if (isRotating.value) return

    isRotating.value = true
    result.value = ''
    const circles = 5 + Math.floor(Math.random() * 5)
    const currentNormalizedDegrees = rotateDegrees.value % 360
    const targetDegrees = 360 * circles + (360 - targetIndex * 45) - currentNormalizedDegrees + 360 + 45

    rotateDegrees.value += targetDegrees
}
const onTransitionEnd = function () {
    const currentIndex = calculateCurrentPrizeIndex()
    shouldNormalize.value = true
    rotateDegrees.value = rotateDegrees.value % 360
    showflygold.value = true
    setTimeout(() => {
        showflygold.value = false
    }, 1300)
    setTimeout(() => {
        isRotating.value = false
        shouldNormalize.value = false

        emits('invitewheel:rotate-end', currentIndex)
    }, 100)
}
const calculateCurrentPrizeIndex = function () {
    // 计算当前指向的奖品索引
    const normalizedDegrees = ((360 - (rotateDegrees.value % 360)) % 360) + 45
    return Math.floor(normalizedDegrees / 45) % 8
}

// 计算样式，处理归一化时不带过渡效果
const turntableStyle = computed(() => ({
    transform: `rotate(${rotateDegrees.value}deg)`,
    transition: shouldNormalize.value ? 'none' : `transform ${rotateTime.value}s ease-out`,
}))

const getPrizeStyle = (index) => {
    return {
        transform: `rotate(${index * 45}deg)`,
    }
}

defineExpose({
    startRotate,
})
</script>

<style scoped>
.turntable-container {
    position: relative;
    width: 700px;
    height: 1850px;
    margin: 0 auto;
}

.turntable {
    position: relative;
    width: 700px;
    height: 700px;
    border-radius: 50%;
    overflow: hidden;
    /* box-shadow: 0 0 10px rgba(0, 0, 0, 1); */
}

.turntable-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('@/assets/img/invitewheel/spin8.avif');
    background-size: 100% 100%;
    border-radius: 50%;
}

.prize-item {
    position: absolute;
    width: 50%;
    height: 50%;
    left: 0;
    top: 0;
    transform-origin: right bottom;
    box-sizing: border-box;
}

.prize-image {
    position: relative;
    object-fit: cover;
    transform: translate(-50%, -50%);
    width: 30%;
    height: 30%;
    top: 33%;
    left: 33%;
    /* scale: 0.3; */
    transform: rotate(-45deg);
}

.prize-text {
    position: absolute;
    width: 52%;
    height: 10%;
    top: 180px;
    left: 110px;
    text-align: center;
    font-weight: bold;
    transform: rotate(-45deg);
    z-index: 100;
    color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    .prize-text-content {
        white-space: nowrap;
        min-width: 110px;
        border: #000 3px solid;
        line-height: 21px;
        border-radius: 99px;
        padding: 6px 5px 4px 5px;
        font-size: 20px;
        position: absolute;
        font-weight: 400;
        background-color: #fff;
    }
}

.invite-flygold {
    position: absolute;
    transform: rotate(-90deg);
    top: -100px;
    img {
        display: inline-block;
    }
}

.center-circle {
    position: absolute;
    width: 320px;
    height: 320px;
    border-radius: 50%;
    top: 334px;
    left: 350px;
    background-image: url('@/assets/img/invitewheel/spin3.png');
    background-size: 100% 100%;
    transform: translate(-50%, -50%);

    z-index: 10;
    color: #5a3d1c;

    .center-circle-txt {
        box-sizing: border-box;
        line-height: 1;

        position: absolute;
        cursor: pointer;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-weight: 800;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
            sans-serif;
        font-size: 99px;
    }
    .arrow {
        white-space: nowrap;
        position: absolute;
        font-size: 30px;
        top: 72%;
        left: 50%;
        font-weight: 800;
        transform: translate(-50%, -50%);
    }
}

.pointer {
    position: absolute;
    top: -66px;
    left: 50.4%;
    transform: translateX(-50%);
    width: 304px;
    height: 325px;
    background-image: url('@/assets/img/invitewheel/spin7.png');
    background-size: 100% 100%;
}

.controls {
    margin-top: 20px;
    text-align: center;
}

button {
    padding: 8px 20px;
    background: #ff0000;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.result {
    margin-top: 10px;
    font-weight: bold;
    color: #ff0000;
    font-size: 18px;
}
</style>
