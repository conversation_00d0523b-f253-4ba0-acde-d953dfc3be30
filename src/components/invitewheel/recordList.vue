<template>
    <div class="vip-table-container">
        <div class="vip-table-wrapper" ref="tableWrapper">
            <table class="vip-table">
                <thead>
                    <tr>
                        <th v-for="(item, index) in tableConfig" :key="index" v-html="$t(item.title)"></th>
                    </tr>
                </thead>

                <tbody>
                    <tr v-for="(item, index) in redrecords" :key="index">
                        <td :style="{ color: parseItem(item).cheat == 1 ? 'red' : 'green' }">
                            {{ parseItem(item).cheat == 1 ? $t('Rejected') : $t('Received') }}
                        </td>
                        <td>{{ store.curSymbol[store.wallet?.currency] + (parseItem(item).gold || 0).toFixed(2) }}</td>
                        <td>{{ dayjs(parseItem(item).time).format('YYYY-MM-DD HH:mm:ss') }}</td>
                        <!-- <td>{{ parseItem(item).cheat == 1 ? 'Yes' : '-' }}</td> -->
                    </tr>
                </tbody>
            </table>
        </div>
        <van-empty v-if="!redrecords.length" description="" />
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import dayjs from 'dayjs'
// import { storeToRefs } from 'pinia'

const tableWrapper = ref(null)

const store = useBaseStore()
const redrecords = ref([])
// const { vipConfig } = storeToRefs(store)

// const vipconfigs = computed(() => {
//     return vipConfig.value
// })

const tableConfig = computed(() => {
    return [
        {
            title: 'Status',
        },
        {
            title: 'Amount',
            tip: '',
        },
        {
            title: 'Review Time',
            tip: '',
        },
        // {
        //     title: 'Details',
        //     tip: '',
        // },
    ]
})

// const formatNumber = function (num) {
//     return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
// }

const parseItem = function (item) {
    try {
        const data = JSON.parse(item)
        return data
    } catch (error) {
        console.log(error)
    }
    return {}
}

onMounted(() => {
    const records = store.redfissionInfo.redrecords
    redrecords.value = records
})

onBeforeUnmount(() => {})
</script>

<style scoped>
.vip-table-container {
    width: 95%;
    overflow-y: auto;
    padding-bottom: 100px;
    border-radius: 10px;
    padding-top: calc(var(--menu-header) + 30px);
}

.vip-table-wrapper {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    margin-bottom: 8px;
    border-radius: 10px;
}

.vip-table-wrapper::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.vip-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 600px;
    background-color: #232626;
    color: rgba(255, 255, 255, 0.9);
}

.vip-table th,
.vip-table td {
    font-size: 24px;
    padding: 12px 16px;
    text-align: center;
    /* border: 1px solid #e0e0e0; */
    white-space: nowrap;
    font-weight: 600;
}

.vip-table th {
    background-color: rgb(50, 55, 56);
    position: sticky;
    top: 0;
}

.vip-table tr:nth-child(even) {
    background-color: rgb(50, 55, 56);
}
</style>
