<template>
    <div class="header">
        <slot v-if="custom"></slot>
        <template v-else>
            <div v-if="showBack" class="header-back" @click="handleBack"></div>
            <slot v-if="$slots.title" name="title"></slot>
            <div v-else class="header-title">{{ title ? $t(title) : $t(route.meta.title) }}</div>
            <slot v-if="$slots.right" name="right"></slot>
            <div v-else-if="showRight" class="header-right"></div>
        </template>
    </div>
</template>
<script setup lang="ts" name="headers">
defineProps({
    custom: {
        type: Boolean,
        default: () => false,
    },
    title: {
        type: String,
        default: () => '',
    },
    showBack: {
        type: Boolean,
        default: () => true,
    },
    showRight: {
        type: Boolean,
        default: () => true,
    },
})

const router = useRouter()
const route = useRoute()

const handleBack = () => {
    // if (window.history && !window.history.state.back) {
    //     router.replace('/')
    // } else {
    router.back()
    // }
}
</script>
<style scoped lang="scss">
.header {
    @apply flex items-center;
    // box-sizing: border-box;
    position: relative;
    z-index: 101;
    height: var(--header-height);
    padding: 0 20px;
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    border-bottom: 2px solid #3d3b38;

    .header-back,
    .header-right {
        min-width: 60px;
        height: var(--header-height);
    }
    .header-back {
        background: url('@/assets/img/header/back.png') no-repeat center center;
        background-size: 22px 40px;
    }
    .header-title {
        flex: 1;
        text-align: center;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
    }
}
</style>
