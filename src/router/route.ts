import { RouteRecordRaw } from 'vue-router'

/**
 * 建议：路由 path 路径与文件夹名称相同，找文件可浏览器地址找，方便定位文件位置
 *
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空 2、isIframe:false`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      permissions：   当前路由权限标识，控制路由权限
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

// 扩展 RouteMeta 接口
declare module 'vue-router' {
    interface RouteMeta {
        title?: string
        isLink?: string
        isHide?: boolean
        isKeepAlive?: boolean
        isIframe?: boolean
        roles?: string[]
        permissions?: string[]
        icon?: string
    }
}
const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: 'home',
        component: () => import('@/views/newHome/index.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/video',
        name: 'video',
        component: () => import('@/views/videos/index.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/jackpot',
        name: 'jackpot',
        component: () => import('@/components/jackpot/MorePage.vue'),
        meta: {
            title: 'Jackpot',
        },
    },
    {
        path: '/earnCash',
        name: 'earnCash',
        component: () => import('@/views/earnCash/index.vue'),
        meta: {
            title: 'earn',
        },
    },
    {
        path: '/earn-rule',
        name: 'earnRule',
        component: () => import('@/views/earnCash/rule/index.vue'),
        meta: {
            title: 'earnMoney_rule_title',
        },
    },

    {
        path: '/payGuide',
        name: 'payGuide',
        component: () => import('@/views/payGuide/index.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/vip',
        name: 'vipmain',
        component: () => import('@/views/vipNew/index.vue'),
        //component: () => import('@/views/vipNew/VIPBenefitsList.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/vipconfig',
        name: 'vipconfig',
        component: () => import('@/views/vip/index.vue'),
        //component: () => import('@/views/vipNew/VIPBenefitsList.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/wallets',
        name: 'wallets',
        component: () => import('@/views/wallets/index.vue'),
        meta: {
            title: 'Home_Wallets',
        },
    },
    {
        path: '/cashout',
        name: 'cashout',
        component: () => import('@/views/withDraw/index.vue'),
        meta: {
            title: 'Withdrawal',
        },
    },
    {
        path: '/security',
        name: 'security',
        component: () => import('@/views/security/index.vue'),
        meta: {
            title: 'Security_Center',
        },
    },
    {
        path: '/notice',
        name: 'notice',
        component: () => import('@/views/notice/index.vue'),
        meta: {
            title: 'Home_events',
        },
    },
    {
        path: '/email',
        name: 'email',
        component: () => import('@/views/email/index.vue'),
        meta: {
            title: '绑定邮箱',
        },
    },
    {
        path: '/setting',
        name: 'setting',
        component: () => import('@/views/setting/index.vue'),
        meta: {
            title: 'Settings',
        },
    },
    {
        path: '/language',
        name: 'language',
        component: () => import('@/views/language/index.vue'),
        meta: {
            title: 'Language_settings',
        },
    },
    {
        path: '/profile',
        name: 'profile',
        component: () => import('@/views/profile/index.vue'),
        meta: {
            title: 'Personal_page',
        },
    },

    {
        path: '/message',
        name: 'message',
        component: () => import('@/views/message/index.vue'),
        meta: {
            title: 'Chat_interface_title',
        },
    },
    {
        path: '/chat',
        name: 'chat',
        component: () => import('@/views/chat/index.vue'),
        meta: {
            title: '',
        },
    },
    {
        path: '/report',
        name: 'report',
        component: () => import('@/views/report/index.vue'),
        meta: {
            title: 'Report',
        },
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/index.vue'),
        meta: {
            title: 'Login_button',
        },
    },
    {
        path: '/events',
        name: 'events',
        component: () => import('@/views/events/index.vue'),
        meta: {
            title: 'Home_events',
        },
    },
    {
        path: '/check',
        name: 'check',
        component: () => import('@/views/checkIn/index.vue'),
        meta: {
            title: 'Sign_in',
        },
    },
    {
        path: '/iframe',
        name: 'iframe',
        component: () => import('@/views/iframe/index.vue'),
        meta: {
            title: 'Game',
        },
    },
    {
        path: '/bindInviter',
        name: 'bindInviter',
        component: () => import('@/views/binInviter/index.vue'),
        meta: {
            title: 'bindInviter',
        },
    },
    {
        path: '/bindPhone',
        name: 'bindPhone',
        component: () => import('@/views/bindPhone/index.vue'),
        meta: {
            title: 'bindPhone',
        },
    },
    {
        path: '/rechargeRebate',
        name: 'rechargeRebate',
        component: () => import('@/views/rechargeRebate/index.vue'),
        meta: {
            title: 'Recharge_rebate',
        },
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('@/views/register/index.vue'),
        meta: {
            title: '安装',
        },
    },
    {
        path: '/layout',
        name: 'layout',
        component: () => import('@/components/layout/normalLayout.vue'),
        children: [
            {
                path: '/loseEvent',
                name: 'loseEvent',
                component: () => import('@/views/loseEvent/index.vue'),
                meta: {
                    title: 'Never_lose_money',
                },
            },
        ],
    },
    {
        path: '/:path(.*)*',
        name: 'notFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
            title: '找不到此页面',
            isHide: true,
        },
    },
]
export default routes
