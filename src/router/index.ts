import { createRouter, createWebHashHistory } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { useBaseStore } from '@/stores'
// import { Session } from '@/utils/storage'
import { NO_ANIMATION, NOT_LOGIN } from '@/enums'
import routes from '@/router/route'
import i18n from '@/language'

export const router = createRouter({
    history: createWebHashHistory(),
    routes,
    // scrollBehavior(to, from, savedPosition) {
    //     // 返回的对象将会被传递给 window.scrollTo 方法
    //     console.log(to.meta.requiresScroll, 'to.meta.requiresScroll')
    //     if (to.meta.requiresScroll) {
    //         // 保存的位置，当用户点击浏览器后退按钮时可用

    //         return {
    //             el: '.tab-content',
    //             top: 300,
    //         }
    //     } else {
    //         // 滚动到页面顶部
    //         return { top: 0 }
    //     }
    // },
})
const showdialog = () => {
    showDialog({
        message: i18n.global.t('The data is expected to berefreshed. lf you cannot clickthe confirmation button below,please wait for 3-5 seconds'),
        className: 'common-dialog',
        showCancelButton: false,
        confirmButtonText: i18n.global.t('Confirm'),
    })
}
router.beforeResolve(async (to, from, next) => {
    const baseStore = useBaseStore()
    if (from.path === '/iframe') {
        if (baseStore.gameserver?.socket.connected) {
            try {
                const res = await baseStore.gameUnSubscribe()
                if (res.code === 200) {
                    if (res.bonus) {
                        baseStore.bonus = res.bonus
                    }
                    next()
                } else {
                    showdialog()
                    next(false)
                }
            } catch (e) {
                showdialog()
                next(false)
            }
        } else {
            next()
        }
    }
    next()
})
router.beforeEach(async (to, from, next) => {
    const baseStore = useBaseStore()
    if (NOT_LOGIN.includes(to.path)) {
        next()
        return
    }
    if (!baseStore.token) {
        next('/login')
        return
    }

    NProgress.configure({ showSpinner: false })
    if (to.meta.title) NProgress.start()
    if (NO_ANIMATION.indexOf(from.path) !== -1 && NO_ANIMATION.indexOf(to.path) !== -1) {
        return next()
    }

    const toDepth = routes.findIndex((v) => v.path === to.path)
    const fromDepth = routes.findIndex((v) => v.path === from.path)
    if (toDepth > fromDepth) {
        if (to.matched && to.matched.length) {
            const toComponentName = await to.matched[0].components?.default.name
            baseStore.updateExcludeNames({ type: 'remove', value: toComponentName })
        }
    } else {
        if (from.matched && from.matched.length) {
            const fromComponentName = await from.matched[0].components?.default.name
            baseStore.updateExcludeNames({ type: 'add', value: fromComponentName })
        }
    }

    next()
})

// 路由加载后
router.afterEach(() => {
    NProgress.done()
})

// 导出路由
export default router
