class EventEmitter {
    _events: object
    constructor() {
        this._events = {}
    }
    removeAllListeners() {
        // @ts-ignore
        this.off()
    }
    on(event, fn) {
        if (Array.isArray(event)) {
            for (let i = 0, l = event.length; i < l; i++) {
                this.on(event[i], fn)
            }
        } else {
            if (!this._events[event]) {
                this._events[event] = []
            }
            this._events[event].push(fn)
        }
        return this
    }
    once(event, fn) {
        function on() {
            this.off(event, on)
            fn.apply(this, arguments)
        }
        on.fn = fn
        this.on(event, on)
        return this
    }
    off(event, fn) {
        const vm = this
        // all
        if (!arguments.length) {
            vm._events = Object.create(null)
            return vm
        }
        // array of events
        if (Array.isArray(event)) {
            for (let i = 0, l = event.length; i < l; i++) {
                vm.off(event[i], fn)
            }
            return vm
        }
        // specific event
        const cbs = vm._events[event!]
        if (!cbs) {
            return vm
        }
        if (!fn) {
            vm._events[event!] = null
            return vm
        }
        // specific handler
        let cb
        let i = cbs.length
        while (i--) {
            cb = cbs[i]
            if (cb === fn || cb.fn === fn) {
                cbs.splice(i, 1)
                break
            }
        }
        return vm
    }
    emit(event) {
        const vm = this
        let cbs = vm._events[event]
        if (cbs) {
            const args = Array.from(arguments).slice(1)
            for (let i = 0, l = cbs.length; i < l; i++) {
                cbs[i].apply(vm, args)
            }
        }
        return vm
    }
}

export { EventEmitter }
