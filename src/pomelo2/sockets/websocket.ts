'use strict'

import { EventEmitter } from '../events'

declare interface Logger {
    trace(message: string, body?: object): void
    info(message: string, body?: object): void
    debug(message: string, body?: object): void
    warn(message: string, body?: object): void
    error(message: string, body?: object): void
    fatal(message: string, body?: object): void
}
enum STATUS {
    UNKNOWN = 0,
    CONNECTING = 1,
    OPEN = 2,
    CLOSING = 3,
    CLOSED = 4,
}
export class websocket extends EventEmitter {
    status: number = STATUS.UNKNOWN
    socket: WebSocket | undefined
    // logger: Logger
    // logger: Logger
    constructor() {
        super()
        // this.logger = logger
    }

    get connected() {
        if (!this.socket) {
            return false
        }
        return this.status === STATUS.OPEN
    }

    get connectting() {
        if (!this.socket) {
            return false
        }
        return this.status === STATUS.CONNECTING
    }

    async connect(uri: string, protocal?: string, cert?: any) {
        if (this.socket) {
            // this.logger.warn('socket already inuse', { uri, protocal })
            return this
        }

        // this.logger.trace('try to connect remote server', { uri, protocal })
        /// @ts-ignore
        this.socket = new WebSocket(uri, protocal, cert)
        this.status = STATUS.CONNECTING

        this.socket.binaryType = 'arraybuffer'
        this.socket.onmessage = (event) => {
            // this.logger.trace('receive message', {
            //     byteLength: event.data.byteLength,
            // })
            // console.log('websocket onmessage 时间接收到消息，未解密', event.data)
            this.emit('message', event.data)
        }

        this.socket.onerror = (err) => {
            // this.logger.error('web socket has error', { uri, err })
            this.emit('error', err)
        }

        this.socket.onopen = () => {
            this.status = STATUS.OPEN
            // this.logger.debug('websocket open success', { uri, protocal })
            this.emit('connecting')
        }

        this.socket.onclose = () => {
            this.status = STATUS.CLOSED
            // this.logger.debug('websocket was closed', { uri, protocal })
            this.emit('closed')
        }

        return this
    }

    async close(code: number, reason: string) {
        // this.logger.warn('socket colse', { code, reason })
        if (this.socket) {
            this.socket.close(1000, reason)
            delete this.socket
            this.socket = undefined
        }
    }

    async send(msg: Uint8Array) {
        if (this.socket && this.connected) {
            // this.logger.trace('send message', { size: msg.length })
            this.socket.send(msg.buffer)
            return
        }

        // this.logger.error('send message failed', { size: msg.length, message: msg.toString() })
        return Promise.reject('socket hunup!')
    }
}
