// 弹窗管理器
import { ref } from 'vue'
import { getAllImagesUrl, preloadImages } from '@/utils'
import EventBus from '@/utils/bus'
class PopupManager {
    queue: { component: any; params: any }[] = []
    currentPopup = ref<{ component: any; params: any } | null>(null)
    isChecking = false
    loaded: { [key: string]: boolean } = {}
    timer = null

    addPopup(component: any, params: any = {}) {
        this.queue.push({ component, params })
    }
    insertPopup(component: any, params: any = {}) {
        this.queue.unshift({ component, params })
    }
    clearQueue() {
        this.queue = []
        this.isChecking = false
    }
    startCheck() {
        if (this.isChecking) {
            return
        }
        this.isChecking = true
        this.checkQueue()
    }
    stopCheck() {
        this.clearQueue()
        this.closeCurrentPopup()
    }
    delayCheckQueue() {
        const handleCb = () => {
            if (this.timer) {
                this.timer && clearTimeout(this.timer)
                this.timer = null
                this.clearQueue()
                EventBus.emit('checkPoint')
            }
            document.removeEventListener('click', handleCb, true)
        }
        document.addEventListener('click', handleCb, true)
        this.timer = setTimeout(() => {
            this.timer = null
            document.removeEventListener('click', handleCb, true)
            this.checkQueue()
        }, 1000)
    }

    checkQueue(isnext = true) {
        console.trace('CheckDialog---- checkQueue', this.queue.length, this.isChecking)

        if (this.queue.length === 0) {
            this.isChecking = false
            this.currentPopup.value = null
            EventBus.emit('checkDialogEnd')
            return
        }
        const nextPopup = this.queue.shift()
        if (nextPopup && isnext) {
            // const name = nextPopup.component.__name
            // if (this.loaded[name]) {
            this.currentPopup.value = nextPopup
            // } else {
            //     this.loadImgs(name).then(() => {
            //         this.currentPopup.value = nextPopup
            //     })
            // }
        } else {
            this.stopCheck()
            EventBus.emit('checkDialogEnd')
        }
    }

    closeCurrentPopup() {
        this.currentPopup.value = null
        this.checkQueue(false)
    }
    loadImgs(name) {
        return this.preload(name, getAllImagesUrl(name))
    }
    preload(name, imagePaths = {}) {
        return preloadImages(Object.values(imagePaths)).then(() => {
            this.loaded[name] = true
        })
    }
}

export default new PopupManager()
