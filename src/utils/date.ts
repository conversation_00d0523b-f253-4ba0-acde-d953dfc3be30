/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-26 17:59:39
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-14 16:19:56
 * @FilePath     : /src/utils/date.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-26 17:59:39
 */

import dayjs, { Dayjs } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import relativeTime from 'dayjs/plugin/relativeTime'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import duration from 'dayjs/plugin/duration'

// 扩展插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(relativeTime)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(duration)

export type DateInput = string | number | Date | Dayjs

// 常用格式常量
export const DATE_FORMATS = {
    DATE: 'YYYY-MM-DD',
    TIME: 'HH:mm:ss',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    DATETIME_SHORT: 'YYYY-MM-DD HH:mm',
    MONTH_DAY: 'MM-DD',
    YEAR_MONTH: 'YYYY-MM',
    TIME_SHORT: 'HH:mm',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss.SSS',
} as const

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @param useUtc 是否使用 UTC 时间（用于处理服务器时间，避免时区转换），默认 false
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: DateInput, format: string = DATE_FORMATS.DATETIME, useUtc: boolean = false): string {
    if (!date) return ''
    if (useUtc) {
        return dayjs(date).utc().format(format)
    }
		return dayjs(date).format(format)
}

/**
 * 格式化为日期（不含时间，可自定义分隔符）
 * @param date 日期
 * @param separator 分隔符，默认为 '-'
 */
export function formatDateOnly(date: DateInput, separator: string = '-'): string {
    const format = `YYYY${separator}MM${separator}DD`
    return formatDate(date, format)
}

/**
 * 格式化为时间（不含日期）
 */
export function formatTimeOnly(date: DateInput): string {
    return formatDate(date, DATE_FORMATS.TIME)
}

/**
 * 格式化为短时间
 */
export function formatTimeShort(date: DateInput): string {
    return formatDate(date, DATE_FORMATS.TIME_SHORT)
}

/**
 * 格式化为月日（可自定义分隔符）
 * @param date 日期
 * @param separator 分隔符，默认为 '-'
 */
export function formatMonthDay(date: DateInput, separator: string = '-'): string {
    const format = `MM${separator}DD`
    return formatDate(date, format)
}

/**
 * 格式化为相对时间（如：2小时前）
 */
export function formatRelativeTime(date: DateInput): string {
    return dayjs(date).fromNow()
}

/**
 * 获取当前时间戳
 */
export function getNow(): number {
    return dayjs().valueOf()
}

/**
 * 获取当前日期字符串
 */
export function getNowString(format: string = DATE_FORMATS.DATETIME): string {
    return dayjs().format(format)
}

/**
 * 获取今天开始时间
 */
export function getStartOfDay(date?: DateInput): Dayjs {
    return dayjs(date).startOf('day')
}

/**
 * 获取今天结束时间
 */
export function getEndOfDay(date?: DateInput): Dayjs {
    return dayjs(date).endOf('day')
}

/**
 * 获取本周开始时间
 */
export function getStartOfWeek(date?: DateInput): Dayjs {
    return dayjs(date).startOf('week')
}

/**
 * 获取本月开始时间
 */
export function getStartOfMonth(date?: DateInput): Dayjs {
    return dayjs(date).startOf('month')
}

/**
 * 判断是否是今天
 */
export function isToday(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否是昨天
 */
export function isYesterday(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否是明天
 */
export function isTomorrow(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs().add(1, 'day'), 'day')
}

/**
 * 判断是否是本周
 */
export function isThisWeek(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否是本月
 */
export function isThisMonth(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 判断是否是本年
 */
export function isThisYear(date: DateInput): boolean {
    return dayjs(date).isSame(dayjs(), 'year')
}

/**
 * 计算两个日期之间的差值
 * @param date1 日期1
 * @param date2 日期2，默认为当前时间
 * @param unit 单位（'day', 'hour', 'minute', 'second'等）
 */
export function getDateDiff(date1: DateInput, date2: DateInput = dayjs(), unit: dayjs.OpUnitType = 'day'): number {
    return dayjs(date1).diff(dayjs(date2), unit)
}

/**
 * 添加时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位
 */
export function addTime(date: DateInput, amount: number, unit: dayjs.ManipulateType): Dayjs {
    return dayjs(date).add(amount, unit)
}

/**
 * 减去时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位
 */
export function subtractTime(date: DateInput, amount: number, unit: dayjs.ManipulateType): Dayjs {
    return dayjs(date).subtract(amount, unit)
}

/**
 * 判断日期是否在指定范围内
 * @param date 要检查的日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 */
export function isDateInRange(date: DateInput, startDate: DateInput, endDate: DateInput): boolean {
    const checkDate = dayjs(date)
    return checkDate.isSameOrAfter(dayjs(startDate)) && checkDate.isSameOrBefore(dayjs(endDate))
}

/**
 * 获取倒计时文本
 * @param targetDate 目标日期
 * @returns 倒计时文本，如：'2天 3小时 45分钟'
 */
export function getCountdownText(targetDate: DateInput): string {
    const now = dayjs()
    const target = dayjs(targetDate)
    const diff = target.diff(now)

    if (diff <= 0) return '已结束'

    const duration = dayjs.duration(diff)
    const days = Math.floor(duration.asDays())
    const hours = duration.hours()
    const minutes = duration.minutes()
    const seconds = duration.seconds()

    const parts: string[] = []
    if (days > 0) parts.push(`${days}天`)
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (parts.length === 0 && seconds > 0) parts.push(`${seconds}秒`)

    return parts.join(' ')
}

/**
 * 获取倒计时毫秒数
 * @param targetDate 目标日期
 * @returns 剩余毫秒数，如果已过期返回0
 */
export function getCountdownMs(targetDate: DateInput): number {
    const diff = dayjs(targetDate).diff(dayjs())
    return Math.max(0, diff)
}

/**
 * 计算两个日期之间的毫秒差值
 * @param endDate 结束时间
 * @param startDate 开始时间
 * @returns 毫秒差值，如果结束时间早于开始时间返回0
 */
export function getTimeDiffMs(endDate: DateInput, startDate: DateInput): number {
    const diff = dayjs(endDate).diff(dayjs(startDate))
    return Math.max(0, diff)
}

/**
 * 智能日期显示
 * 根据日期远近显示不同格式：
 * - 今天：显示时间
 * - 昨天：显示"昨天 HH:mm"
 * - 本年：显示"MM-DD HH:mm"
 * - 其他：显示"YYYY-MM-DD HH:mm"
 */
export function smartDateFormat(date: DateInput): string {
    const dateObj = dayjs(date)
    const now = dayjs()

    if (dateObj.isSame(now, 'day')) {
        return dateObj.format('HH:mm')
    } else if (dateObj.isSame(now.subtract(1, 'day'), 'day')) {
        return `昨天 ${dateObj.format('HH:mm')}`
    } else if (dateObj.isSame(now, 'year')) {
        return dateObj.format('MM-DD HH:mm')
    } else {
        return dateObj.format('YYYY-MM-DD HH:mm')
    }
}

/**
 * 时区转换
 * @param date 日期
 * @param timezone 目标时区
 */
export function convertTimezone(date: DateInput, timezone: string): Dayjs {
    return dayjs(date).tz(timezone)
}

/**
 * 获取本地时区
 */
export function getLocalTimezone(): string {
    return dayjs.tz.guess()
}

/**
 * 解析日期字符串
 * @param dateString 日期字符串
 * @param format 格式
 */
export function parseDate(dateString: string, format?: string): Dayjs {
    return format ? dayjs(dateString, format) : dayjs(dateString)
}

/**
 * 验证日期字符串是否有效
 */
export function isValidDate(date: DateInput): boolean {
    return dayjs(date).isValid()
}

// 导出 dayjs 实例，方便直接使用
export { dayjs }
export default dayjs
