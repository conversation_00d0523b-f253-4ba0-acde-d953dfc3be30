import EVT from '@/utils/bus'

const Google_Container_Id = 'google-signin-button'

//Google登录成功返回数据
export interface GoogleLoginResponse {
    credential?: string //一键式登录返回
    access_token?: string //手动登录返回
}

//登录回调
// type LoginCallback = (resp: LoginData) => void

//登录数据
export interface LoginData {
    status: 'success' | 'failed'
    id?: number
    name?: string
    from: 'facebook' | 'google'
    avatar?: string
    accesstoken?: string
    client_id?: string //用于Google
}

// 全局声明checkLoginState函数，用于Facebook登录按钮回调
declare global {
    interface Window {
        checkLoginState: () => void
    }
}

export namespace H5Login {
    export const Google_Client_Id = '352509932096-qprle5pi9h9vcpk1is8q11tl8so40v84.apps.googleusercontent.com'
    export const FB_App_Id = '1028570359224106'
    export const H5_Login_Back = 'H5_Login_Back'
    export const GOOGLE_USER_LOGIN = 'GOOGLE_USER_LOGIN'
    export const FB_USER_LOGIN = 'FB_USER_LOGIN'

    export function init() {
        initGoogleH5Login()
        initFacebookH5Login()
    }

    //-----------------google login-----------------
    //解析Google token
    export function decodeJwtResponse(token: string) {
        console.log('decodeJwtResponse--', token)

        let base64Url = token.split('.')[1]
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
        let jsonPayload = decodeURIComponent(
            window
                .atob(base64)
                .split('')
                .map(function (c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
                })
                .join('')
        )
        console.log('jsonPayload--', jsonPayload)

        return JSON.parse(jsonPayload)
    }

    //添加Google登录按钮
    export function addGoogleLoginButton() {
        let divElement = document.getElementById(Google_Container_Id)
        if (!divElement) {
            const div = document.createElement('div')
            div.id = Google_Container_Id
            div.className = 'g_id_signin'
            div.setAttribute('data-type', 'standard')
            div.setAttribute('data-shape', 'rectangular')
            div.setAttribute('data-theme', 'filled_blue')
            div.setAttribute('data-text', 'signin_with')
            div.setAttribute('data-size', 'large')
            div.setAttribute('data-logo_alignment', 'left')

            document.body.appendChild(div)
            return div
        }
        return divElement
    }
    function handleGoogleSignIn(response: GoogleLoginResponse) {
        // 处理Google登录成功的响应
        const responsePayload = decodeJwtResponse(response.credential)
        const loginData: LoginData = {
            from: 'google',
            status: 'success',
            id: responsePayload.sub,
            name: responsePayload.name,
            accesstoken: response.credential,
            client_id: Google_Client_Id,
            avatar: responsePayload.picture,
        }
        EVT.emit(H5_Login_Back, loginData) //成功登录通知
    }

    /** 初始化GoogleH5登录
     */
    export function initGoogleH5Login() {
        const script = document.createElement('script')
        const handleGoogleSignInCancel = () => {
            // 处理Google登录取消
            const loginData: LoginData = {
                from: 'google',
                status: 'failed',
                client_id: Google_Client_Id,
            }
            EVT.emit(H5_Login_Back, loginData)
        }

        const initializeGoogleSignIn = (client_id: string) => {
            // 确保按钮容器存在
            const buttonContainer = document.getElementById(Google_Container_Id) //addGoogleLoginButton()
            if (!buttonContainer) {
                console.error('Google login button container not found.')
                return
            }

            //@ts-ignore
            window.google.accounts?.id.initialize({
                client_id,
                use_fedcm_for_prompt: false,
                cancel_on_tap_outside: true, // 控制是否在提示之外进行点击时取消提示(关闭一键登录弹窗)，默认true
                auto_select: false, // 开启自动登录功能，默认false
                callback: handleGoogleSignIn, // 验证成功回调
                cancel: handleGoogleSignInCancel,
            })

            //@ts-ignore
            window.google.accounts?.id.renderButton(buttonContainer, {
                type: 'standard',
                theme: 'filled_white',
                size: 'large',
                shape: 'rectangular',
                text: 'signin_with',
                logo_alignment: 'left',
                width: 250, // 明确指定宽度
            })
        }

        //@ts-ignore
        if (!window.google) {
            script.src = 'https://accounts.google.com/gsi/client' // 加载客户端库
            script.async = true
            script.defer = true
            script.onload = () => {
                initializeGoogleSignIn(Google_Client_Id)
            }

            document.head.appendChild(script)
        } else {
            // 如果Google客户端库已加载，直接初始化
            initializeGoogleSignIn(Google_Client_Id)
        }
    }

    // 启用一键登录提示(弹窗)功能
    export function enableGoogleSignInPrompt() {
        // @ts-ignore
        window.google?.accounts.id.prompt((notification) => {
            console.log(notification, 'notification')
            console.log('notification.isNotDisplayed()--', notification.isNotDisplayed())
            console.log('notification.isSkippedMoment()--', notification.isSkippedMoment())
            // if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // @ts-ignore
            var divElement = document.querySelector('.g_id_signin')
            var buttonElement = divElement.querySelector('[role="button"]')
            if (buttonElement) {
                // @ts-ignore
                buttonElement.click() //有阻止弹出式弹窗的浏览器，此处无效
            } else {
                //@ts-ignore
                const client = window.google.accounts.oauth2.initTokenClient({
                    client_id: Google_Client_Id,
                    scope: 'openid email profile',
                    callback: getUserInfo,
                })
                client.requestAccessToken()
            }
        })
    }

    async function getUserInfo(response: GoogleLoginResponse) {
        let accessToken = response.access_token
        let userInfo = null
        try {
            const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            })

            if (response.ok) {
                userInfo = await response.json()
                const loginData: LoginData = {
                    from: 'google',
                    status: 'success',
                    id: userInfo.id,
                    name: userInfo.name,
                    accesstoken: accessToken,
                    client_id: '',
                }
                EVT.emit(H5_Login_Back, loginData)
                return
            } else {
                console.error('Failed to get user info:', response.status)
            }
        } catch (error) {
            console.error('Error getting user info:', error)
        }
        const loginData: LoginData = {
            from: 'google',
            status: 'failed',
            client_id: Google_Client_Id,
        }
        EVT.emit(H5_Login_Back, loginData)
    }

    //-----------------facebook login-----------------
    function initFacebookH5Login() {
        const initfb = function () {
            // 获取当前URL，检查是否是ngrok URL
            // const currentUrl = window.location.href;
            // const isNgrok = currentUrl.includes('ngrok');
            // console.log('Facebook SDK 初始化，当前URL:', currentUrl);

            //@ts-ignore
            FB.init({
                appId: FB_App_Id,
                xfbml: true,
                version: 'v20.0',
                cookie: true,
                // 如果是ngrok URL，设置status为true以便更好地调试
                // status: isNgrok,
            })

            // 添加全局回调函数，用于Facebook登录按钮
            window.checkLoginState = function () {
                // 检查登录状态
                //@ts-ignore
                FB.getLoginStatus(function (response) {
                    handleFacebookLoginStatus(response)
                })
            }

            // 确保Facebook按钮被正确渲染
            //@ts-ignore
            // FB.XFBML.parse();
        }
        //@ts-ignore
        if (window.FB) {
            console.log('Facebook SDK 已经加载，直接初始化')

            initfb()
        } else {
            // 如果FB对象不存在，加载Facebook SDK
            const script = document.createElement('script')
            script.src = 'https://connect.facebook.net/en_US/sdk.js'
            script.async = true
            script.onload = () => {
                initfb()
            }

            document.head.appendChild(script)
        }
    }

    /**
     * 处理Facebook登录状态
     * 用于处理Facebook登录按钮的回调
     */
    function handleFacebookLoginStatus(response: { status: string; authResponse?: { accessToken: string } }) {
        if (response.status === 'connected' && response.authResponse) {
            // 用户已登录Facebook并授权应用
            //@ts-ignore
            FB.api('/me', { fields: 'name, id, picture' }, function (userInfo: { picture?: { data: { url: string } }; id: number; name: string }) {
                let avatar = userInfo.picture ? userInfo.picture.data.url : ''
                const loginData: LoginData = {
                    from: 'facebook',
                    status: 'success',
                    id: userInfo.id,
                    name: userInfo.name,
                    avatar: avatar,
                    accesstoken: response.authResponse.accessToken,
                }
                EVT.emit(H5_Login_Back, loginData)
                //@ts-ignore
                FB.logout(function () {})
            })
        } else {
            // 用户未登录或未授权应用
            const loginData: LoginData = {
                from: 'facebook',
                status: 'failed',
            }
            EVT.emit(H5_Login_Back, loginData)
        }
    }

    /**
     * facebook登录
     * @param successCallback
     * @param cancelCallback
     */
    export function loginWithFacebook() {
        //@ts-ignore
        FB.login(
            function (resp: { authResponse: { accessToken: string } }) {
                if (resp.authResponse) {
                    //@ts-ignore
                    FB.api(
                        '/me',
                        { fields: 'name, id, picture' },
                        function (response: { picture?: { data: { url: string } }; id: number; name: string }) {
                            let avatar = response.picture ? response.picture.data.url : ''
                            const loginData: LoginData = {
                                from: 'facebook',
                                status: 'success',
                                id: response.id,
                                name: response.name,
                                avatar: avatar,
                                accesstoken: resp.authResponse.accessToken,
                            }
                            EVT.emit(H5_Login_Back, loginData)
                            //@ts-ignore
                            FB.logout(function () {})
                        }
                    )
                } else {
                    const loginData: LoginData = {
                        from: 'facebook',
                        status: 'failed',
                    }
                    EVT.emit(H5_Login_Back, loginData)
                }
            },
            { scope: 'public_profile' }
        )
    }
    // facebook分享
    export function FBShare(params) {
        return new Promise((resolve) => {
            //@ts-ignore
            FB.ui(
                {
                    method: 'share',
                    ...params,
                },
                function (response) {
                    resolve(response)
                }
            )
        })
    }
}
