/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-09 12:13:42
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-11 11:57:32
 * @FilePath     : /src/utils/bus.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-09 12:13:42
 */
// https://www.npmjs.com/package/mitt
import mitt from 'mitt'

// 导出
export default mitt()

export const EVENT_KEY = {
    SINGLE_CLICK: 'SINGLE_CLICK',
    DOUBLE_CLICK: 'DOUBLE_CLICK',
    SINGLE_CLICK_BROADCAST: 'SINGLE_CLICK_BROADCAST',
    ENTER_FULLSCREEN: 'ENTER_FULLSCREEN',
    EXIT_FULLSCREEN: 'EXIT_FULLSCREEN',
    TOGGLE_FULLSCREEN: 'TOGGLE_FULLSCREEN',
    TOGGLE_COMMENT: 'TOGGLE_COMMENT',
    OPEN_COMMENTS: 'OPEN_COMMENTS',
    CL<PERSON>E_COMMENTS: 'CLOSE_COMMENTS',
    DIALOG_MOVE: 'DIALOG_MOVE',
    DIALOG_END: 'DIALOG_END',
    OPEN_SUB_TYPE: 'OPEN_SUB_TYPE',
    CLOSE_SUB_TYPE: 'CLOSE_SUB_TYPE',
    ITEM_TOGGLE: 'ITEM_TOGGLE',
    ITEM_PLAY: 'ITEM_PLAY',
    ITEM_LIKE: 'ITEM_LIKE',
    ITEM_STOP: 'ITEM_STOP',
    NAV: 'NAV',
    GO_USERINFO: 'GO_USERINFO',
    SHOW_SHARE: 'SHOW_SHARE',
    UPDATE_ITEM: 'UPDATE_ITEM',
    CURRENT_ITEM: 'CURRENT_ITEM',
    REMOVE_MUTED: 'REMOVE_MUTED',
    HIDE_MUTED_NOTICE: 'HIDE_MUTED_NOTICE',
    TOGGLE_CURRENT_VIDEO: 'TOGGLE_CURRENT_VIDEO',
    SHOW_AUDIO_CALL: 'SHOW_AUDIO_CALL',
    TOOL_CLICK_BROADCAST: 'TOOL_CLICK_BROADCAST',
    RECIEVE_MESSAGE: 'RECIEVE_MESSAGE',
    KICK_OUT: 'KICK_OUT',
    MESSAGE_UPDATE: 'MESSAGE_UPDATE',
    LOGOUT: 'LOGOUT',
    PAYMENT: 'PAYMENT',
    JACKPOT_VIEW_ENTER: 'JACKPOT_VIEW_ENTER',
    JACKPOT_VIEW_EXIT: 'JACKPOT_VIEW_EXIT',
}
