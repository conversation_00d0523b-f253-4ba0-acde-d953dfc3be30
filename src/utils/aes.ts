import CryptoJS from 'crypto-js'

export default {
    key: '3dfe30508ab4a03043f014a6684034ff',
    iv: 'fe3480b3543dytj3',
    /**
     * 设置加密密钥
     * @param pure
     */
    setPureKey(pure: string) {
        this._pureKey = pure
        let ret = this._decryptByPure(this._pureKey)
        let keyAndIv = ret.split(':')
        this._encryptKey = keyAndIv[0]
        this._encryptIv = keyAndIv[1]
    },

    /**
     * 是否存在加密密钥
     * @returns
     */
    isPureKey(): boolean {
        return this._pureKey != null
    },

    /**
     * 加密
     * @param data
     * @returns
     */
    aesEncrypt(data: string | Object) {
        if (typeof data !== 'string') {
            data = JSON.stringify(data)
        }
        const key = CryptoJS.enc.Utf8.parse(this._encryptKey)
        const iv = CryptoJS.enc.Utf8.parse(this._encryptIv)
        const encrypt = CryptoJS.AES.encrypt(data, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7,
        })
        return encrypt.toString()
    },

    /**
     * 解密
     * @param data
     * @returns
     */
    _decryptByPure(data: string) {
        const key = CryptoJS.enc.Utf8.parse(this.key)
        const iv = CryptoJS.enc.Utf8.parse(this.iv)
        const decrypted = CryptoJS.AES.decrypt(data, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7,
        })
        return decrypted.toString(CryptoJS.enc.Utf8)
    },
}
