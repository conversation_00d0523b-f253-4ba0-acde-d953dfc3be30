const postcssPresetEnv = require('postcss-preset-env')
const px2Rem = require('postcss-pxtorem')

module.exports = {
    //模块暴露
    plugins: [
        require('tailwindcss'),
        // require('autoprefixer'),
        postcssPresetEnv({
            minimumVendorImplementations: 0,
            autoprefixer: true,
            stage: 3,
            features: {
                'gap-properties': true,
            },
        }),
        px2Rem({
            //插件名称
            rootValue: 75, //设计稿375就设置37.5    设计稿750就设置75
            propList: ['*'], //配置所有字体大小
            replace: true,
            selectorBlackList: ['.load-preview', '.load-preview-img'], //过滤掉scss-开头的class，不进行rem转换
            exclude: /node_modules/,
            minPixelValue: 0,
        }),
    ],
}
