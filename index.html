<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, orientation=portrait, minimal-ui,viewport-fit=cover"
        />
        <meta name="theme-color" content="#232626" />
        <meta name="application-name" content="BetFugu" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="theme-color" content="#232626" />
        <meta property="og:url" content="https://betfugu.com/" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Get huge cash for free!" />
        <meta property="og:description" content="Absolutely fair, huge rewards, interesting games, happy life!!!" />
        <meta property="og:image" content="https://betfugu.com/static/icon.png" />
        <title>BetFugu</title>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon-precomposed" sizes="120x120" href="https://betfugu.com/static/icon.png" />

        <link rel="preconnect" href="https://connect.facebook.net" />
        <link rel="dns-prefetch" href="https://connect.facebook.net" />

        <link rel="preconnect" href="https://accounts.google.com" />
        <link rel="dns-prefetch" href="https://accounts.google.com" />

        <link rel="preconnect" href="https://h5server.betfuguapi.com" />
        <link rel="dns-prefetch" href="https://h5server.betfuguapi.com" />

        <link rel="preconnect" href="wss://acc.betfuguapi.com" />
        <link rel="dns-prefetch" href="wss://acc.betfuguapi.com" />

        <!-- <script disable-devtool-auto src="https://cdn.jsdelivr.net/npm/disable-devtool@latest"></script> -->
        <script>
            // 判断当前页面的协议是否为HTTP
            if (window.location.protocol === 'http:' && !window.location.host.includes('localhost') && window.location.host !== '**************') {
                // 如果是HTTP，则重定向到HTTPS
                window.location.href = 'https:' + window.location.href.substring(window.location.protocol.length)
            }
        </script>
        <style>
            html,
            body,
            #app {
                margin: 0;
                padding: 0;
                background: #232626 !important;
                height: 100vh;
                width: 100%;
                box-sizing: border-box;
                border: 0px;
            }
            .load-preview {
                position: absolute;
                box-sizing: border-box;

                padding: 24vh 20vw 0;
                width: 100vw;
                height: 100vh;
                background: #232626 !important;
                z-index: 8000;
            }
            .loading-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            .loading-logo {
                width: 53vw;
                position: absolute;
                bottom: 30vh;
                left: 50%;
                transform: translateX(-50%);
            }

            .loading-wrap {
                position: absolute;
                bottom: 23vh;
                left: 10vw;
                width: 80vw;
                height: 1vh;
                background: #373737;
                border-radius: 20rem;
            }
            .loading-inner {
                position: relative;
                width: 0;
                height: 100%;
                background: #25f4ee;
                border-radius: 20rem;
                transition: 0.1s ease;
            }
            .loading-inner svg {
                position: absolute;
                top: 50%;
                right: 0;
                width: 20px;
                height: 20px;
                transform: translate3d(50%, -50%, 0);
            }
            .loading-text {
                position: absolute;
                top: -50px;
                right: 0;
                font-size: 28px;
                font-weight: 400;
                color: #fff;
                font-family: 'Microsoft YaHei';
                transform: translateX(55%);
            }
            .loading-tip {
                position: absolute;
                bottom: 19vh;
                left: 10vw;
                width: 80vw;
                color: #788094;

                text-align: center;
                font-family: 'Microsoft YaHei';
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                text-align: center;
            }
            body {
                padding-top: env(safe-area-inset-top);
                background-color: #232626 !important;
            }
        </style>
    </head>
    <body>
        <div class="load-preview">
            <img class="loading-bg" src="./src/assets/img/loading/loading_bg.avif" />
            <img class="loading-logo" src="./src/assets/img/loading/LOGO.png" />
            <div class="loading-wrap">
                <div class="loading-inner">
                    <div class="loading-text">0%</div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <circle cx="10" cy="10" r="10" fill="url(#paint0_radial_473_85)" />
                        <defs>
                            <radialGradient
                                id="paint0_radial_473_85"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(10 10) rotate(90) scale(10)"
                            >
                                <stop offset="0.415" stop-color="white" />
                                <stop offset="1" stop-color="#B6FFAC" stop-opacity="0" />
                            </radialGradient>
                        </defs>
                    </svg>
                </div>
            </div>
            <div class="loading-tip"></div>
        </div>
        <div id="app"></div>
        <script type="module" src="/src/main.ts"></script>
        <script>
            var preview = document.querySelector('.load-preview')
            var inner = document.querySelector('.loading-inner')
            var innerText = document.querySelector('.loading-text')
            var loadingTip = document.querySelector('.loading-tip')
            var percent = 0
            var time = 5
            var timer = null
            var tipTimer = null
            var tipIdx = 0
            var tipList = [
                'The more you bet, the more you earn!',
                'Become VIP and receive daily bet return!',
                'Play more, win more — withdraw daily!',
                'Invite friends and get extra bonus rewards!',
                'Link your phone to protect your balance!',
                'Real cash, real fun — withdraw your winnings easily!',
                'Participate Jackpot to Win daily rewards.',
                'Make the first deposit to get 777! ',
            ]
            var tipText = tipList[0]
            function setPer(num) {
                var text = num + '%'
                inner.style.width = text
                innerText.innerHTML = text
            }
            function load() {
                percent = percent + 10
                setPer(percent)
                if (percent >= 100) {
                    timer && clearInterval(timer)
                    tipTimer && clearInterval(tipTimer)
                    return preview.remove()
                }
                timer = setTimeout(function () {
                    load()
                }, 300)
            }
            function getRandomTip() {
                return tipList[Math.floor(Math.random() * tipList.length)]
            }
            function changeText() {
                loadingTip.innerHTML = getRandomTip()
                tipTimer = setInterval(function () {
                    loadingTip.innerHTML = getRandomTip()
                }, 2000)
            }
            changeText()
            window.addEventListener('DOMContentLoaded', () => {
                setPer(percent)
                load()
            })
            window.addEventListener('DOMContentLoaded', () => {
                setPer(percent)
                load()
            })
            window.addEventListener('load', function () {
                if (percent >= 100) {
                    return
                }
                percent = 100
                timer && clearInterval(timer)
                setPer(percent)
            })
        </script>
    </body>
</html>
