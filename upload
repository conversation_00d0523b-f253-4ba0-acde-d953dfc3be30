#!/usr/bin/env node
const { program } = require('commander')
const fs = require('fs')
const path = require('path')
const async = require('async')
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3')
const mime = require('mime-types')

const bucket = 'betfugu.com'
var result = 0

program
    .command('test')
    .description('测试服!')
    .option('-o --output <path>', '热更文件输出目录', `${__dirname}/dist`)
    .action(async (options) => {
        const s3Client = new S3Client({
            region: 'ap-southeast-1',
            credentials: {
                accessKeyId: '********************',
                secretAccessKey: 'S4LGmsBqgJEmJScynpMRf8DEJWxm2jBoY9kph8SH',
            },
        })

        const files = fs.readdirSync(options.output)

        uploadqueue(options.output, 'testWeb', files, s3Client)
    })

program
    .command('pro')
    .description('正式服')
    .option('-o --output <path>', '热更文件输出目录', `${__dirname}/dist`)
    .action(async (options) => {
        const s3Client = new S3Client({
            region: 'ap-southeast-1',
            credentials: {
                accessKeyId: '********************',
                secretAccessKey: 'S4LGmsBqgJEmJScynpMRf8DEJWxm2jBoY9kph8SH',
            },
        })

        const files = fs.readdirSync(options.output)

        uploadqueue(options.output, '', files, s3Client)
    })

program
    .command('mac')
    .description('sifu')
    .option('-o --output <path>', '热更文件输出目录', `${__dirname}/dist`)
    .action(async (options) => {
        const s3Client = new S3Client({
            region: 'ap-southeast-1',
            credentials: {
                accessKeyId: '********************',
                secretAccessKey: 'S4LGmsBqgJEmJScynpMRf8DEJWxm2jBoY9kph8SH',
            },
        })

        const files = fs.readdirSync(options.output)

        uploadqueue(options.output, 'mactest', files, s3Client)
    })

program.parse(process.argv)

function addqueue(queue, workspace, env, f) {
    const stat = fs.statSync(path.join(workspace, f))
    if (stat.isFile()) {
        result.total += 1

        const objectKey = env ? path.join(env, f) : f
        const file = path.join(workspace, f)
        const ContentType = mime.contentType(f.split('/').pop())
        queue.push({ file: file, objectKey: objectKey, ContentType }, (err) => {
            if (err) {
                console.error(`Error uploading ${file}:`, err)
                result.failed += 1
            } else {
                result.success += 1
            }
            console.log(`上传进度：${result.success + result.failed}/${result.total}   文件：${objectKey}  `)
        })
    }

    if (stat.isDirectory()) {
        const files = fs.readdirSync(path.join(workspace, f))
        for (let i in files) {
            addqueue(queue, workspace, env, path.join(f, files[i]))
        }
    }
}

async function upload(workspace, env, f, client) {
    const stat = fs.statSync(path.join(workspace, f))
    if (stat.isFile()) {
        const objectKey = env ? path.join(env, f) : f
        const file = path.join(workspace, f)
        const ContentType = mime.contentType(f.split('/').pop())
        let okey = objectKey
        if (process.platform === 'win32') {
            okey = objectKey.replace(/\\/g, `/`)
        }

        const params = {
            Bucket: bucket,
            Key: okey,
            Body: fs.createReadStream(file),
            ContentType,
        }
        const command = new PutObjectCommand(params)

        try {
            await client
                .send(command)
                .then((data) => {
                    console.log(`上传成功: `, okey)
                })
                .catch((err) => {
                    console.log(err)
                })
        } catch (error) {
            console.error('上传失败:', error)
        }
    }

    if (stat.isDirectory()) {
        const files = fs.readdirSync(path.join(workspace, f))
        for (let i in files) {
            await upload(workspace, env, path.join(f, files[i]), client)
        }
    }
}

async function uploadqueue(workspace, env, files, client) {
    result = { failed: 0, success: 0, total: 0 }
    const st = new Date().getTime()
    const uploadQueue = async.queue((task, callback) => {
        let okey = task.objectKey
        if (process.platform === 'win32') {
            okey = task.objectKey.replace(/\\/g, `/`)
        }

        const fileStream = fs.createReadStream(task.file)
        var ContentType = task.ContentType ? task.ContentType : undefined
        const uploadParams = {
            Bucket: bucket,
            Key: okey,
            Body: fileStream,
            ContentType,
        }

        const command = new PutObjectCommand(uploadParams)

        try {
            client
                .send(command)
                .then(() => {
                    // console.log(`上传成功: `)
                    callback()
                })
                .catch((err) => {
                    console.log(err)
                })
        } catch (error) {
            console.error('上传失败:', error)
            callback(error)
        }
    }, 30)

    for (let i in files) {
        const nfiles = ['index.html', 'version.json']
        if (!nfiles.includes(files[i])) {
            addqueue(uploadQueue, workspace, env, files[i])
        }
    }

    uploadQueue.drain(async () => {
        uploadQueue.kill()
        const et = new Date().getTime()
        await upload(workspace, env, `index.html`, client)
        await upload(workspace, env, `version.json`, client)
        console.log(env + '上传成功,请启动游戏检测!, 耗时:' + formatTime(et - st) + '  https://usgameweb.com')
    })
}

function formatTime(milliseconds) {
    const totalSeconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60

    return `${minutes}分${seconds}秒`
}
