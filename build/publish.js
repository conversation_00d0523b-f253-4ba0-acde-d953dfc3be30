#!/usr/bin/env node
const { program } = require('commander')
const OSS = require('ali-oss-publish')
const { exec } = require('child_process')
const path = require('path')
const { prompt } = require('inquirer').default

const uploadFile = ({ mode }, isMaster = false) => {
    const bucket = mode === 'production' && isMaster ? 'h5-playtok-oss' : 'h5-vue-client'
    console.log(`正在发版，发版环境：${isMaster ? '线上环境' : '测试环境'} bucket: ${bucket}`)
    prompt([
        {
            type: 'confirm', // 指定类型为 confirm
            name: 'continue', // 指定答案的键名
            message: `正在发版，发版环境：${isMaster ? '线上环境' : '测试环境'} bucket: ${bucket}`, // 提示信息
            default: false, // 默认值为 true，表示默认情况下选项是选中的
        },
    ]).then((answers) => {
        if (answers.continue) {
            console.log(`用户执行发布操作, 环境: ${mode}, 路径: ${bucket}`)
            OSS(
                {
                    id: 'LTAI5t6Z52WwtVYPwixFaSWWfewf',
                    secret: 'HYrkB3vmQazok1rlGjKBHrx8uOwvkLR',
                    region: 'oss-ap-southeast-6',
                    bucket,
                    entry: path.join(__dirname, '../dist'),
                    output: './',
                    config: '',
                    retry: 1,
                    concurrency: 4,
                    force: false,
                },
                (err, status) => {
                    if (err) {
                        console.error('发布失败,请重新尝试', err)
                        process.exit(1)
                    }
                    if (status.hasProgress()) {
                        const { type, index, current, total, message } = status

                        console.log('[%s] [%s/%s] #%s: %s', type, current, total, index, message)
                        if (current === total) {
                            console.log('上传完成', current, total)
                        }
                    } else {
                        console.log(status.message)
                    }
                    if (status.hasWarnings()) {
                        console.warn('ali-oss-publish encountered some warnings.')
                        status.warnings.forEach((x) => {
                            console.warn(x)
                        })
                    }
                }
            )
        } else {
            console.log('用户取消执行操作')
        }
    })
}
program.option('-m --mode [mode]').action(async (options) => {
    if (options.mode !== 'development') {
        exec('git rev-parse --abbrev-ref HEAD', (error, stdout, stderr) => {
            if (error) {
                console.error(`执行 git 命令出错: ${error}`)
                return
            }
            if (stderr) {
                console.error(`git 命令错误输出: ${stderr}`)
                return
            }
            console.log(`当前 Git 分支名称: ${stdout.trim()}`)
            const isMaster = stdout.trim() === 'master'
            if (isMaster) {
                uploadFile(options, isMaster)
            } else {
                console.error('git分之错误，清检查分支，确保在master分支发布正式环境代码')
            }
        })
    } else {
        uploadFile(options)
    }
})

program.parse(process.argv)
