#!/usr/bin/env node
const { exec } = require('child_process')
const path = require('path')

DOMWEB_DIR = './domweb'
PWA_RELEASE_DIR = '../pwa_release'
PLAYTOKPWA_DIR = '../playtokpwa'
TARGET_PWA_SUBDIR = 'dist'
const BASE_URL = path.join(__dirname, '../')

const PLAYTOKPWA_GIT = {
    development: 'playtok/test',
    production: 'playtok/release',
}
const PWA_RELEASE_GIT = {
    development: 'test',
    production: 'release',
}

const copyFile = (mode) => {
    console.log(99999, mode, __dirname, __basename)
    // exec('git rev-parse --abbrev-ref HEAD', (error, stdout, stderr) => {})
}

copyFile('development')
