const fs = require('fs')
const path = require('path')
const XLSX = require('xlsx')

const makeFile = (reslut, basePath) => {
    Object.keys(reslut).forEach((key) => {
        const filepath = `${basePath}/${key}.json`
        // const con = 'export default ' + JSON.stringify(reslut[key])
        const con = JSON.stringify(reslut[key])
        fs.writeFile(filepath, con, (err) => {
            if (err) throw err
            console.log(`多语言文件${key}创建成功`)
        })
    })
}
const makeIndex = (reslut, basePath) => {
    const keys = Object.keys(reslut).filter((key) => key)
    console.log(keys, 'keyskeyskeyskeys')
    let con =
        keys.reduce((prev, key) => {
            return (
                prev +
                '\n' +
                `case '${key}':
            return import('./${key}.json')`
            )
        }, 'export function loadLocaleMessages(locale) {switch (locale) {') + '\n'

    con += ` default:
            return import('./en-US.json')}}`
    // let exportVar = '{'
    // keys.forEach((key) => {
    //     exportVar += `'${key}':${key.replace('-', '')},`
    // })
    // exportVar += '}'
    // con += `export default ${exportVar}`
    fs.writeFile(`${basePath}/index.js`, con, (err) => {
        if (err) throw err
        console.log(`多语言文件index创建成功`)
    })
}
function readFile(filePath) {
    const fileContent = XLSX.readFile(path.resolve(__dirname, '../src/language/多语言前端用.xlsx'))
    const name = fileContent.SheetNames[0] // 获取第一个sheet的名称
    const sheet = fileContent.Sheets[name]
    const jsonData = XLSX.utils.sheet_to_json(sheet)
    return jsonData
}
const basePath = path.resolve(__dirname, '../src/language/locales')
const main = (reslut) => {
    makeFile(reslut, basePath)
    makeIndex(reslut, basePath)
}

const mainLocal = () => {
    const content = readFile()
    const reslut = {}
    content.forEach((item) => {
        Object.keys(item)
            .filter((key) => key !== 'id')
            .forEach((key) => {
                if (!reslut[key]) {
                    reslut[key] = {}
                }
                const lang = reslut[key] || {}
                lang[item.id] = item[key]
            })
    })
    makeFile(reslut, basePath)
    makeIndex(reslut, basePath)
}

module.exports = {
    main,
    mainLocal,
}
