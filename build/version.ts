const inquirer = require('@inquirer/prompts')
// import { input, confirm } from '@inquirer/prompts'
const child_process = require('child_process')
const fs = require('fs')
const path = require('path')

const input = inquirer.input
// const confirm = inquirer.confirm

const packageName = 'package.json'

const versionRegex = /^\d+(\.\d+){0,2}$/

const exec = child_process.exec
function main() {
    exec('git rev-parse --abbrev-ref HEAD', async (error, stdout, stderr) => {
        const branch = stdout.trim()
        const isMaster = branch === 'master'
        let confirm = isMaster
        if (!isMaster) {
            confirm = await inquirer.confirm({ message: '测试环境是否需要更新版本号?' })
        }
        if (confirm) {
            inquirer
                .select({
                    type: 'list',
                    name: 'version',
                    message: '🚀🚀 请选择更新版本类型',
                    choices: [
                        { name: '小版本', value: 'patch' },
                        { name: '次要版本', value: 'minor' },
                        { name: '大版本', value: 'major' },
                        { name: '指定版本', value: 'appoint' },
                    ],
                })
                .then(async (answers) => {
                    if (answers === 'appoint') {
                        const answer = await input({ message: '请输入版本号' })
                        getLastCommitVersion(answer, isMaster, branch)
                    } else {
                        updatePackageJson(answers, isMaster, branch)
                    }
                })
        }
    })
}
async function createVersionFile(version, callback) {
    await fs.writeFile(
        path.join(__dirname, '../public/version.json'),
        JSON.stringify({ version: require(path.join(__dirname, '../', packageName)).version }),
        (err) => {
            if (err) {
                console.log('创建版本json失败')
                process.exit(1)
            }
            callback && callback()
        }
    )
}
// 更新package.json文件的版本号
function updatePackageJson(version, isMaster, branch) {
    var cmd = `npm version ${version}`
    if (!isMaster) {
        cmd += ' --no-git-tag-version'
    }
    exec(cmd, function (err) {
        if (err) {
            console.log(err)
            process.exit(1)
            return
        }
        createVersionFile(version, () => {
            gitAdd(() => {
                gitPublish(branch)
            })
        })
        // if (isMaster) {
        //     gitPublish()
        // }
    })
}
function gitAdd(calback) {
    exec('git add .', function (err) {
        if (err) {
            console.log(err)
            process.exit(1)
            return
        }
        exec('git commit -m "update version file"', function (err1) {
            if (err1) {
                console.log(err1)
                process.exit(1)
                return
            }
            calback && calback()
        })
    })
}
// // 将tag和变更代码提交到远端
function gitPublish(branch) {
    const cmd = `git push origin ${branch}`
    exec(cmd, function (err) {
        if (err) {
            console.log(err)
            process.exit(1)
            return
        }
        console.log(`推送远端${branch}成功`)
    })
}
// 获取上一次commit提交的版本号
function getLastCommitVersion(version, isMaster, branch) {
    if (!versionRegex.test(version)) {
        process.stdout.write('版本号格式错误，请检查版本号')
        process.exit(1)
    } else {
        const cmd = 'git describe --abbrev=0 --tags'
        exec(cmd, function (err, stdout) {
            if (err) {
                console.log(err, 'err')
                return
            }
            const prevVersion = stdout.replace('v', '')
            if (compareVersion(prevVersion, version) !== -1) {
                process.stdout.write(`版本号${version}需大于上次版本号${prevVersion}, 输入版本号错误，请重试`)
                process.exit(1)
            } else {
                updatePackageJson(version, isMaster, branch)
            }
        })
    }
}
// 比较版本号
function compareVersion(version1, version2) {
    // 将版本号字符串分割成数组
    const v1 = version1.split('.').map(Number)
    const v2 = version2.split('.').map(Number)

    // 找到较长的版本号长度
    const len = Math.max(v1.length, v2.length)

    // 逐位比较
    for (let i = 0; i < len; i++) {
        // 如果某一位不存在，则默认为0
        const num1 = i < v1.length ? v1[i] : 0
        const num2 = i < v2.length ? v2[i] : 0

        // 如果当前位的数字不同，则返回比较结果
        if (num1 > num2) {
            return 1
        } else if (num1 < num2) {
            return -1
        }
    }

    // 如果所有位都相同，则版本号相等
    return 0
}

main()
