#!/usr/bin/env node

const axios = require('axios')
const fileMethod = require('./file')
let token = ''
const spreadsheetToken = 'MP2YsirRjhlpRGt0xuHlNMt8grg'
const request = axios.create({
    baseURL: 'https://open.larksuite.com/open-apis',
    timeout: 10000,
    headers: { 'Content-Type': 'application/json; charset=utf-8' },
})

async function getToken() {
    return request({
        url: '/auth/v3/tenant_access_token/internal',
        method: 'post',
        data: {
            app_id: 'cli_a604e253ddf8502f',
            app_secret: 'pH26eBvg6fipqJFPv0saadMliUhklAdZ',
        },
    }).then((res) => res.data.tenant_access_token)
}
async function getSheetMeta() {
    // return request.get(`/sheets/v2/spreadsheets/${spreadsheetToken}/metainfo`).then((res) => res.data.data.sheets[0].sheetId)
    return request.get(`/sheets/v2/spreadsheets/${spreadsheetToken}/metainfo`).then((res) => res.data.data.sheets[4].sheetId)
}

async function getSheetContent(sheetId) {
    return request({
        url: `/sheets/v2/spreadsheets/${spreadsheetToken}/values_batch_get?ranges=${sheetId}`,
        method: 'get',
    }).then((res) => res.data.data.valueRanges)
}
function getText(item) {
    if (!item || !Array.isArray(item)) {
        return item
    }
    // if (!item || typeof item === 'string') {
    //     return
    // }
    return item.reduce((prev, cur) => {
        return prev + cur.text
    }, '')
}
function organizeData(list) {
    const title = list.shift()
    const result = {}
    title.forEach((name, index) => {
        if (name !== 'id' && name) {
            if (result[name]) {
                result[name] = {}
            }
            list.forEach((item) => {
                if (!result[name]) {
                    result[name] = {}
                }
                result[name][item[0]] = getText(item[index])
            })
        }
    })
    fileMethod.main(result)
}

async function main() {
    token = await getToken()
    const sheetId = await getSheetMeta()
    const result = await getSheetContent(sheetId)
    organizeData(result[0].values)
}

request.interceptors.request.use(
    function (config) {
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        // 在发送请求之前做些什么
        return config
    },
    function (error) {
        // 对请求错误做些什么
        return Promise.reject(error)
    }
)

main()
