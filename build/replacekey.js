#!/usr/bin/env node
const fs = require('fs')
const path = require('path')
const axios = require('axios')
const { toBase64 } = require('js-base64')

// 定义要搜索的目录
const srcDir = path.resolve(__dirname, '../src')

let token = ''
const spreadsheetToken = 'MP2YsirRjhlpRGt0xuHlNMt8grg'
const request = axios.create({
    baseURL: 'https://open.larksuite.com/open-apis',
    timeout: 10000,
    headers: { 'Content-Type': 'application/json; charset=utf-8' },
})

async function getToken() {
    return request({
        url: '/auth/v3/tenant_access_token/internal',
        method: 'post',
        data: {
            app_id: 'cli_a604e253ddf8502f',
            app_secret: 'pH26eBvg6fipqJFPv0saadMliUhklAdZ',
        },
    }).then((res) => res.data.tenant_access_token)
}
async function getSheetMeta() {
    return request.get(`/sheets/v2/spreadsheets/${spreadsheetToken}/metainfo`).then((res) => {
        const sheets = res.data.data.sheets
        return sheets[sheets.length - 1].sheetId
    })
}

async function getSheetContent(sheetId) {
    return request({
        url: `/sheets/v2/spreadsheets/${spreadsheetToken}/values_batch_get?ranges=${sheetId}`,
        method: 'get',
    }).then((res) => res.data.data.valueRanges)
}
request.interceptors.request.use(
    function (config) {
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        // 在发送请求之前做些什么
        return config
    },
    function (error) {
        // 对请求错误做些什么
        return Promise.reject(error)
    }
)

function organizeData(list) {
    const result = []
    list.shift()
    list.forEach((item) => {
        const [first, second] = item
        if (second && first && first !== second) {
            result.push({
                key: first,
                value: second,
            })
        }
    })
    result.sort((a, b) => toBase64(b.key).length - toBase64(a.key).length)
    return result
}
async function main() {
    token = await getToken()
    const sheetId = await getSheetMeta()
    const result = await getSheetContent(sheetId)
    return organizeData(result[0].values)
}
// 递归读取目录下的所有文件
async function readFiles(dir, result) {
    fs.readdir(dir, (err, files) => {
        if (err) {
            console.error(`无法读取目录: ${dir}`, err)
            return
        }

        files.forEach((file) => {
            const filePath = path.join(dir, file)
            fs.stat(filePath, (err, stats) => {
                if (err) {
                    console.error(`无法获取文件信息: ${filePath}`, err)
                    return
                }

                if (stats.isDirectory()) {
                    // 如果是目录，递归读取
                    readFiles(filePath, result)
                } else if (stats.isFile() && isTargetFile(filePath)) {
                    // 如果是文件，进行替换操作
                    replaceKeysInFile(filePath, result)
                }
            })
        })
    })
}
// 检查文件是否为目标类型
function isTargetFile(filePath) {
    const ext = path.extname(filePath)
    return ext === '.ts' || ext === '.js' || ext === '.vue'
}
// 在文件中替换键值
function replaceKeysInFile(filePath, result) {
    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
            console.error(`无法读取文件: ${filePath}`, err)
            return
        }

        let modifiedData = data
        result.forEach((item) => {
            const regex = new RegExp(`(['"\`])${item.key}\\1`, 'g')
            modifiedData = modifiedData.replace(regex, `$1${item.value}$1`)
        })

        if (modifiedData !== data) {
            fs.writeFile(filePath, modifiedData, 'utf8', (err) => {
                if (err) {
                    console.error(`无法写入文件: ${filePath}`, err)
                } else {
                    console.log(`文件已更新: ${filePath}`)
                }
            })
        }
    })
}
const init = async () => {
    const result = await main()
    console.log(result, 'result')
    readFiles(srcDir, result)
}
init()
