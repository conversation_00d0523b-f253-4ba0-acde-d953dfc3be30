import importToCDN from 'vite-plugin-cdn-import'

/**
 * 打包相关（测试）---没有使用 lzj
 * 注意 prodUrl：使用的是 jsdelivr 还是 unpkg。它们的 path 可能不一致
 * @description importToCDN https://github.com/mmf-fe/vite-plugin-cdn-import
 * @description cdn 在线引入的 cdn 地址配置。path：https://www.jsdelivr.com/ || https://unpkg.com/
 * @description external 打包时，过滤包导入。参考：https://rollupjs.org/configuration-options/#external
 */
export const buildConfig = {
    cdn() {
        return importToCDN({
            prodUrl: 'https://betfugu.com/static',
            modules: [
                {
                    name: 'vue',
                    var: 'Vue',
                    path: '/vue.3.4.35.js',
                },

                {
                    name: 'vue-router',
                    var: 'VueRouter',
                    path: '/vue-router.4.4.1.js',
                },
                {
                    name: 'pinia',
                    var: 'Pinia',
                    path: '/pinia.2.2.0.js',
                },
            ],
        })
    },
    external: ['vue', 'vue-router', 'pinia'],
}
