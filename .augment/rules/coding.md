---
type: 'always_apply'
---

# 通用编程规范

## 代码可读性

1. **命名规范**：

    - 变量和函数名应描述其目的或行为，不使用无意义名称
    - 使用驼峰命名法（camelCase）或特定语言的常规命名约定
    - 布尔变量通常以`is`、`has`、`can`、`should`等前缀开头
    - 常量使用全大写，单词间用下划线分隔
    - **名称应体现"目的"而非"细节"**：说明"为什么存在""做什么用"，而非"是什么类型"
    - **避免误导性名称**：不使用与含义相悖的词，不使用易混淆的缩写
    - **名称长度与作用域匹配**：作用域越小，名称可越短；作用域越大，名称应越具体

    a. **变量命名规则**：

    - **集合类**：使用复数形式，如`users`、`orders`、`tasksQueue`
    - **索引/位置类**：使用`index`、`position`等，如`currentIndex`、`startPosition`
    - **临时变量**：体现"临时存储"含义，如`tempValue`、`midResult`
    - **状态/类型变量**：使用表示"状态"或"类别"的词，如`status`、`type`、`mode`
    - **时间/日期变量**：包含`time`、`date`等词，如`createTime`、`expireDate`

    b. **函数/方法命名规则**：

    - **获取数据**：以`get`开头，如`getName()`、`getUserById()`
    - **设置数据**：以`set`开头，如`setAge()`、`setConfig()`
    - **判断/检查**：以`is`、`has`、`can`、`check`等开头，如`isValid()`、`hasPermission()`
    - **操作/执行**：以动词开头，如`save()`、`delete()`、`calculateTotal()`
    - **转换/处理**：以`to`、`from`、`parse`、`format`等开头，如`toString()`、`fromJson()`
    - **事件/回调**：以`on`开头，如`onClick()`、`onSuccess()`、`onError()`
    - **添加/移除**：以`add`、`remove`开头，如`addItem()`、`removeUser()`

2. **注释规范**：

    - **必须使用中文注释**：所有代码注释必须使用中文
    - 添加必要的注释解释"为什么"而非"是什么"
    - 复杂逻辑必须有注释说明
    - 保持注释与代码同步更新
    - **避免冗余注释**：不注释显而易见的代码
    - **注释复杂部分**：对算法、tricky 逻辑必须加注释，说明设计思路

    a. **文档注释规范**：

    - 每个公共函数/方法/类必须有文档注释
    - 包含功能描述、参数说明、返回值说明、异常说明（如适用）
    - 使用统一的文档注释格式（如 JSDoc、JavaDoc 等）
    - 所有文档注释内容必须使用中文

    ```javascript
    /**
     * 计算两点之间的距离
     * @param {Point} point1 - 第一个点的坐标
     * @param {Point} point2 - 第二个点的坐标
     * @returns {number} 两点之间的欧几里得距离
     * @throws {TypeError} 如果参数不是有效的Point对象
     */
    function calculateDistance(point1, point2) {
        if (!isValidPoint(point1) || !isValidPoint(point2)) {
            throw new TypeError('参数必须是有效的Point对象')
        }

        // 使用欧几里得距离公式
        const dx = point2.x - point1.x
        const dy = point2.y - point1.y
        return Math.sqrt(dx * dx + dy * dy)
    }
    ```

    b. **代码块注释规范**：

    - 对复杂代码块使用块注释说明其目的和工作原理
    - 块注释应放在代码块之前，与代码保持相同缩进级别
    - 所有块注释必须使用中文

    ```javascript
    function processData(data) {
        // 预处理：移除无效数据并标准化格式
        const validData = data.filter((item) => item !== null)
        const normalizedData = validData.map((item) => ({
            id: String(item.id),
            value: Number(item.value),
        }))

        // 分组处理：按ID分组并计算每组的平均值
        const groups = {}
        normalizedData.forEach((item) => {
            if (!groups[item.id]) {
                groups[item.id] = []
            }
            groups[item.id].push(item.value)
        })

        // 计算结果：每组的平均值
        const result = {}
        Object.keys(groups).forEach((id) => {
            const values = groups[id]
            const sum = values.reduce((acc, val) => acc + val, 0)
            result[id] = sum / values.length
        })

        return result
    }
    ```

    c. **行内注释规范**：

    - 行内注释应放在代码行的右侧或上方，简洁明了
    - 避免注释显而易见的操作
    - 所有行内注释必须使用中文

    ```javascript
    // 好的行内注释示例
    const offset = 32 // ASCII码中大小写字母的差值

    // 对于特殊魔法数字的解释
    const retryDelay = 1000 * 60 * 5 // 5分钟重试延迟

    // 对于不明显的条件判断的解释
    if (status === 409) {
        // 409表示资源冲突，需要特殊处理
        resolveConflict()
    }
    ```

    d. **TODO 注释规范**：

    - 使用统一格式标记待办事项（如 TODO、FIXME、HACK 等）
    - 包含具体行动项和（可选的）负责人
    - 定期审查和处理 TODO 注释
    - 所有 TODO 注释内容必须使用中文

3. **格式规范**：
    - 保持一致的缩进风格
    - 避免过长的行和嵌套层级
    - 在逻辑块之间添加适当空行
    - 相关代码应该组织在一起

## 代码结构

1. **函数设计**：

    - 一个函数只做一件事
    - 控制函数长度，通常不超过 30 行
    - 减少参数数量，通常不超过 3 个
    - **避免使用魔法数字**：不直接在代码中使用数字常量，应定义有意义的常量名
    - **函数参数不宜过多**：参数过多时考虑使用对象封装相关参数
    - **应返回一致的类型**：避免同一函数在不同分支返回不同类型（如有时返回 int，有时返回 None），否则调用时需额外处理类型判断，增加出错风险
    - **避免副作用**：函数应尽量"纯"：输入相同则输出相同，且不修改外部变量（除非是设计为"修改器"的函数，如 setXxx()）。例如，一个 calculateTotal()函数不应同时修改传入的订单对象

2. **模块化**：

    - 相关功能应组织在同一模块中
    - 明确模块的公共 API 和内部实现
    - 避免模块间的循环依赖
    - **模块接口保持稳定**：修改时避免破坏现有调用方式

3. **错误处理**：
    - 合理使用错误处理机制
    - 提供有意义的错误信息
    - 不忽略捕获的错误
    - 考虑异常情况和边缘情况
    - **明确处理所有错误**：不忽略异常（如 `try { ... } catch (Exception e) {}` 空捕获块会隐藏问题）
    - **错误信息应具体**：包含上下文，如"用户 ID=123 的订单查询失败"，而非笼统的"查询失败"
    - **使用异常而非错误码**：相比返回错误码（如-1 表示失败），抛出异常能更清晰地分离正常逻辑和错误处理，避免错误被忽略

## 语句与表达式规范

1. **避免复杂表达式**：

    - 拆分嵌套过深的条件判断（如超过 3 层 if-else），可通过提前返回、引入辅助变量或使用多态解决

    ```python
    # 改进前：嵌套过深
    if condition1:
        if condition2:
            if condition3:
                doSomething()

    # 改进后：使用卫语句(Guard Clauses)提前返回
    if not condition1:
        return
    if not condition2:
        return
    if not condition3:
        return
    doSomething()
    ```

    这种使用卫语句的模式可以减少嵌套层级，提高代码可读性，将异常情况的处理与主要逻辑分离。

2. **使用正向逻辑表达**：

    - 避免双重否定，让条件更易理解

    ```javascript
    // 差：if not (isValid and not isExpired)（双重否定，难理解）
    // 好：if not isValid or isExpired（正向逻辑）
    ```

3. **每个语句只做一件事**：
    - 避免一行内写多个语句
    - 拆分复杂赋值

## 条件逻辑

1. **默认值与空值处理**：

    - 为参数提供明确的默认值
    - 采用防御性编程处理空值和未定义值
    - 使用空值合并和可选链等模式简化代码
    - 避免将`null`和`undefined`作为有效返回值

2. **Switch 语句替代**：
    - 考虑使用对象字面量或 Map 替代大型 switch 语句
    - 提取多个 if/else if 为策略对象或映射表
    - 使用多态性替代基于类型的条件语句

## 安全实践

1. **输入验证**：

    - 所有用户输入必须验证
    - 实施适当的输入限制
    - 使用白名单而非黑名单策略

2. **敏感信息**：
    - 不存储或显示敏感信息
    - 不在 URL 中传递敏感数据
    - 不在日志中记录敏感信息

## 代码复用

1. **DRY 原则**：

    - 识别和消除重复代码
    - 提取通用逻辑到共享函数
    - 创建可复用组件和工具
    - **重复出现 3 次以上的逻辑应提炼为函数或工具类**：多次出现的相同或相似逻辑（如字符串处理、日期格式化），应提炼为工具函数或工具类，而非重复编写，修改时只需改一处

2. **抽象级别**：

    - 根据频率和用例设计抽象
    - 避免过度抽象和过早抽象
    - 确保抽象的一致性和直观性
    - **依赖具体不如依赖抽象**：函数参数、变量类型尽量使用抽象接口而非具体实现，增强灵活性（如用 List 而非 ArrayList）
    - **避免过度设计**：复用需适度，不要为"可能的未来需求"提前设计复杂的抽象层，应基于当前需求合理抽象（遵循"YAGNI 原则"：You Aren't Gonna Need It）

3. **防御性编程**：

    - 对外部输入（如函数参数、用户输入）进行校验，避免非法值导致崩溃

    ```python
    def calculate_price(quantity):
        if quantity < 0:
            raise ValueError("数量不能为负数")  # 提前校验非法输入
        return quantity * unit_price
    ```

## 代码评审标准

1. **功能性检查点**：

    - 代码是否正确实现需求
    - 是否考虑了边缘情况和错误场景
    - 是否提供适当的错误处理和失败恢复

2. **可读性检查点**：

    - 命名是否清晰表达意图
    - 复杂逻辑是否有恰当的注释
    - 代码结构是否便于理解

3. **可维护性检查点**：

    - 是否遵循项目的代码风格和约定
    - 是否有重复代码可以提取和复用
    - 是否有过度复杂的实现可以简化

4. **安全性检查点**：
    - 是否正确验证用户输入
    - 是否避免常见的安全漏洞
    - 敏感数据处理是否安全

## 文件结构与组织

1. **文件组织原则**：

    - 相关功能放在同一目录下
    - 文件命名应反映内容
    - 保持目录结构的一致性和可预测性

2. **文件大小限制**：
    - 单个文件不宜过大（通常不超过 300-500 行）
    - 当文件过大时，考虑拆分为多个专注的模块
    - 确保文件的功能具有内聚性
