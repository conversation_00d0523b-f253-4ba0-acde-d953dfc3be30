import vue from '@vitejs/plugin-vue'
import path from 'path'
import { defineConfig, loadEnv, ConfigEnv } from 'vite'
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus'
import viteCompression from 'vite-plugin-compression'
import { buildConfig } from './build/build'
import VueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import legacy from '@vitejs/plugin-legacy'
import pkJson from './package.json'
// import { visualizer } from 'rollup-plugin-visualizer'
// import { VitePWA } from 'vite-plugin-pwa'

const viteConfig = defineConfig((mode: ConfigEnv) => {
    const env = loadEnv(mode.mode, process.cwd())
    const isPro = mode.mode === 'production'
    return {
        plugins: [
            // visualizer({
            //     open: true,
            //     filename: 'chunk-stats.html',
            // }),
            // VitePWA({
            //     strategies: 'generateSW',
            //     srcDir: 'src',
            //     filename: 'sw.js', // 自定义 Service Worker 文件路径
            //     registerType: 'autoUpdate',
            //     workbox: {
            //         // '**/*.{png,jpg,svg,webp,avi}'
            //         globPatterns: [], // 缓存所有静态资源
            //         runtimeCaching: [
            //             {
            //                 // urlPattern: /\/images\/.*\.(png|jpg|jpeg|svg|webp|avi)/, // 匹配图片路径
            //                 // urlPattern: /\.(?:png|jpg|jpeg|svg|webp|avi)$/i, // 严格匹配图片扩展名
            //                 urlPattern: /\/.*\.(png|jpg|jpeg|svg|webp|avi)(\?.*)?$/i,
            //                 handler: 'CacheFirst',
            //                 options: {
            //                     cacheName: 'image-cache',
            //                     expiration: {
            //                         // maxEntries: 50, // 最多缓存 50 张图片
            //                         maxAgeSeconds: 30 * 24 * 60 * 60, // 30 天
            //                     },
            //                     cacheableResponse: {
            //                         statuses: [0, 200], // 缓存跨域和 opaque 响应
            //                     },
            //                 },
            //             },
            //         ],
            //     },
            // }),
            vue(),
            VueJsx(),
            vueSetupExtend(),
            viteCompression(),
            //...JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null,
            AutoImport({
                resolvers: [VantResolver()],
                imports: ['vue', 'vue-router'],
                dts: true,
            }),
            Components({
                resolvers: [VantResolver()],
            }),
            legacy({
                // targets: ['defaults', 'not IE 11', 'chromeAndroid>=52, iOS>=13.1'],
                targets: ['> 0.2%', 'not dead'],
                modernPolyfills: true,
                additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
                renderLegacyChunks: true,
            }),
        ],
        root: process.cwd(),
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
            },
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
        },
        base: mode.command === 'serve' ? `./?_v=${Date.now()}` : env.VITE_PUBLIC_PATH,
        optimizeDeps: { exclude: [] },
        server: {
            host: '0.0.0.0',
            port: env.VITE_PORT as unknown as number,
            open: JSON.parse(env.VITE_OPEN),
            hmr: true,
            proxy: {},
            https: false,
            allowedHosts: ['6005b07b87a4.ngrok-free.app'],
        },
        build: {
            sourcemap: true,
            outDir: 'dist',
            cssCodeSplit: true,
            chunkSizeWarningLimit: 1500,
            assetsInlineLimit: 1024 * 6, //5kb以下图片转成base64 减少图片加载次数 减轻带宽压力，不过base64相对图片会变大 打包体积优化
            rollupOptions: {
                output: {
                    chunkFileNames: 'assets/js/[name]-[hash].js',
                    entryFileNames: 'assets/js/[name]-[hash].js',
                    assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
                    experimentalMinChunkSize: 100 * 1024,
                    manualChunks(id, { getModuleInfo }) {
                        if (id.includes('node_modules')) {
                            // 将第三方依赖合并为单个 vendor 包（或按需分组）
                            const vueLib = ['vue', 'pinia', 'pinia-plugin-persistedstate', 'vue-router', 'axios', 'mitt']
                            const videoLib = ['@videojs/http-streaming', 'video.js']
                            const otherLib = ['swiper', '@twa-dev/sdk', 'crypto-js']
                            if (vueLib.some((lib) => id.includes(`node_modules/${lib}`))) {
                                return 'vue-lib'
                            }
                            if (id.includes(`node_modules/vant`)) {
                                return 'vant'
                            }

                            if (videoLib.some((lib) => id.includes(`node_modules/${lib}`))) {
                                return 'video-lib'
                            }
                            if (otherLib.some((lib) => id.includes(`node_modules/${lib}`))) {
                                return 'other'
                            }
                            if (id.includes('libphonenumber-js')) {
                                return 'libphonenumber-js'
                            }

                            const { moduleSize } = getModuleInfo(id)
                            if (moduleSize < 1024 * 10) {
                                // <10KB 的小文件
                                return 'vendor-small-libs'
                            }

                            return 'vendor'
                        }
                        // 动态检测复用模块（核心逻辑）
                        if (id.includes('src/components')) {
                            // 获取当前模块的依赖图信息
                            const { importers } = getModuleInfo(id)

                            if (importers.length >= 2) {
                                // const fileNameWithExt = id.split('/').pop() || id // 兼容无斜杠的路径
                                // const fileName = fileNameWithExt.split('.').shift() // 去掉扩展名（可选）
                                // console.log(fileName, 'aaa')
                                return 'share' // 自动归为公共模块
                            }
                        }
                        // 按功能模块分组（示例：按路由或模块路径）
                        if (id.includes('/src/views/')) {
                            const module = id.split('/src/views/')[1].split('/')[0]
                            return `views-${module}`
                        }
                        // 合并小工具函数
                        if (id.includes('/src/utils/')) {
                            return 'utils'
                        }
                    },
                },
                // ...(JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}),
            },
            minify: 'terser',
            terserOptions: {
                compress: {
                    drop_console: isPro, //lzj 正式服看log，稍后屏蔽  上线需修改
                    drop_debugger: isPro,
                },
            },
            // mode.mode === 'development'
        },
        css: {
            preprocessorOptions: { css: { charset: false }, scss: { charset: true, additionalData: `@use "./src/theme/globalVariable.scss";` } },
        },
        define: {
            // __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
            __APP_VERSION__: JSON.stringify(pkJson.version),
            // __NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
        },
    }
})

export default viteConfig
