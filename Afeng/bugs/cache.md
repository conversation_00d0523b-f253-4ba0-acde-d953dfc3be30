<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-13 14:42:15
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-13 14:42:16
 * @FilePath     : /.feng1/bugs/cache.md
 * @Description  : Service Worker 缓存导致的黑屏问题分析
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-13 14:42:15
-->

# Service Worker 缓存导致的黑屏问题分析

## Bug 现象

### 问题表现

1. [正常行为] 应用应该正常加载并显示内容
2. [异常行为] 应用页面呈现黑屏状态
3. [特殊情况] 控制台显示大量 "----- 直接返回-" 日志，表明 Service Worker 正在从缓存返回资源
4. [异常信息] 控制台出现错误: `GET http://localhost/node_modules/.vite/deps/vant_es_field_style_index.js?v=0bd4a043 net::ERR_ABORTED 504 (Outdated Optimize Dep)`

### 触发条件

- 环境：本地开发环境 (localhost)
- 操作路径：
  1. 启动应用
  2. 访问应用页面
- 复现率：100%
- 特殊说明：应用使用 Vite 作为开发服务器，同时启用了 Service Worker 进行资源缓存

### 影响程度

- 严重程度：高（应用完全无法使用）
- 用户体验：页面完全黑屏，无法与应用交互
- 业务影响：开发环境下无法正常开发和测试功能

## Bug 分析

### 问题根源

经过分析，问题出在 Service Worker 的缓存策略与 Vite 开发服务器的工作方式冲突导致：

1. **Service Worker 缓存机制**：查看 `_sw.js` 文件，发现 Service Worker 实现了一个缓存优先的策略：

```javascript
self.addEventListener('fetch', function (event) {
    event.respondWith(
        caches.match(event.request).then(function (response) {
            if (response) {
                if (response.status >= 400) {
                    console.log('%c----->= 400-  ', 'background-color:blue;font-size:12px;color:#fff')
                    caches.open(CACHE_NAME).then((cache) => cache.delete(event.request))
                } else {
                    console.log('%c----- 直接返回-  ', 'background-color:blue;font-size:12px;color:#fff')
                    return response
                }
            }
            // ... 其他代码
        })
    )
})
```

2. **Vite 开发服务器特性**：Vite 在开发模式下使用动态模块热替换（HMR）和按需编译，会频繁更新模块内容。每次代码修改后，模块的内容会变化，但 URL 可能保持不变或仅查询参数变化。

3. **冲突点**：
   - 当 Service Worker 缓存了 Vite 开发服务器提供的资源后，即使资源内容在 Vite 服务器上已更新，Service Worker 仍会返回旧的缓存版本
   - 错误信息 `ERR_ABORTED 504 (Outdated Optimize Dep)` 明确指出依赖项已过时，但请求被中止
   - 控制台中大量的 "----- 直接返回-" 日志表明 Service Worker 正在从缓存返回资源，而不是从 Vite 服务器获取最新版本

4. **代码问题**：
   - 虽然代码中有针对开发环境禁用缓存的逻辑，但该逻辑被注释掉了：

```javascript
// 检查是否为开发环境（localhost或127.0.0.1）
// if (self.location.hostname === 'localhost' || self.location.hostname === '127.0.0.1') {
//     // 开发环境下不使用缓存，直接从网络获取资源
//     event.respondWith(fetch(event.request))
//     return
// }
```

## 解决方案

### 短期修复

1. **启用开发环境检测代码**：取消注释开发环境检测的代码块，确保在本地开发时不使用 Service Worker 缓存：

```javascript
self.addEventListener('fetch', function (event) {
    // 检查是否为开发环境（localhost或127.0.0.1）
    if (self.location.hostname === 'localhost' || self.location.hostname === '127.0.0.1') {
        // 开发环境下不使用缓存，直接从网络获取资源
        event.respondWith(fetch(event.request))
        return
    }
    
    // 其余代码保持不变...
}
```

2. **清除现有缓存**：在浏览器开发者工具中手动清除应用的缓存：
   - 打开开发者工具 → Application → Storage → Clear Site Data
   - 或者在 Application → Cache Storage 中删除特定的缓存

### 中期改进

1. **添加版本控制机制**：在缓存键中加入更细粒度的版本控制，特别是针对 Vite 生成的资源：

```javascript
// 在资源 URL 中添加时间戳或内容哈希
const APP_NAME = 'BetFugu'
const APP_VER = '107'
const BUILD_TIME = new Date().getTime() // 或从构建过程中注入
const CACHE_NAME = APP_NAME + '-' + APP_VER + '-' + BUILD_TIME
```

2. **改进缓存策略**：针对不同类型的资源采用不同的缓存策略：

```javascript
// 对于可能频繁变化的资源，使用网络优先策略
if (url.includes('/node_modules/.vite/') || url.includes('?v=')) {
    // 对于 Vite 生成的依赖，先尝试网络请求，失败时才使用缓存
    return fetch(event.request)
        .catch(() => caches.match(event.request))
        .then(response => response || new Response('Resource not found', {status: 404}))
}
```

### 长期优化

1. **环境感知的 Service Worker 注册**：在主应用中根据环境动态决定是否注册 Service Worker：

```javascript
// 在主应用的 Service Worker 注册代码中
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    navigator.serviceWorker.register('/_sw.js')
        .then(registration => console.log('SW registered:', registration))
        .catch(error => console.log('SW registration failed:', error))
} else if ('serviceWorker' in navigator) {
    // 在开发环境中注销任何已注册的 Service Worker
    navigator.serviceWorker.getRegistrations().then(registrations => {
        for (let registration of registrations) {
            registration.unregister()
        }
    })
}
```

2. **集成 Workbox**：考虑使用 Workbox 库来管理 Service Worker，它提供了更完善的缓存策略和与构建工具的集成：

```javascript
// 使用 Workbox 的示例
importScripts('https://storage.googleapis.com/workbox-cdn/releases/6.4.1/workbox-sw.js')

workbox.setConfig({ debug: false })

workbox.routing.registerRoute(
    ({request}) => request.destination === 'style' || request.destination === 'script',
    new workbox.strategies.StaleWhileRevalidate({
        cacheName: 'static-resources',
    })
)

// 对于 API 请求使用网络优先策略
workbox.routing.registerRoute(
    ({url}) => url.pathname.startsWith('/api/'),
    new workbox.strategies.NetworkFirst({
        cacheName: 'api-responses',
        networkTimeoutSeconds: 10,
    })
)
```

通过实施上述解决方案，特别是启用开发环境检测代码，应用将能够正常工作，同时在生产环境中仍保持 Service Worker 的缓存优势。
