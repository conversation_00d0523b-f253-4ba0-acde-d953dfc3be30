<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-12 11:20:08
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:47:13

 * @FilePath     : /Afeng/project-rules.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-12 11:20:08
-->
# BetFugu 项目开发规则

## 🏗️ 技术栈规范

### 核心技术栈

- **前端框架**: Vue 3 (Composition API优先)
- **语言**: TypeScript (严格模式)
- **构建工具**: Vite 4.x
- **状态管理**: Pinia (替代Vuex)
- **路由**: Vue Router 4
- **UI组件库**: Vant 4 (移动端UI)
- **CSS预处理器**: SCSS/Sass
- **包管理**: 推荐使用cnpm或yarn

### 支持的浏览器

- Chrome >= 87
- Firefox >= 78
- Safari >= 13
- Edge >= 88
- 移动端: iOS >= 10, Android >= 5

## 📁 项目结构规范

```
src/
├── api/          # API接口定义
├── assets/       # 静态资源
├── components/   # 通用组件
├── directive/    # 自定义指令
├── enums/        # 枚举定义
├── hooks/        # 组合式函数
├── language/     # 国际化文件
├── plugins/      # 插件配置
├── router/       # 路由配置
├── stores/       # Pinia状态管理

├── theme/        # 主题样式
├── types/        # TypeScript类型定义
├── utils/        # 工具函数
├── views/        # 页面组件
└── mock/         # 模拟数据
```

## 🎯 编码规范

### Vue组件规范

- 优先使用`<script setup>`语法
- 组件命名使用PascalCase (如: `UserProfile.vue`)

- 组件文件应包含单一职责
- 使用vueSetupExtend插件支持组件name属性

```vue
<script setup lang="ts" name="ComponentName">
// 组件逻辑

</script>
```

### TypeScript规范

- 严格的类型检查，禁用`any`类型（特殊情况除外）
- 接口命名使用I前缀或描述性命名
- 类型定义统一放在`src/types/`目录

- 使用泛型提高代码复用性

### CSS/SCSS规范

- 使用SCSS作为样式预处理器
- 全局变量定义在`src/theme/globalVariable.scss`
- 遵循BEM命名规范
- 移动端适配使用rem单位（已配置pxtorem）
- 行宽限制150字符，缩进4个空格

### 命名规范

- **文件名**: kebab-case (如: `user-profile.vue`)
- **变量名**: camelCase (如: `userName`)
- **常量名**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **函数名**: camelCase (如: `getUserInfo`)
- **类名**: PascalCase (如: `UserService`)

## 🔧 开发工具配置

### ESLint规则

- 继承Vue 3推荐配置
- TypeScript支持
- 禁用console.log（生产环境）
- 未使用变量检查
- 单引号字符串

### Prettier格式化

- 行宽: 150字符
- 缩进: 4个空格
- 无分号结尾
- 单引号字符串
- 尾随逗号: es5模式

## 📦 依赖管理规范

### 生产依赖

- Vue生态系统组件优先选择官方维护的包
- UI组件统一使用Vant，避免引入其他UI库
- 状态管理统一使用Pinia

- 网络请求统一使用axios

### 开发依赖

- 构建工具以Vite生态为主
- 代码质量工具: ESLint + Prettier + TypeScript
- 测试工具: Vitest（如需要）

## 🚀 构建与部署规范

### 构建配置

- 开发环境: `npm run dev`

- 测试构建: `npm run test`
- 生产构建: `npm run build:release`
- 代码分割策略已优化（按功能模块和依赖大小）

### 环境变量

- 使用`.env`文件管理环境配置
- 环境变量以`VITE_`前缀命名
- 敏感信息不得提交到代码库

### 性能优化

- 图片小于6KB自动转base64
- 第三方依赖按大小和功能分组打包
- 启用gzip压缩
- 支持Legacy浏览器（可选）

## 🌐 国际化规范

### 多语言支持

- 使用vue-i18n进行国际化
- 语言文件统一放在`src/language/`目录
- 所有用户可见文本必须国际化

- 键名使用点号分层 (如: `common.button.save`)

## 📱 移动端开发规范

### 响应式设计

- 基础设计稿: 375px宽度
- 使用flexible适配方案
- 关键断点: 320px, 375px, 414px, 768px

### Touch事件

- 优先使用Vant组件的内置手势
- 自定义手势使用@vueuse/gesture
- 避免300ms点击延迟

### 性能优化

- 图片懒加载
- 路由懒加载
- 组件按需引入
- 使用虚拟列表处理长列表

## 🔐 安全规范

### 数据安全

- 所有API请求使用HTTPS
- 敏感数据加密存储（crypto-js）
- 用户输入数据验证和过滤

- 避免XSS和CSRF攻击

### 隐私保护

- 遵循GDPR等隐私法规
- 最小化数据收集原则

- 用户数据加密传输和存储

## 🧪 测试规范

### 单元测试

- 使用Vitest作为测试框架

- 测试覆盖率目标: 80%以上
- 关键业务逻辑必须有测试

### E2E测试

- 使用Playwright或Cypress

- 核心用户流程必须有E2E测试

## 📋 代码提交规范

### Git Commit

- 使用Conventional Commits规范
- 格式: `type(scope): description`
- 类型: feat, fix, docs, style, refactor, test, chore

### 分支管理

- main: 生产环境分支
- develop: 开发环境分支
- feature/*: 功能开发分支
- hotfix/*: 紧急修复分支

## 🔍 代码审查规范

### PR要求

- 代码必须通过ESLint检查
- 必须有适当的测试覆盖
- 功能需要有文档说明
- 至少一人审查通过

### 审查重点

- 代码质量和可维护性
- 性能影响
- 安全风险

- 设计模式的合理性

## 📊 监控与日志

### 错误监控

- 集成Sentry进行错误追踪
- 生产环境禁用console.log

- 关键操作需要日志记录

### 性能监控

- 页面加载时间监控
- API响应时间监控
- 用户行为分析

## 🔄 持续集成

### CI/CD流程

- 代码提交触发自动构建
- 自动运行测试套件
- 通过后自动部署到对应环境
- 部署失败自动回滚

---

*此规则文档会根据项目发展持续更新，请团队成员定期查阅最新版本。*
