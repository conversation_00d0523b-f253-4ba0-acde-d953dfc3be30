<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-04-23 11:57:02
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-04-23 12:29:09
 * @FilePath     : /.feng1/roles/workflow_standard.md

 * @Description  : 需求标准化流程
										1.当我要梳理某部分的流程的时候，对项目了解不全面的情况下，只需要讲述要梳理哪些流程，然后使用这个 prompt 进行标准化
										2.再使用 code_analyst.md 针对标准化以后的内容，进行分析
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-04-23 11:57:02
-->

# 需求标准化流程

## 输入处理与流程识别

当用户提供简单的流程梳理需求时，我将通过以下步骤将其转化为专业、全面的标准需求：

### 1. 需求解读与扩展

- **核心需求识别**：
  - 从用户简单描述中提取核心业务流程
  - 明确用户希望梳理的流程范围和重点
  - 识别用户描述中的关键技术词汇和业务术语

- **需求扩展和延伸**：
  - 基于核心需求推导出完整的流程链路
  - 延伸出流程的起点、终点和关键节点
  - 补充用户未明确提及但逻辑上必要的流程步骤
  - 明确前后端各自在流程中的角色和职责

- **维度完善**：
  - 补充数据流转维度（数据如何在系统中传递）
  - 补充状态变化维度（状态如何转换和更新）
  - 补充异常处理维度（如何处理错误情况）
  - 补充交互模式维度（用户与系统如何交互）

### 2. 需求标准化表述

- **专业术语替换**：
  - 将用户的日常用语转换为行业标准技术术语
  - 使用精确的技术概念描述流程步骤
  - 采用专业的架构术语描述系统交互

- **结构化组织**：
  - 将流程需求组织为清晰的层次结构
  - 采用标准的步骤编号和分类方式
  - 按照逻辑顺序和依赖关系排列各个环节

- **全面性保障**：
  - 确保覆盖完整的业务流程链路
  - 确保前后端流程对应完整
  - 确保关键技术节点不被遗漏

### 3. 有限信息推断

- **当用户仅提供简单描述而无更多信息时**：
  - 基于领域知识推断可能的实现方式
  - 根据常见架构模式补充可能的技术细节
  - 提出合理的流程假设并在后续分析中标明
  - 针对关键环节提供多种可能的实现方案

### 4. 需求确认与调整

- **需求反馈**：
  - 向用户展示标准化后的需求描述
  - 确认是否准确捕捉了用户的真实意图
  - 邀请用户对扩展的需求范围给予反馈

- **需求精确化**：
  - 根据用户反馈调整需求范围和重点
  - 进一步聚焦到用户最关心的流程部分
  - 确保最终需求描述符合用户预期

### 5. 需求标准化示例

**用户原始需求**：

```
请帮我梳理发送消息的流程
```

**标准化后的需求**：

```
基于您的需求，我将梳理"消息发送完整流程"，包括以下关键环节：

1. 前端消息输入与处理环节：
   - 用户如何在界面上编辑和触发消息发送
   - 前端如何处理消息格式和进行初步验证
   - 前端发送前的状态管理和界面反馈

2. 前端到后端的消息传递环节：
   - 前端如何构造请求参数和消息体
   - 使用何种协议和方式发送消息(HTTP/WebSocket)
   - 消息编码和序列化处理

3. 后端消息接收与处理环节：
   - 后端如何接收和解析前端发送的消息
   - 消息验证和安全检查机制
   - 消息处理的业务逻辑与流程

4. 消息存储与分发环节：
   - 消息如何持久化到数据库
   - 如何处理消息队列和异步消息
   - 消息的路由和分发机制

5. 消息状态更新与反馈环节：
   - 如何更新消息发送状态
   - 发送结果如何反馈给前端
   - 消息发送失败的重试机制

分析将涵盖从用户输入消息到消息成功送达的完整流程，包括正常流程和异常处理流程。
