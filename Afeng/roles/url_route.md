# 前端开发 URL 解析规则

## 基本原则

当我输入一个地址时，比如 `http://localhost:3000/user/detail/1`， 应该：

1. 解析 URL 路径部分：`/user/detail/1`
2. 根据不同的前端框架规范和项目结构， 查找相应的 route,并定位到对应的前端代码文件
3. 提供文件位置和相关代码
4. 如果存在多个可能的匹配，提供选项让我选择

## URL 解析与代码定位

1. 自动识别项目使用的前端框架（React、Vue、Angular 等）
2. 分析项目的路由结构（如路由配置文件或基于文件系统的路由）
3. 将 URL 路径映射到对应的组件文件

## 意图识别规则

当我提出与 URL 相关的需求时， 应该能识别以下意图类型：

1. **代码定位**：我想找到实现特定 URL 的代码
2. **功能实现**：我想在特定路由中实现某些功能
3. **问题排查**：我在特定路由页面遇到了问题
4. **重构优化**：我想重构或优化特定路由的实现

## 上下文感知

 应该根据当前项目上下文理解我的需求：

1. 理解当前正在查看的文件与 URL 路径的关系
2. 记住之前讨论过的 URL 和相关组件
3. 根据项目结构推断不同 URL 之间的关系

# 通用前端开发规则

## 组件开发规则

当我提到特定组件或描述 UI 元素时， 应该：

1. **组件识别**：根据描述识别我正在谈论的是哪个组件
2. **状态管理**：根据组件行为推断其状态管理方式
3. **属性分析**：理解组件的 props/attributes 及其数据流向
4. **生命周期**：了解组件在不同生命周期阶段的行为

## 样式处理规则

当我描述样式或布局需求时， 应该：

1. **样式技术识别**：区分 CSS、SCSS/LESS、CSS-in-JS、Tailwind 等不同样式技术
2. **响应式设计**：理解媒体查询和响应式布局相关需求
3. **主题系统**：识别与主题、暗色模式等相关的样式变量
4. **动画效果**：理解过渡、动画等效果实现方式

## 数据交互规则

当我描述数据处理需求时， 应该：

1. **API 调用**：识别与后端 API 交互的请求模式（REST、GraphQL 等）
2. **状态管理**：区分本地状态、全局状态管理的使用场景
3. **数据流**：理解单向数据流、响应式数据等模式
4. **异步处理**：识别 Promise、async/await、响应式编程等异步处理方式

## 参数处理

### 动态路由参数

- 识别 URL 中的动态参数（如 `/user/1` 中的 `1`）
- 支持多级动态参数（如 `/org/:orgId/project/:projectId`）
- 可选参数处理（如 `/user/:id?`）

### 查询字符串参数

- 解析查询参数（如 `?filter=active&sort=desc`）
- 多值参数处理（如 `?tags[]=js&tags[]=react`）
- 查询参数与组件状态的映射关系

### 哈希路由处理

- 识别哈希路由模式（如 `/#/user/profile`）
- 哈希参数解析（如 `/#/search?q=keyword`）
- 锚点定位处理（如 `#section-1`）

## 框架特定配置

### Vue Router

```js
// 路由配置示例
const routes = [
  {
    path: '/user/:id',
    component: UserDetail,
    props: true
  }
]
```

### React Router

```jsx
// 路由配置示例
<Route path="/user/:id" element={<UserDetail />} />
```

### Angular Router

```typescript
// 路由配置示例
const routes: Routes = [
  { path: 'user/:id', component: UserDetailComponent }
];
```

## 路由守卫与中间件

1. **前置守卫**：路由跳转前的权限验证、数据预加载
2. **后置守卫**：路由跳转后的数据统计、页面标题更新
3. **解析守卫**：组件内数据解析和转换
4. **错误处理**：路由匹配失败、权限不足的统一处理

## 使用示例

当我提供一个 URL 如 `/products/detail/5` 时， 应该：

1. 自动识别项目框架
2. 找到实现该路由的组件文件
3. 显示相关代码实现
4. 如有需要，解释动态参数的处理方式

如果我提供的 URL 信息不完整，请询问更多上下文以便准确定位代码文件。
