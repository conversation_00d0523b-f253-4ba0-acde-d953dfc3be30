# UI 设计分析结果 - 供应商选择弹窗

## 分析概述

本文档包含对供应商选择弹窗 UI 设计图的详细分析结果，包括布局结构、元素组成、样式特征和技术实现方案。

## 设计图信息

-   **设计图名称**: 供应商选择弹窗 (ProviderSheet)
-   **分析时间**: 2025-07-30
-   **目标平台**: Web 移动端
-   **组件类型**: 多选弹窗组件

## 🔍 布局方向分析

-   **主要布局方向**：垂直布局
-   **判断依据**：设计图显示的是一个弹窗界面，从上到下依次排列：标题栏、供应商列表项，每个列表项垂直排列
-   **主要区域划分**：
    1. 顶部标题栏区域（包含"Select"标题、"Clear All"按钮、关闭按钮）
    2. 供应商列表区域（垂直滚动的供应商选项列表）

## 整体布局结构

```
主容器布局方式: 垂直布局 (flex-direction: column)
├── 标题栏区域: 水平布局 - 固定高度 (~80px)
└── 供应商列表区域: 垂直布局 - 可滚动区域 (flex: 1)
```

## 区域分析

### 🔲 标题栏区域

**元素组成**:

-   左侧标题文字: "Select"
-   右侧操作按钮组: "Clear All" 文字按钮 + "×" 关闭图标按钮

**布局方式**: `display: flex; justify-content: space-between; align-items: center`
**排列方式**: 水平排列，左右两端对齐，垂直居中
**关键样式**: 深色背景 (#2c3031)，顶部圆角 (12px 12px 0 0)

#### 标题文字 "Select"

-   **位置**: 左侧
-   **样式特征**:
    -   颜色: 白色 (#ffffff)
    -   字体: 32px，粗体 (font-weight: 600)

#### 操作按钮组

-   **位置**: 右侧，水平排列 (gap: 16px)
-   **Clear All 按钮**:
    -   颜色: 白色文字 (#ffffff)
    -   字体: 16px，常规字重
    -   交互: 点击清除所有选择
    -   悬停效果: 透明度变化
-   **关闭按钮**:
    -   图标: × 符号 (cross)
    -   背景: 深灰色圆角背景 (#404647)
    -   尺寸: 56px × 56px
    -   圆角: 8px
    -   悬停效果: 透明度 0.8

### 🔲 供应商列表区域

**元素组成**:

-   多个供应商选项项，每项包含：
    -   左侧复选框
    -   中间供应商 logo 图片
    -   右侧游戏数量文字

**布局方式**: `display: flex; flex-direction: column`
**排列方式**: 垂直排列，每个选项等高
**关键样式**: 深色背景 (#2c3031)，可滚动，底部内边距 40px

#### 供应商选项项详细分析

**选项项容器**:

-   **布局**: `display: flex; align-items: center; justify-content: space-between`
-   **高度**: 固定高度 100px
-   **内边距**: 左右 24px，上下 20px
-   **背景**: 深色 (#2c3031)
-   **交互状态**:
    -   悬停: 轻微背景色变化 (rgba(255, 255, 255, 0.03))
    -   选中: 背景色稍亮 (#363838)

**复选框**:

-   **位置**: 每行左侧
-   **尺寸**: 24px × 24px
-   **状态样式**:
    -   未选中: 灰色边框方框 (#666666)
    -   选中: 绿色背景 (#00d4aa) + 白色勾选图标
-   **圆角**: 4px

**供应商 Logo**:

-   **位置**: 复选框右侧，margin-left: 16px
-   **尺寸**: 最大宽度 120px，高度自适应
-   **样式**: 保持原始比例，object-fit: contain

**游戏数量文字**:

-   **位置**: 每行右侧
-   **格式**: "XXX Games"
-   **样式特征**:
    -   颜色: 白色 (#ffffff)
    -   字体: 16px，常规字重
    -   对齐: 右对齐

## 样式规范

### 颜色方案

-   **主背景色**: #2c3031 (深灰色)
-   **选中背景色**: #363838 (稍亮的深灰色)
-   **主题色**: #00d4aa (青绿色，用于选中状态)
-   **文字颜色**: #ffffff (白色)
-   **按钮背景**: #404647 (深灰色)
-   **悬停效果**: rgba(255, 255, 255, 0.03)

### 字体系统

-   **标题字体**: 32px, font-weight: 600
-   **按钮字体**: 16px, font-weight: 400
-   **内容字体**: 16px, font-weight: 400
-   **字体族**: 系统默认字体栈

### 间距系统

-   **容器内边距**: 24px (左右)
-   **元素间距**: 16px
-   **选项高度**: 100px
-   **圆角**: 12px (容器), 8px (按钮), 4px (复选框)

## 技术实现要点

### 布局技术选择

-   **主容器**: Flexbox 垂直布局
-   **标题栏**: Flexbox 水平布局，两端对齐
-   **列表项**: Flexbox 水平布局，space-between
-   **滚动**: overflow-y: auto

### 关键技术点

1. **多选功能**: 使用数组管理选中状态
2. **全选/清空**: 提供批量操作功能
3. **虚拟滚动**: 如果供应商数量很多，考虑虚拟滚动优化
4. **响应式**: 移动端适配，触摸友好的交互区域

### 交互状态管理

-   **选中状态**: 通过数组包含检查
-   **悬停效果**: CSS :hover 伪类
-   **点击反馈**: 背景色变化 + 复选框状态切换

## 实现建议

### 组件结构

-   基于 SelectSheet 组件扩展
-   支持多选模式
-   提供 Clear All 功能
-   支持供应商 logo 显示

### 数据结构

```typescript
interface Provider {
    id: string
    name: string
    logo: string
    gameCount: number
    selected?: boolean
}
```

### 性能优化

-   图片懒加载
-   防抖处理选择操作
-   虚拟滚动（如果数据量大）

### 可访问性

-   键盘导航支持
-   屏幕阅读器友好
-   合适的焦点管理
