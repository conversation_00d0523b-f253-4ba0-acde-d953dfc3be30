{"componentName": "ProviderSheet", "containerStyles": {"display": "flex", "flexDirection": "column", "backgroundColor": "#2c3031", "borderRadius": "12px 12px 0 0"}, "layout": {"type": "flex", "direction": "column", "areas": [{"name": "headerArea", "className": "sheet-header", "layout": "flex-horizontal-space-between", "elements": [{"type": "text", "name": "title", "className": "sheet-title", "content": "Select", "styles": {"fontSize": "32px", "fontWeight": "600", "color": "#ffffff"}}, {"type": "container", "name": "actionButtons", "className": "action-buttons", "styles": {"display": "flex", "alignItems": "center", "gap": "16px"}, "elements": [{"type": "button", "name": "clearAllButton", "className": "clear-all-btn", "content": "Clear All", "styles": {"fontSize": "16px", "color": "#ffffff", "backgroundColor": "transparent", "border": "none", "cursor": "pointer"}}, {"type": "button", "name": "closeButton", "className": "sheet-close", "styles": {"width": "56px", "height": "56px", "backgroundColor": "#404647", "borderRadius": "8px", "border": "none", "color": "#ffffff", "fontSize": "20px", "cursor": "pointer", "display": "flex", "alignItems": "center", "justifyContent": "center"}}]}]}, {"name": "providerListArea", "className": "provider-list", "layout": "flex-vertical", "styles": {"flex": "1", "overflowY": "auto", "paddingBottom": "40px"}, "elements": [{"type": "container", "name": "providerItem", "className": "provider-item", "styles": {"display": "flex", "alignItems": "center", "justifyContent": "space-between", "height": "100px", "padding": "20px 24px", "backgroundColor": "#2c3031", "cursor": "pointer"}, "elements": [{"type": "container", "name": "leftContent", "className": "left-content", "styles": {"display": "flex", "alignItems": "center", "gap": "16px"}, "elements": [{"type": "checkbox", "name": "checkbox", "className": "provider-checkbox", "styles": {"width": "24px", "height": "24px", "borderRadius": "4px", "border": "2px solid #666666", "backgroundColor": "transparent"}}, {"type": "image", "name": "providerLogo", "className": "provider-logo", "src": "provider-logo-url", "alt": "Provider <PERSON>", "styles": {"maxWidth": "120px", "height": "auto", "objectFit": "contain"}}]}, {"type": "text", "name": "gameCount", "className": "game-count", "content": "XXX Games", "styles": {"fontSize": "16px", "color": "#ffffff", "textAlign": "right"}}]}]}]}, "colors": {"primary": "#00d4aa", "text": "#ffffff", "background": "#2c3031", "backgroundSecondary": "#363838", "border": "#666666", "accent": "#404647"}, "interactions": {"hover": {"providerItem": {"backgroundColor": "rgba(255, 255, 255, 0.03)"}, "closeButton": {"opacity": "0.8"}, "clearAllButton": {"opacity": "0.8"}}, "selected": {"providerItem": {"backgroundColor": "#363838"}, "checkbox": {"backgroundColor": "#00d4aa", "borderColor": "#00d4aa"}}}}