<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-13 16:43:42
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-13 19:43:07
 * @FilePath     : /.feng1/login.md
 * @Description  : 登录页面UI改版方案
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-13 16:43:42
-->

# 登录页面UI改版方案

## 整体布局结构

### 1. 顶部区域

- 左上角放置logo图片 (`logo.png`)
- 顶部使用背景图 (`login_head_bg.png`)
- 保持原有的 <Back />, class="login-service" 还在左右两侧

 <div class="login"> 添加 background: #1c1b28 url:login_head_bg.png no-repeat left top/100%
- 右上角添加关闭按钮（X形状）

### 2. 中间内容区

Secure and Fast

- "Sign Up"和"Get ₱100 gift"文案放在顶部背景图上

<p data-v-9c47328c="" class="login-gifts">Get <span class="text-yellow">₱100 gift</span></p>
- "Sign In"文案作为输入区域的标题

### 3. 底部区域

- 用户协议复选框
- 分隔线
- 第三方登录选项
- 版本信息

## 具体样式调整

### 1. 颜色方案

- 使用深色背景（深蓝/深绿色调）
- 按钮使用鲜明的绿色
- 文字使用白色和浅灰色

### 2. 输入框样式

- 圆角边框
- 内部填充增加
- 更清晰的分隔线

### 3. 按钮样式

- 更大的圆角
- 渐变色背景
- 居中对齐

### 4. 其他元素

- 添加复选框组件
- 优化分隔线样式
- 调整第三方登录图标大小和间距

## 布局结构对比

**当前结构**：

```
- Back按钮
- 服务按钮
- Logo
- 输入区域（手机号+验证码）
- 登录按钮
- 分隔线
- 第三方登录
- 底部协议文本
- 版本信息
```

**新结构**：

```
- 顶部区域（Logo + 背景图 + 关闭按钮）
- Sign Up/Gift文案（在背景图上）
- Sign In标题
- 输入区域（手机号+验证码）
- 登录按钮
- 协议复选框
- 分隔线
- 第三方登录
- 版本信息
```

## 技术实现要点

1. **CSS样式更新**
   - 使用flex布局优化各元素位置
   - 添加适当的阴影和过渡效果
   - 确保移动端响应式设计

2. **组件调整**
   - 添加复选框组件
   - 更新按钮组件样式
   - 优化输入框组件

3. **图片资源**
   - 使用现有的`logo.png`和`login_head_bg.png`
   - 可能需要添加关闭按钮图标

## 注意事项

- 保持所有功能逻辑不变
- 保持所有文案内容不变
- 只调整UI样式和布局
- 确保在各种屏幕尺寸下的良好显示效果
